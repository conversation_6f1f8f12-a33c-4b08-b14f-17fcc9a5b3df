<?php

require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/admin_functions.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$employeeId = sanitize_input($_POST['employee_id'] ?? '');
$employeeName = sanitize_input($_POST['employee_name'] ?? '');
$employeeEmail = sanitize_input($_POST['employee_email'] ?? '');
$employeePhone = sanitize_input($_POST['employee_phone'] ?? '');
$serviceIds = $_POST['service_ids'] ?? [];
$workingDays = $_POST['working_days'] ?? [];

if (!$employeeName) {
    echo json_encode(['success' => false, 'message' => 'Employee name is required']);
    exit;
}

if (empty($serviceIds)) {
    echo json_encode(['success' => false, 'message' => 'At least one service must be selected']);
    exit;
}

// Process working hours
$workingHours = [];
$days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

foreach ($days as $day) {
    if (in_array($day, $workingDays)) {
        $dayPeriods = $_POST[$day] ?? [];
        $periods = [];

        foreach ($dayPeriods as $period) {
            if (!empty($period['start']) && !empty($period['end'])) {
                $periods[] = [
                    'start' => sanitize_input($period['start']),
                    'end' => sanitize_input($period['end'])
                ];
            }
        }

        if (!empty($periods)) {
            $workingHours[$day] = $periods;
        }
    }
}

// Sanitize service IDs
$serviceIds = array_map('sanitize_input', $serviceIds);

if ($employeeId) {
    // Update existing
    $result = updateEmployee($employeeId, $employeeName, $employeeEmail, $employeePhone, $workingHours, $serviceIds);
    $message = 'Employee updated successfully';
} else {
    // Add new
    $employeeId = generate_employee_id();
    $result = addEmployee($employeeId, $employeeName, $employeeEmail, $employeePhone, $workingHours, $serviceIds);
    $message = 'Employee added successfully';
}

// Add debugging
log_activity("Admin attempting to save employee: $employeeName, Services: " . implode(',', $serviceIds) . ", Working days: " . implode(',', $workingDays));

if ($result) {
    log_activity("Employee save success: $employeeName");
    echo json_encode(['success' => true, 'message' => $message, 'refresh' => true]);
} else {
    log_activity("Employee save failed: $employeeName");
    echo json_encode(['success' => false, 'message' => 'Failed to save employee']);
}
