<?php

/**
 * Styled Template Generator with Editable Placeholders
 * Keeps all the beautiful styling but only allows editing specific text areas
 */
class StyledTemplateGenerator
{
    private $templatesDir;
    private $creator;

    public function __construct()
    {
        $this->templatesDir = __DIR__ . '/templates';

        // Use the original EmailTemplateCreator for styling
        require_once __DIR__ . '/email_template_creator.php';
        $this->creator = new EmailTemplateCreator();

        // Create templates directory if it doesn't exist
        if (!is_dir($this->templatesDir)) {
            mkdir($this->templatesDir, 0755, true);
        }
    }

    /**
     * Generate all styled email templates with editable placeholders
     */
    public function generateAllTemplates()
    {
        echo "🚀 Generating styled email templates with editable placeholders...\n\n";

        $languages = ['en', 'el'];

        foreach ($languages as $lang) {
            echo "🌐 Language: " . strtoupper($lang) . "\n";

            $this->generateConfirmationTemplate($lang);
            $this->generateCancellationTemplate($lang);
            $this->generateReminderTemplate($lang);
            $this->generateWelcomeTemplate($lang);
            $this->generateVerificationTemplate($lang);

            echo "\n";
        }

        echo "✅ All styled templates with placeholders generated!\n";
    }

    /**
     * Generate confirmation email template with editable placeholders
     */
    private function generateConfirmationTemplate($language)
    {
        // Get editable texts first
        require_once __DIR__ . '/../unified_text_manager.php';
        $emailTexts = UnifiedTextManager::getEmailTexts();

        $content = $this->creator->generateGreeting($language, $emailTexts[$language]['confirmation_greeting']) . '

            ' . $this->creator->generateParagraph($language, $emailTexts[$language]['confirmation_message']) . '

            ' . $this->creator->generateReservationId($emailTexts[$language]['confirmation_id_label'] . ': {{reservation_id}}') . '

            ' . $this->creator->generateDetailSection($language, 'confirmation_details_title', '📋', [
            'field_service' => '{{service_name}}',
            'field_date' => '{{date}}',
            'field_time' => '{{time}}',
            'field_price' => '€{{service_price}}'
        ]) . '

            ' . $this->creator->generateParagraph($language, $emailTexts[$language]['confirmation_thanks']);

        $html = $this->creator->generateTemplate(
            'confirmation',
            $language,
            'confirmation_title',
            $emailTexts[$language]['confirmation_header_text'],
            $content
        );

        $filename = $language === 'en' ? 'confirmation.html' : 'confirmation_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate cancellation email template
     */
    private function generateCancellationTemplate($language)
    {
        // Get editable texts first
        require_once __DIR__ . '/../unified_text_manager.php';
        $emailTexts = UnifiedTextManager::getEmailTexts();

        $content = $this->creator->generateGreeting($language, $emailTexts[$language]['cancellation_greeting']) . '

            ' . $this->creator->generateParagraph($language, $emailTexts[$language]['cancellation_message']) . '

            ' . $this->creator->generateReservationId($emailTexts[$language]['cancellation_id_label'] . ': {{reservation_id}}') . '

            ' . $this->creator->generateDetailSection($language, 'cancellation_details_title', '📋', [
            'Service' => '{{service_name}}',
            'Date' => '{{date}}',
            'Time' => '{{time}}'
        ]) . '

            ' . $this->creator->generateParagraph($language, $emailTexts[$language]['cancellation_thanks']);

        $html = $this->creator->generateTemplate(
            'cancellation',
            $language,
            'cancellation_title',
            $emailTexts[$language]['cancellation_header_text'],
            $content
        );

        $filename = $language === 'en' ? 'cancellation.html' : 'cancellation_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate reminder email template
     */
    private function generateReminderTemplate($language)
    {
        // Get editable texts first
        require_once __DIR__ . '/../unified_text_manager.php';
        $emailTexts = UnifiedTextManager::getEmailTexts();

        $content = $this->creator->generateGreeting($language, $emailTexts[$language]['reminder_greeting']) . '

            ' . $this->creator->generateParagraph($language, $emailTexts[$language]['reminder_message']) . '

            ' . $this->creator->generateReservationId($emailTexts[$language]['reminder_id_label'] . ': {{reservation_id}}') . '

            ' . $this->creator->generateDetailSection($language, 'reminder_details_title', '📋', [
            'Service' => '{{service_name}}',
            'Date' => '{{date}}',
            'Time' => '{{time}}',
            'Duration' => '{{duration}} minutes',
            'Price' => '€{{price}}'
        ]) . '

            ' . $this->creator->generateParagraph($language, $emailTexts[$language]['reminder_thanks']);

        $html = $this->creator->generateTemplate(
            'reminder',
            $language,
            'reminder_title',
            $emailTexts[$language]['reminder_header_text'],
            $content
        );

        $filename = $language === 'en' ? 'reminder.html' : 'reminder_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate welcome email template
     */
    private function generateWelcomeTemplate($language)
    {
        // Get editable texts first
        require_once __DIR__ . '/../unified_text_manager.php';
        $emailTexts = UnifiedTextManager::getEmailTexts();

        $content = $this->creator->generateGreeting($language, $emailTexts[$language]['welcome_greeting']) . '

            ' . $this->creator->generateParagraph($language, $emailTexts[$language]['welcome_message']) . '

            ' . $this->creator->generateAccessCode(
            $language,
            'welcome_access_title',
            $emailTexts[$language]['welcome_access_info'],
            '{{user_hash}}',
            $emailTexts[$language]['welcome_thanks']
        );

        $html = $this->creator->generateTemplate(
            'welcome',
            $language,
            'welcome_title',
            $emailTexts[$language]['welcome_header_text'],
            $content
        );

        $filename = $language === 'en' ? 'welcome.html' : 'welcome_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate verification email template
     */
    private function generateVerificationTemplate($language)
    {
        // Get editable texts first
        require_once __DIR__ . '/../unified_text_manager.php';
        $emailTexts = UnifiedTextManager::getEmailTexts();

        $content = $this->creator->generateGreeting($language, $emailTexts[$language]['verification_greeting']) . '

            ' . $this->creator->generateParagraph($language, $emailTexts[$language]['verification_message']) . '

            ' . $this->creator->generateVerificationCode('{{code}}') . '

            ' . $this->creator->generateParagraph($language, $emailTexts[$language]['verification_thanks']);

        $html = $this->creator->generateTemplate(
            'verification',
            $language,
            'verification_title',
            $emailTexts[$language]['verification_header_text'],
            $content
        );

        $filename = $language === 'en' ? 'verification.html' : 'verification_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }



    /**
     * Save template to file
     */
    private function saveTemplate($filename, $html)
    {
        $filepath = $this->templatesDir . '/' . $filename;
        file_put_contents($filepath, $html);
    }
}
