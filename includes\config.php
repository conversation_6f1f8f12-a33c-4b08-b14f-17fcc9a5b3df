<?php

/**
 * Application configuration and constants
 */

// Core directories and files
define('DATA_DIR', __DIR__ . '/../data/');
define('DB_FILE', DATA_DIR . 'gk_booking.sqlite');
define('SETTINGS_FILE', __DIR__ . '/settings.php');

// Security and timing settings
define('SESSION_TIMEOUT', 1800); // 30 minutes
define('VERIFICATION_CODE_EXPIRY', 120); // 2 minutes
define('MAX_RESERVATIONS_PER_CUSTOMER', 5);

// Set timezone
date_default_timezone_set('Europe/Athens');

// Email configuration - will be set dynamically based on tenant context
define('SMTP_FROM_EMAIL', '<EMAIL>'); // Default fallback

// Ensure required directories exist
if (!file_exists(DATA_DIR)) {
    mkdir(DATA_DIR, 0755, true);
}

// Include database class
require_once __DIR__ . '/Database.php';
