<?php
/**
 * Test Admin Settings Loading
 * This script tests if the admin panel is loading the correct settings
 */

require_once 'includes/config.php';
require_once 'includes/tenant_init.php';
require_once 'includes/functions.php';

echo "<h1>Admin Settings Loading Test</h1>";
echo "<p>Current URL: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . $_SERVER['REQUEST_URI'] . "</p>";

// Test 1: Current tenant context
echo "<h2>1. Tenant Context</h2>";
try {
    require_once 'includes/TenantContext.php';
    $tenantId = TenantContext::getTenant();
    $tenantData = TenantContext::getTenantData();
    
    echo "✅ Tenant ID: " . ($tenantId ?? 'NULL') . "<br>";
    echo "✅ Tenant Data: " . json_encode($tenantData) . "<br>";
} catch (Exception $e) {
    echo "❌ Tenant context error: " . $e->getMessage() . "<br>";
}

// Test 2: Direct database check
echo "<h2>2. Direct Database Settings Check</h2>";
try {
    require_once 'includes/Database.php';
    require_once 'includes/TenantSettingsManager.php';
    
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    if ($tenantId) {
        $stmt = $conn->prepare("SELECT setting_key, setting_value FROM tenant_settings WHERE tenant_id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        
        $dbSettings = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $dbSettings[$row['setting_key']] = $row['setting_value'];
        }
        
        echo "✅ Database settings count: " . count($dbSettings) . "<br>";
        echo "📋 DB Site Name: " . htmlspecialchars($dbSettings['site_name'] ?? 'NOT SET') . "<br>";
        echo "📋 DB Admin Email: " . htmlspecialchars($dbSettings['admin_email'] ?? 'NOT SET') . "<br>";
        echo "📋 DB Email From: " . htmlspecialchars($dbSettings['email_from'] ?? 'NOT SET') . "<br>";
    } else {
        echo "❌ No tenant ID for database check<br>";
    }
} catch (Exception $e) {
    echo "❌ Database check error: " . $e->getMessage() . "<br>";
}

// Test 3: TenantSettingsManager::getAllSettings()
echo "<h2>3. TenantSettingsManager::getAllSettings()</h2>";
try {
    $managerSettings = TenantSettingsManager::getAllSettings();
    echo "✅ Manager settings count: " . count($managerSettings) . "<br>";
    echo "📋 Manager Site Name: " . htmlspecialchars($managerSettings['site_name'] ?? 'NOT SET') . "<br>";
    echo "📋 Manager Admin Email: " . htmlspecialchars($managerSettings['admin_email'] ?? 'NOT SET') . "<br>";
    echo "📋 Manager Email From: " . htmlspecialchars($managerSettings['email_from'] ?? 'NOT SET') . "<br>";
} catch (Exception $e) {
    echo "❌ TenantSettingsManager error: " . $e->getMessage() . "<br>";
}

// Test 4: getSettings() function
echo "<h2>4. getSettings() Function</h2>";
try {
    $functionSettings = getSettings();
    echo "✅ Function settings count: " . count($functionSettings) . "<br>";
    echo "📋 Function Site Name: " . htmlspecialchars($functionSettings['site_name'] ?? 'NOT SET') . "<br>";
    echo "📋 Function Admin Email: " . htmlspecialchars($functionSettings['admin_email'] ?? 'NOT SET') . "<br>";
    echo "📋 Function Email From: " . htmlspecialchars($functionSettings['email_from'] ?? 'NOT SET') . "<br>";
} catch (Exception $e) {
    echo "❌ getSettings() error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Test 5: Simulate admin/index.php settings loading
echo "<h2>5. Simulate Admin Panel Settings Loading</h2>";
try {
    // This is the exact code from admin/index.php
    $adminSettings = getSettings();
    echo "✅ Admin simulation settings loaded<br>";
    echo "📋 Admin Site Name: " . htmlspecialchars($adminSettings['site_name'] ?? 'NOT SET') . "<br>";
    
    // Test the exact HTML output
    $titleOutput = "Admin Dashboard - " . htmlspecialchars($adminSettings['site_name']);
    $sidebarOutput = '<h2><i class="fas fa-spa"></i> ' . htmlspecialchars($adminSettings['site_name']) . '</h2>';
    
    echo "✅ Title would be: '$titleOutput'<br>";
    echo "✅ Sidebar would be: '$sidebarOutput'<br>";
    
} catch (Exception $e) {
    echo "❌ Admin simulation error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Test 6: Check if settings are being cached or overridden
echo "<h2>6. Settings Consistency Check</h2>";
$allSiteNames = [
    'Database' => $dbSettings['site_name'] ?? 'NOT SET',
    'Manager' => $managerSettings['site_name'] ?? 'NOT SET', 
    'Function' => $functionSettings['site_name'] ?? 'NOT SET',
    'Admin' => $adminSettings['site_name'] ?? 'NOT SET'
];

echo "📋 All site names:<br>";
foreach ($allSiteNames as $source => $siteName) {
    echo "&nbsp;&nbsp;$source: " . htmlspecialchars($siteName) . "<br>";
}

$uniqueNames = array_unique(array_values($allSiteNames));
if (count($uniqueNames) === 1) {
    echo "✅ All sources return the same site name<br>";
} else {
    echo "❌ Different sources return different site names!<br>";
    echo "❌ This indicates a settings loading inconsistency<br>";
}

// Test 7: Check error logs
echo "<h2>7. Recent Error Log Check</h2>";
$errorLogFile = ini_get('error_log');
if ($errorLogFile && file_exists($errorLogFile)) {
    $logLines = file($errorLogFile);
    $recentLines = array_slice($logLines, -20); // Last 20 lines
    
    echo "📋 Recent error log entries:<br>";
    echo "<pre style='background: #f5f5f5; padding: 10px; font-size: 12px;'>";
    foreach ($recentLines as $line) {
        if (strpos($line, 'Settings') !== false || strpos($line, 'Tenant') !== false) {
            echo htmlspecialchars($line);
        }
    }
    echo "</pre>";
} else {
    echo "❌ Error log file not found or not accessible<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p><strong>Summary:</strong></p>";
echo "<ul>";
echo "<li>If all site names are the same and match your saved settings, the admin panel should display correctly</li>";
echo "<li>If they differ, there's a settings loading inconsistency</li>";
echo "<li>Check the error log for any settings-related errors</li>";
echo "</ul>";
?>
