<?php

/**
 * ========================================
 * EMAIL TEMPLATE SELECTOR
 * ========================================
 * 
 * Automatically selects the correct email template
 * based on customer language preferences.
 * 
 * Integrates with the existing EmailSender system
 * to provide multilingual email support.
 * 
 * <AUTHOR> System
 * @version 1.0
 */

class EmailTemplateSelector
{
    private $templatesDir;
    private $defaultLanguage = 'en';
    private $availableLanguages = ['en', 'el'];

    public function __construct()
    {
        $this->templatesDir = __DIR__ . '/templates/';
    }

    /**
     * Get the correct template file based on customer language
     * @param string $templateName - Base template name (e.g., 'confirmation', 'welcome')
     * @param string $customerLanguage - Customer's preferred language
     * @return string Full path to template file
     */
    public function getTemplateFile($templateName, $customerLanguage = null)
    {
        // Determine language to use
        $language = $this->determineLanguage($customerLanguage);

        // Build filename
        if ($language === 'en') {
            $filename = $templateName . '.html';
        } else {
            $filename = $templateName . '_' . $language . '.html';
        }

        $fullPath = $this->templatesDir . $filename;

        // Fallback to English if language-specific template doesn't exist
        if (!file_exists($fullPath)) {
            $fallbackPath = $this->templatesDir . $templateName . '.html';
            if (file_exists($fallbackPath)) {
                return $fallbackPath;
            }
        }

        return $fullPath;
    }

    /**
     * Get template content with language selection
     * @param string $templateName - Base template name
     * @param string $customerLanguage - Customer's preferred language
     * @return string Template content or false if not found
     */
    public function getTemplateContent($templateName, $customerLanguage = null)
    {
        // Determine language to use
        $language = $this->determineLanguage($customerLanguage);

        // Load content from database (UnifiedTextManager) instead of static files
        require_once __DIR__ . '/../unified_text_manager.php';
        $emailTexts = UnifiedTextManager::getEmailTexts();

        // Get HTML content from database
        $htmlKey = $templateName . '_html';
        if (isset($emailTexts[$language][$htmlKey])) {
            // Generate complete HTML with CSS styling
            require_once __DIR__ . '/html_template_generator.php';
            $generator = new HtmlTemplateGenerator();

            // Use reflection to access the private method
            $reflection = new ReflectionClass($generator);
            $method = $reflection->getMethod('generateCompleteHtml');
            $method->setAccessible(true);

            return $method->invoke($generator, $emailTexts[$language][$htmlKey], $templateName);
        }

        // Fallback to static template files if database content not found
        $templateFile = $this->getTemplateFile($templateName, $customerLanguage);
        if (file_exists($templateFile)) {
            return file_get_contents($templateFile);
        }

        return false;
    }

    /**
     * Determine which language to use based on customer preference
     * @param string $customerLanguage - Customer's language preference
     * @return string Language code to use
     */
    private function determineLanguage($customerLanguage)
    {
        // If no language specified, use default
        if (empty($customerLanguage)) {
            return $this->defaultLanguage;
        }

        // Normalize language code
        $language = strtolower(trim($customerLanguage));

        // Map common variations
        $languageMap = [
            'primary' => 'el',      // Main app primary language = Greek
            'secondary' => 'en',    // Main app secondary language = English
            'gr' => 'el',
            'greek' => 'el',
            'ελληνικά' => 'el',
            'english' => 'en',
            'en-us' => 'en',
            'en-gb' => 'en'
        ];

        if (isset($languageMap[$language])) {
            $language = $languageMap[$language];
        }

        // Check if language is available
        if (in_array($language, $this->availableLanguages)) {
            return $language;
        }

        // Fallback to default
        return $this->defaultLanguage;
    }

    /**
     * Get customer language from database or fallbacks
     * @param mixed $customerId - Customer ID (int or string)
     * @return string Customer's language preference
     */
    public function getCustomerLanguage($customerId = null)
    {
        // Try to get from customer record first (most reliable)
        if ($customerId) {
            try {
                require_once __DIR__ . '/../config.php';
                $db = Database::getInstance();
                $conn = $db->getConnection();

                $stmt = $conn->prepare("SELECT preferred_language FROM customers WHERE id = ?");
                $stmt->bindValue(1, $customerId, SQLITE3_TEXT);
                $result = $stmt->execute();
                $row = $result->fetchArray(SQLITE3_ASSOC);

                if ($row && !empty($row['preferred_language'])) {
                    return $row['preferred_language'];
                }
            } catch (Exception $e) {
                // Log error but continue with fallback
                error_log("Error getting customer language: " . $e->getMessage());
            }
        }

        // Try to get from session (email-specific language)
        if (isset($_SESSION['email_language'])) {
            return $_SESSION['email_language'];
        }

        // Try to get from main application language setting
        if (isset($_GET['lang'])) {
            $appLang = $_GET['lang'];
            // Convert application language to email language
            if ($appLang === 'primary') {
                return 'el'; // Greek
            } elseif ($appLang === 'secondary') {
                return 'en'; // English
            }
        }

        // Try to get from session language (if set by main app)
        if (isset($_SESSION['language'])) {
            return $this->determineLanguage($_SESSION['language']);
        }

        // Try to detect from browser language
        if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $browserLang = substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 2);
            return $this->determineLanguage($browserLang);
        }

        // Final fallback
        return $this->defaultLanguage;
    }

    /**
     * Get available languages
     * @return array Available language codes
     */
    public function getAvailableLanguages()
    {
        return $this->availableLanguages;
    }

    /**
     * Check if a specific template exists for a language
     * @param string $templateName - Template name
     * @param string $language - Language code
     * @return bool True if template exists
     */
    public function templateExists($templateName, $language)
    {
        $templateFile = $this->getTemplateFile($templateName, $language);
        return file_exists($templateFile);
    }

    /**
     * Get all available templates
     * @return array List of available template names
     */
    public function getAvailableTemplates()
    {
        $templates = [];
        $files = glob($this->templatesDir . '*.html');

        foreach ($files as $file) {
            $filename = basename($file, '.html');

            // Skip language-specific versions, only get base names
            if (!preg_match('/_[a-z]{2}$/', $filename)) {
                $templates[] = $filename;
            }
        }

        return array_unique($templates);
    }

    /**
     * Enhanced method for EmailSender integration
     * Returns template content with automatic language detection
     * @param string $templateName - Template name
     * @param mixed $customerId - Customer ID for language detection (int or string)
     * @param string $forceLanguage - Force specific language
     * @return string Template content
     */
    public function getLocalizedTemplate($templateName, $customerId = null, $forceLanguage = null)
    {
        $language = $forceLanguage ?: $this->getCustomerLanguage($customerId);
        return $this->getTemplateContent($templateName, $language);
    }
}

/**
 * ========================================
 * INTEGRATION HELPER FUNCTIONS
 * ========================================
 */

/**
 * Quick helper function to get localized email template
 * @param string $templateName - Template name
 * @param mixed $customerId - Customer ID (int or string)
 * @param string $language - Force specific language
 * @return string Template content
 */
function getLocalizedEmailTemplate($templateName, $customerId = null, $language = null)
{
    static $selector = null;

    if ($selector === null) {
        $selector = new EmailTemplateSelector();
    }

    return $selector->getLocalizedTemplate($templateName, $customerId, $language);
}

/**
 * Get customer's preferred language
 * @param mixed $customerId - Customer ID (int or string)
 * @return string Language code
 */
function getCustomerLanguagePreference($customerId = null)
{
    static $selector = null;

    if ($selector === null) {
        $selector = new EmailTemplateSelector();
    }

    return $selector->getCustomerLanguage($customerId);
}
