<?php
/**
 * Test Settings Flow - Complete settings save/load cycle
 */

require_once 'includes/config.php';
require_once 'includes/tenant_init.php';
require_once 'includes/functions.php';

echo "<h1>Settings Flow Test</h1>";

// Test 1: Current Settings
echo "<h2>1. Current Settings</h2>";
try {
    $currentSettings = getSettings();
    echo "✅ Current settings loaded<br>";
    echo "📋 Site Name: " . htmlspecialchars($currentSettings['site_name'] ?? 'NOT SET') . "<br>";
    echo "📋 Admin Email: " . htmlspecialchars($currentSettings['admin_email'] ?? 'NOT SET') . "<br>";
    echo "📋 Email From: " . htmlspecialchars($currentSettings['email_from'] ?? 'NOT SET') . "<br>";
} catch (Exception $e) {
    echo "❌ Error loading current settings: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Save New Settings
echo "<h2>2. Save New Settings Test</h2>";
$testSiteName = "Test Site " . date('H:i:s');
$testAdminEmail = "test" . time() . "@example.com";
$testEmailFrom = "from" . time() . "@example.com";

try {
    require_once 'includes/TenantSettingsManager.php';
    
    echo "🔍 Saving test site name: $testSiteName<br>";
    $result1 = TenantSettingsManager::setSetting('site_name', $testSiteName);
    echo "✅ Site name save result: " . ($result1 ? 'SUCCESS' : 'FAILED') . "<br>";
    
    echo "🔍 Saving test admin email: $testAdminEmail<br>";
    $result2 = TenantSettingsManager::setSetting('admin_email', $testAdminEmail);
    echo "✅ Admin email save result: " . ($result2 ? 'SUCCESS' : 'FAILED') . "<br>";
    
    echo "🔍 Saving test email from: $testEmailFrom<br>";
    $result3 = TenantSettingsManager::setSetting('email_from', $testEmailFrom);
    echo "✅ Email from save result: " . ($result3 ? 'SUCCESS' : 'FAILED') . "<br>";
    
} catch (Exception $e) {
    echo "❌ Error saving settings: " . $e->getMessage() . "<br>";
}

// Test 3: Reload Settings
echo "<h2>3. Reload Settings Test</h2>";
try {
    // Clear any cached settings by creating new instance
    $reloadedSettings = getSettings();
    echo "✅ Settings reloaded<br>";
    echo "📋 Site Name: " . htmlspecialchars($reloadedSettings['site_name'] ?? 'NOT SET') . "<br>";
    echo "📋 Admin Email: " . htmlspecialchars($reloadedSettings['admin_email'] ?? 'NOT SET') . "<br>";
    echo "📋 Email From: " . htmlspecialchars($reloadedSettings['email_from'] ?? 'NOT SET') . "<br>";
    
    // Check if values match
    $siteNameMatch = ($reloadedSettings['site_name'] ?? '') === $testSiteName;
    $adminEmailMatch = ($reloadedSettings['admin_email'] ?? '') === $testAdminEmail;
    $emailFromMatch = ($reloadedSettings['email_from'] ?? '') === $testEmailFrom;
    
    echo "✅ Site name matches: " . ($siteNameMatch ? 'YES' : 'NO') . "<br>";
    echo "✅ Admin email matches: " . ($adminEmailMatch ? 'YES' : 'NO') . "<br>";
    echo "✅ Email from matches: " . ($emailFromMatch ? 'YES' : 'NO') . "<br>";
    
} catch (Exception $e) {
    echo "❌ Error reloading settings: " . $e->getMessage() . "<br>";
}

// Test 4: Direct Database Check
echo "<h2>4. Direct Database Check</h2>";
try {
    require_once 'includes/Database.php';
    require_once 'includes/TenantContext.php';
    
    $db = Database::getInstance();
    $conn = $db->getConnection();
    $tenantId = TenantContext::getTenant();
    
    echo "🔍 Checking database for tenant: $tenantId<br>";
    
    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM tenant_settings WHERE tenant_id = :tenant_id");
    $stmt->bindValue(':tenant_id', $tenantId);
    $result = $stmt->execute();
    
    $dbSettings = [];
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $dbSettings[$row['setting_key']] = $row['setting_value'];
    }
    
    echo "✅ Database settings count: " . count($dbSettings) . "<br>";
    echo "📋 DB Site Name: " . htmlspecialchars($dbSettings['site_name'] ?? 'NOT SET') . "<br>";
    echo "📋 DB Admin Email: " . htmlspecialchars($dbSettings['admin_email'] ?? 'NOT SET') . "<br>";
    echo "📋 DB Email From: " . htmlspecialchars($dbSettings['email_from'] ?? 'NOT SET') . "<br>";
    
} catch (Exception $e) {
    echo "❌ Database check error: " . $e->getMessage() . "<br>";
}

// Test 5: Admin Panel Settings Load Test
echo "<h2>5. Admin Panel Settings Load Test</h2>";
try {
    // Simulate how admin/index.php loads settings
    $adminSettings = getSettings();
    echo "✅ Admin panel settings loaded<br>";
    echo "📋 Admin Site Name: " . htmlspecialchars($adminSettings['site_name'] ?? 'NOT SET') . "<br>";
    
    // Test the exact code used in admin/index.php sidebar
    $sidebarTitle = htmlspecialchars($adminSettings['site_name']);
    echo "✅ Sidebar title would be: '$sidebarTitle'<br>";
    
} catch (Exception $e) {
    echo "❌ Admin panel settings error: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p><strong>If settings are saving but not showing in admin panel:</strong></p>";
echo "<ul>";
echo "<li>Check if the values above match between save/reload/database/admin tests</li>";
echo "<li>If they match, the issue is in the admin panel refresh</li>";
echo "<li>If they don't match, the issue is in the save/load process</li>";
echo "</ul>";
?>
