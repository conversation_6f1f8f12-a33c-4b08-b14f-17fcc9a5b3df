<?php
/**
 * Test Database Creation
 * This script tests if the database can be created properly
 */

require_once 'includes/config.php';

echo "<h1>Database Creation Test</h1>";

// Test 1: Check DATA_DIR constant
echo "<h2>1. DATA_DIR Configuration</h2>";
echo "DATA_DIR constant: " . DATA_DIR . "<br>";
echo "DATA_DIR exists: " . (is_dir(DATA_DIR) ? 'Yes' : 'No') . "<br>";
echo "DATA_DIR writable: " . (is_writable(DATA_DIR) ? 'Yes' : 'No') . "<br>";

// Test 2: Check database path
$dbPath = DATA_DIR . 'gk_booking.sqlite';
echo "<h2>2. Database Path</h2>";
echo "Database path: " . $dbPath . "<br>";
echo "Database exists: " . (file_exists($dbPath) ? 'Yes' : 'No') . "<br>";

// Test 3: Try to create database manually
echo "<h2>3. Manual Database Creation Test</h2>";
try {
    // Remove existing database if it exists
    if (file_exists($dbPath)) {
        unlink($dbPath);
        echo "✅ Removed existing database<br>";
    }
    
    // Try to create SQLite3 database directly
    $testDb = new SQLite3($dbPath);
    $testDb->enableExceptions(true);
    
    // Test a simple query
    $testDb->exec("CREATE TABLE test_table (id INTEGER PRIMARY KEY, name TEXT)");
    $testDb->exec("INSERT INTO test_table (name) VALUES ('test')");
    
    $result = $testDb->query("SELECT COUNT(*) as count FROM test_table");
    $row = $result->fetchArray(SQLITE3_ASSOC);
    
    echo "✅ Database created successfully<br>";
    echo "✅ Test table created and data inserted<br>";
    echo "✅ Test query result: " . $row['count'] . " rows<br>";
    
    $testDb->close();
    
    // Check if file was created
    if (file_exists($dbPath)) {
        echo "✅ Database file exists after creation<br>";
        echo "✅ Database file size: " . filesize($dbPath) . " bytes<br>";
    } else {
        echo "❌ Database file does not exist after creation<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Manual database creation failed: " . $e->getMessage() . "<br>";
}

// Test 4: Try Database class
echo "<h2>4. Database Class Test</h2>";
try {
    // Remove existing database if it exists
    if (file_exists($dbPath)) {
        unlink($dbPath);
        echo "✅ Removed existing database for class test<br>";
    }
    
    require_once 'includes/Database.php';
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Test a simple query
    $conn->exec("SELECT 1");
    
    echo "✅ Database class instantiated successfully<br>";
    
    if (file_exists($dbPath)) {
        echo "✅ Database file created by Database class<br>";
        echo "✅ Database file size: " . filesize($dbPath) . " bytes<br>";
    } else {
        echo "❌ Database file not created by Database class<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database class test failed: " . $e->getMessage() . "<br>";
}

// Test 5: Directory permissions
echo "<h2>5. Directory Permissions</h2>";
$dataDir = dirname($dbPath);
echo "Data directory: " . $dataDir . "<br>";
echo "Directory exists: " . (is_dir($dataDir) ? 'Yes' : 'No') . "<br>";
echo "Directory readable: " . (is_readable($dataDir) ? 'Yes' : 'No') . "<br>";
echo "Directory writable: " . (is_writable($dataDir) ? 'Yes' : 'No') . "<br>";
echo "Directory executable: " . (is_executable($dataDir) ? 'Yes' : 'No') . "<br>";

if (function_exists('posix_getpwuid') && function_exists('posix_getgrgid')) {
    $stat = stat($dataDir);
    $owner = posix_getpwuid($stat['uid']);
    $group = posix_getgrgid($stat['gid']);
    echo "Directory owner: " . $owner['name'] . "<br>";
    echo "Directory group: " . $group['name'] . "<br>";
    echo "Directory permissions: " . substr(sprintf('%o', fileperms($dataDir)), -4) . "<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p>Check the results above to identify any database creation issues.</p>";
?>
