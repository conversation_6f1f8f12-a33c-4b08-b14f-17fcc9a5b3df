<?php
/**
 * Test Final Fixes for Tenant Issues
 * This script tests the ServiceHandler tenant awareness and settings display
 */

require_once 'includes/config.php';
require_once 'includes/Database.php';
require_once 'includes/TenantContext.php';
require_once 'includes/service_handler.php';
require_once 'includes/employee_handler.php';
require_once 'includes/functions.php';

echo "<h1>🔧 Final Fixes Test</h1>";
echo "<p>Current URL: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . $_SERVER['REQUEST_URI'] . "</p>";

// Test 1: Tenant Detection
echo "<h2>1. Tenant Detection</h2>";
try {
    if (TenantContext::initializeFromRequest()) {
        $tenantId = TenantContext::getTenant();
        $tenantData = TenantContext::getTenantData();
        
        echo "✅ Tenant ID: " . htmlspecialchars($tenantId ?? 'NULL') . "<br>";
        if ($tenantData) {
            echo "✅ Business: " . htmlspecialchars($tenantData['business_name'] ?? 'N/A') . "<br>";
            echo "✅ Subdomain: " . htmlspecialchars($tenantData['subdomain'] ?? 'N/A') . "<br>";
        }
    } else {
        echo "❌ Tenant detection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Tenant detection error: " . $e->getMessage() . "<br>";
}

// Test 2: ServiceHandler Tenant Awareness
echo "<h2>2. ServiceHandler Tenant Awareness</h2>";
try {
    $serviceHandler = new ServiceHandler();
    
    // Test getServiceCount
    $serviceCount = $serviceHandler->getServiceCount();
    echo "✅ Service count (tenant-aware): " . $serviceCount . "<br>";
    
    // Test getServicesPaginated
    $services = $serviceHandler->getServicesPaginated(10, 0);
    echo "✅ Services paginated (tenant-aware): " . count($services) . " services<br>";
    
    // Test search functionality
    $searchResults = $serviceHandler->searchServices('facial', 10, 0);
    echo "✅ Search results (tenant-aware): " . count($searchResults) . " services<br>";
    
    $searchCount = $serviceHandler->getSearchServiceCount('facial');
    echo "✅ Search count (tenant-aware): " . $searchCount . " services<br>";
    
    // Show service details if any exist
    if (!empty($services)) {
        echo "<strong>Sample services:</strong><br>";
        foreach (array_slice($services, 0, 3) as $service) {
            echo "&nbsp;&nbsp;- " . htmlspecialchars($service['name']) . " (ID: " . htmlspecialchars($service['id']) . ")<br>";
        }
    } else {
        echo "ℹ️ No services found for this tenant (expected for new tenant)<br>";
    }
} catch (Exception $e) {
    echo "❌ ServiceHandler error: " . $e->getMessage() . "<br>";
}

// Test 3: EmployeeHandler Tenant Awareness
echo "<h2>3. EmployeeHandler Tenant Awareness</h2>";
try {
    $employeeHandler = new EmployeeHandler();
    
    // Test getEmployeeCount
    $employeeCount = $employeeHandler->getEmployeeCount();
    echo "✅ Employee count (tenant-aware): " . $employeeCount . "<br>";
    
    // Test getEmployeesPaginated
    $employees = $employeeHandler->getEmployeesPaginated(10, 0);
    echo "✅ Employees paginated (tenant-aware): " . count($employees) . " employees<br>";
    
    // Show employee details if any exist
    if (!empty($employees)) {
        echo "<strong>Sample employees:</strong><br>";
        foreach (array_slice($employees, 0, 3) as $employee) {
            echo "&nbsp;&nbsp;- " . htmlspecialchars($employee['name']) . " (ID: " . htmlspecialchars($employee['id']) . ")<br>";
        }
    } else {
        echo "ℹ️ No employees found for this tenant (expected for new tenant)<br>";
    }
} catch (Exception $e) {
    echo "❌ EmployeeHandler error: " . $e->getMessage() . "<br>";
}

// Test 4: Settings Loading and Display
echo "<h2>4. Settings Loading and Display</h2>";
try {
    $settings = getSettings();
    
    echo "✅ Settings loaded successfully<br>";
    echo "✅ Site Name: " . htmlspecialchars($settings['site_name'] ?? 'N/A') . "<br>";
    echo "✅ Email From: " . htmlspecialchars($settings['email_from'] ?? 'N/A') . "<br>";
    echo "✅ Admin Email: " . htmlspecialchars($settings['admin_email'] ?? 'N/A') . "<br>";
    
    // Test if settings would appear in admin panel
    echo "<strong>Admin Panel Title Test:</strong><br>";
    echo "&nbsp;&nbsp;Title would be: 'Admin Dashboard - " . htmlspecialchars($settings['site_name']) . "'<br>";
    echo "&nbsp;&nbsp;Sidebar would show: '" . htmlspecialchars($settings['site_name']) . "'<br>";
    
} catch (Exception $e) {
    echo "❌ Settings error: " . $e->getMessage() . "<br>";
}

// Test 5: Database Data Verification
echo "<h2>5. Database Data Verification</h2>";
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Check services in database for current tenant
    if (isset($tenantId)) {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM services WHERE tenant_id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        echo "✅ Services in database for tenant: " . ($row['count'] ?? 0) . "<br>";
        
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM employees WHERE tenant_id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        echo "✅ Employees in database for tenant: " . ($row['count'] ?? 0) . "<br>";
        
        // Check settings in database
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tenant_settings WHERE tenant_id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        echo "✅ Settings in database for tenant: " . ($row['count'] ?? 0) . "<br>";
    }
    
    // Check total data (all tenants)
    $result = $conn->query("SELECT COUNT(*) as count FROM services");
    $row = $result->fetchArray(SQLITE3_ASSOC);
    echo "ℹ️ Total services (all tenants): " . ($row['count'] ?? 0) . "<br>";
    
    $result = $conn->query("SELECT COUNT(*) as count FROM employees");
    $row = $result->fetchArray(SQLITE3_ASSOC);
    echo "ℹ️ Total employees (all tenants): " . ($row['count'] ?? 0) . "<br>";
    
} catch (Exception $e) {
    echo "❌ Database verification error: " . $e->getMessage() . "<br>";
}

// Test 6: Create Sample Data for Testing (if tenant has no data)
if (isset($tenantId) && isset($serviceCount) && $serviceCount == 0) {
    echo "<h2>6. Create Sample Data for Testing</h2>";
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        
        // Create sample service
        $serviceId = 'SV-' . strtoupper(substr(md5(uniqid()), 0, 10));
        $stmt = $conn->prepare("
            INSERT INTO services (id, name, duration, price, description, tenant_id) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->bindValue(1, $serviceId);
        $stmt->bindValue(2, 'Relaxing Facial Treatment');
        $stmt->bindValue(3, 60);
        $stmt->bindValue(4, 75.00);
        $stmt->bindValue(5, 'Deep cleansing facial treatment for all skin types');
        $stmt->bindValue(6, $tenantId);
        
        if ($stmt->execute()) {
            echo "✅ Created sample service: " . htmlspecialchars($serviceId) . "<br>";
        }
        
        // Create sample employee
        $employeeId = 'EM-' . strtoupper(substr(md5(uniqid()), 0, 10));
        $stmt = $conn->prepare("
            INSERT INTO employees (id, name, email, phone, working_hours, status, created_at, tenant_id) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->bindValue(1, $employeeId);
        $stmt->bindValue(2, 'Maria Therapist');
        $stmt->bindValue(3, '<EMAIL>');
        $stmt->bindValue(4, '+30 123456789');
        $stmt->bindValue(5, json_encode(['monday' => ['09:00', '17:00'], 'tuesday' => ['09:00', '17:00']]));
        $stmt->bindValue(6, 'active');
        $stmt->bindValue(7, date('Y-m-d H:i:s'));
        $stmt->bindValue(8, $tenantId);
        
        if ($stmt->execute()) {
            echo "✅ Created sample employee: " . htmlspecialchars($employeeId) . "<br>";
        }
        
        echo "<p><strong>Now test the admin interface to see if data appears correctly!</strong></p>";
        
    } catch (Exception $e) {
        echo "❌ Sample data creation error: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>✅ Test Summary</h2>";
echo "<p><strong>Expected Results After Fixes:</strong></p>";
echo "<ul>";
echo "<li>✅ ServiceHandler methods are now tenant-aware</li>";
echo "<li>✅ realma.skrtz.gr should show proper service/employee counts</li>";
echo "<li>✅ Admin interface should show 'Showing X-Y of Z services' correctly</li>";
echo "<li>✅ Settings should display properly in admin panel title and sidebar</li>";
echo "<li>✅ Data isolation should work correctly between tenants</li>";
echo "</ul>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Test <strong>realma.skrtz.gr/admin/</strong> - should show correct counts</li>";
echo "<li>Test services page - should show proper pagination</li>";
echo "<li>Test employee page - should show proper pagination</li>";
echo "<li>Check admin panel title and sidebar - should show correct site name</li>";
echo "<li>Test employee/service creation - should work without errors</li>";
echo "</ol>";
?>
