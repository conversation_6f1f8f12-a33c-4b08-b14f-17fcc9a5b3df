<?php

/**
 * Database Connection Handler
 *
 * Singleton class for managing SQLite database connections
 * Handles table creation, optimization, and connection management
 *
 * <AUTHOR> System
 * @version 1.0
 */
class Database
{
    /** @var Database|null Singleton instance */
    private static $instance = null;

    /** @var SQLite3 Database connection */
    private $db;

    /**
     * Private constructor to enforce singleton pattern
     */
    private function __construct()
    {
        $dbPath = DATA_DIR . 'gk_booking.sqlite';
        $isNew = !file_exists($dbPath);

        try {
            // Ensure the directory exists and is writable
            $dataDir = dirname($dbPath);
            if (!is_dir($dataDir)) {
                if (!mkdir($dataDir, 0755, true)) {
                    throw new Exception("Cannot create data directory: $dataDir");
                }
            }

            if (!is_writable($dataDir)) {
                throw new Exception("Data directory is not writable: $dataDir");
            }

            $this->db = new SQLite3($dbPath);
            $this->db->enableExceptions(true);

            // Enable WAL mode for better performance
            $this->db->exec('PRAGMA journal_mode=WAL;');
            $this->db->exec('PRAGMA synchronous=NORMAL;');
            $this->db->exec('PRAGMA cache_size=10000;');
            $this->db->exec('PRAGMA temp_store=MEMORY;');

            // Create tables if this is a new database
            if ($isNew) {
                $this->initializeTables();
                $this->createDummyDataIfEnabled();
            }
        } catch (Exception $e) {
            error_log('Database connection failed: ' . $e->getMessage());
            error_log('Database path: ' . $dbPath);
            error_log('Data directory: ' . dirname($dbPath));
            error_log('Directory exists: ' . (is_dir(dirname($dbPath)) ? 'yes' : 'no'));
            error_log('Directory writable: ' . (is_writable(dirname($dbPath)) ? 'yes' : 'no'));
            throw new Exception('Database connection failed: ' . $e->getMessage());
        }
    }

    /**
     * Get singleton database instance
     * @return Database Database instance
     */
    public static function getInstance(): Database
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Reset singleton instance (for testing/clean setup)
     * @return void
     */
    public static function resetInstance(): void
    {
        if (self::$instance !== null && self::$instance->db !== null) {
            self::$instance->db->close();
        }
        self::$instance = null;
    }

    /**
     * Get raw SQLite3 connection
     * @return SQLite3 Database connection
     */
    public function getConnection(): SQLite3
    {
        return $this->db;
    }

    /**
     * Execute prepared query with parameters
     * @param string $sql SQL query with placeholders
     * @param array $params Parameters to bind
     * @return SQLite3Result Query result
     */
    public function query(string $sql, array $params = []): SQLite3Result
    {
        try {
            $stmt = $this->db->prepare($sql);

            foreach ($params as $param => $value) {
                $stmt->bindValue($param, $value);
            }

            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Database query error: {$e->getMessage()} | SQL: {$sql}");
            throw $e;
        }
    }

    /**
     * Run database migrations
     */
    private function runMigrations()
    {
        // Check if customers table exists first
        $result = $this->db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='customers'");
        $tableExists = $result->fetchArray() !== false;

        if ($tableExists) {
            // Check if preferred_language column exists in customers table
            $result = $this->db->query("PRAGMA table_info(customers)");
            $columns = [];
            while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                $columns[] = $row['name'];
            }

            // Add preferred_language column if it doesn't exist
            if (!in_array('preferred_language', $columns)) {
                $this->db->exec("ALTER TABLE customers ADD COLUMN preferred_language TEXT DEFAULT 'el'");
            }
        }

        // Employee system migrations
        $this->migrateEmployeeSystem();

        // Service employee selection migration
        require_once __DIR__ . '/migrations/add_service_employee_selection.php';
        migrate_add_service_employee_selection();
    }

    /**
     * Migrate employee system tables and data
     */
    private function migrateEmployeeSystem()
    {
        // Check if employees table exists
        $result = $this->db->query("SELECT name FROM sqlite_master WHERE type='table' AND name='employees'");
        $employeesTableExists = $result->fetchArray() !== false;

        if (!$employeesTableExists) {
            // Create employees table
            $this->db->exec("CREATE TABLE employees (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                working_hours TEXT,
                status TEXT DEFAULT 'active',
                created_at TEXT NOT NULL
            )");

            // Create employee_services junction table
            $this->db->exec("CREATE TABLE employee_services (
                employee_id TEXT NOT NULL,
                service_id TEXT NOT NULL,
                PRIMARY KEY (employee_id, service_id),
                FOREIGN KEY (employee_id) REFERENCES employees(id),
                FOREIGN KEY (service_id) REFERENCES services(id)
            )");

            // Insert default employee
            $defaultEmployeeId = generate_employee_id();
            $createdAt = date('Y-m-d H:i:s');

            // Get current business hours to assign to default employee
            $settings = include __DIR__ . '/settings.php';
            $workingHours = json_encode($settings['business_hours'] ?? []);

            $stmt = $this->db->prepare("INSERT INTO employees (id, name, email, phone, working_hours, status, created_at)
                VALUES (:id, :name, :email, :phone, :working_hours, :status, :created_at)");
            $stmt->bindValue(':id', $defaultEmployeeId);
            $stmt->bindValue(':name', 'Employee 1');
            $stmt->bindValue(':email', '');
            $stmt->bindValue(':phone', '');
            $stmt->bindValue(':working_hours', $workingHours);
            $stmt->bindValue(':status', 'active');
            $stmt->bindValue(':created_at', $createdAt);
            $stmt->execute();

            // Assign all existing services to default employee
            $servicesResult = $this->db->query("SELECT id FROM services");
            while ($service = $servicesResult->fetchArray(SQLITE3_ASSOC)) {
                $stmt = $this->db->prepare("INSERT INTO employee_services (employee_id, service_id) VALUES (:employee_id, :service_id)");
                $stmt->bindValue(':employee_id', $defaultEmployeeId);
                $stmt->bindValue(':service_id', $service['id']);
                $stmt->execute();
            }
        }

        // Check if employee_id column exists in reservations table
        $result = $this->db->query("PRAGMA table_info(reservations)");
        $columns = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $columns[] = $row['name'];
        }

        if (!in_array('employee_id', $columns)) {
            // Add employee_id column to reservations table
            $this->db->exec("ALTER TABLE reservations ADD COLUMN employee_id TEXT");

            // Get the default employee ID
            $result = $this->db->query("SELECT id FROM employees ORDER BY created_at ASC LIMIT 1");
            $defaultEmployee = $result->fetchArray(SQLITE3_ASSOC);

            if ($defaultEmployee) {
                // Assign all existing reservations to default employee
                $stmt = $this->db->prepare("UPDATE reservations SET employee_id = :employee_id WHERE employee_id IS NULL");
                $stmt->bindValue(':employee_id', $defaultEmployee['id']);
                $stmt->execute();
            }
        }
    }

    private function initializeTables()
    {
        // Create customers table
        $this->db->exec("CREATE TABLE customers (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT NOT NULL,
            mobile TEXT,
            address TEXT,
            date_of_birth TEXT,
            notes TEXT,
            preferred_contact TEXT DEFAULT 'email',
            preferred_language TEXT DEFAULT 'el',
            total_reservations INTEGER DEFAULT 0,
            total_spent REAL DEFAULT 0,
            last_visit TEXT,
            created_at TEXT NOT NULL,
            user_hash TEXT NOT NULL,
            avatar_url TEXT,
            tenant_id TEXT NOT NULL,
            UNIQUE(email, tenant_id)
        )");

        // Create reservations table
        $this->db->exec("CREATE TABLE reservations (
            id TEXT PRIMARY KEY,
            customer_id TEXT NOT NULL,
            service TEXT NOT NULL,
            date TEXT NOT NULL,
            time TEXT NOT NULL,
            duration INTEGER NOT NULL,
            price REAL NOT NULL,
            status TEXT NOT NULL,
            employee_id TEXT,
            created_at TEXT NOT NULL,
            tenant_id TEXT NOT NULL DEFAULT 'default',
            FOREIGN KEY (customer_id) REFERENCES customers(id),
            FOREIGN KEY (employee_id) REFERENCES employees(id)
        )");

        // Create services table
        $this->db->exec("CREATE TABLE services (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            duration INTEGER NOT NULL,
            price REAL NOT NULL,
            description TEXT,
            allow_employee_selection BOOLEAN DEFAULT FALSE,
            tenant_id TEXT NOT NULL DEFAULT 'default'
        )");

        // Create verification codes table
        $this->db->exec("CREATE TABLE verification_codes (
            email TEXT NOT NULL,
            code TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            PRIMARY KEY (email, code)
        )");

        // Create employees table
        $this->db->exec("CREATE TABLE employees (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            working_hours TEXT,
            status TEXT DEFAULT 'active',
            created_at TEXT NOT NULL,
            tenant_id TEXT NOT NULL DEFAULT 'default'
        )");

        // Create employee_services junction table
        $this->db->exec("CREATE TABLE employee_services (
            employee_id TEXT NOT NULL,
            service_id TEXT NOT NULL,
            PRIMARY KEY (employee_id, service_id),
            FOREIGN KEY (employee_id) REFERENCES employees(id),
            FOREIGN KEY (service_id) REFERENCES services(id)
        )");

        // Create tenants table
        $this->db->exec("CREATE TABLE IF NOT EXISTS tenants (
            id TEXT PRIMARY KEY,
            business_name TEXT NOT NULL,
            domain TEXT UNIQUE,
            subdomain TEXT UNIQUE,
            plan TEXT NOT NULL DEFAULT 'starter',
            status TEXT NOT NULL DEFAULT 'trial',
            trial_ends_at TEXT,
            billing_email TEXT,
            settings TEXT DEFAULT '{}',
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
        )");

        // Create default tenant
        $this->createDefaultTenant();

        // Create tenant-specific configuration tables
        $this->createTenantConfigTables();

        // Run migrations after table creation
        $this->runMigrations();
    }

    /**
     * Create tenant-specific configuration tables
     */
    private function createTenantConfigTables()
    {
        // Create tenant_settings table for storing tenant-specific settings
        $this->db->exec("CREATE TABLE IF NOT EXISTS tenant_settings (
            tenant_id TEXT NOT NULL,
            setting_key TEXT NOT NULL,
            setting_value TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            PRIMARY KEY (tenant_id, setting_key)
        )");

        // Create tenant_texts table for storing custom texts per tenant
        $this->db->exec("CREATE TABLE IF NOT EXISTS tenant_texts (
            tenant_id TEXT NOT NULL,
            text_type TEXT NOT NULL,
            language TEXT NOT NULL DEFAULT 'en',
            text_key TEXT NOT NULL,
            text_value TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            PRIMARY KEY (tenant_id, text_type, language, text_key)
        )");

        // Create activity_logs table for tenant-specific activity logging
        $this->db->exec("CREATE TABLE IF NOT EXISTS activity_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            tenant_id TEXT NOT NULL,
            message TEXT NOT NULL,
            created_at TEXT NOT NULL
        )");

        // Create indexes for better performance
        $this->db->exec("CREATE INDEX IF NOT EXISTS idx_tenant_settings_tenant ON tenant_settings(tenant_id)");
        $this->db->exec("CREATE INDEX IF NOT EXISTS idx_tenant_texts_tenant ON tenant_texts(tenant_id)");
        $this->db->exec("CREATE INDEX IF NOT EXISTS idx_tenant_texts_type ON tenant_texts(text_type)");
        $this->db->exec("CREATE INDEX IF NOT EXISTS idx_activity_logs_tenant ON activity_logs(tenant_id)");
    }

    /**
     * Create dummy data if enabled
     * This method can be commented out to disable dummy data creation
     */
    private function createDummyDataIfEnabled()
    {
        // COMMENT OUT THE LINE BELOW TO DISABLE DUMMY DATA CREATION
        $this->createDummyData();
    }

    /**
     * Create dummy data by calling the external dummy data creator
     */
    private function createDummyData()
    {
        require_once __DIR__ . '/create_dummy.php';

        if (!dummyDataExists()) {
            createDummyData();
        } else {
            echo "Dummy data already exists. Skipping creation.\n";
        }
    }

    /**
     * Create default tenant for fresh database
     */
    private function createDefaultTenant()
    {
        $defaultTenantId = 'TN-' . strtoupper(substr(md5(uniqid()), 0, 10));
        $now = date('Y-m-d H:i:s');

        $stmt = $this->db->prepare("INSERT OR IGNORE INTO tenants
            (id, business_name, domain, subdomain, plan, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)");

        $stmt->bindValue(1, $defaultTenantId);
        $stmt->bindValue(2, 'Default Business');
        $stmt->bindValue(3, 'localhost');
        $stmt->bindValue(4, 'default');
        $stmt->bindValue(5, 'enterprise');
        $stmt->bindValue(6, 'active');
        $stmt->bindValue(7, $now);
        $stmt->bindValue(8, $now);
        $stmt->execute();
    }

    public function __destruct()
    {
        if ($this->db) {
            $this->db->close();
        }
    }
}
