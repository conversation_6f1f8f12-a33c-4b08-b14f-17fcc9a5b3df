<!-- Stats Cards -->
<div class="stats-grid">
    <a href="?action=reservations&date=<?php echo date('Y-m-d'); ?>" class="stat-card today clickable">
        <div class="stat-icon">
            <i class="fas fa-calendar-day"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo $stats['today_reservations'] ?? 0; ?></h3>
            <p>Today's Reservations</p>
        </div>
    </a>

    <a href="?action=reservations&date=<?php echo date('Y-m-d', strtotime('+1 day')); ?>" class="stat-card tomorrow clickable">
        <div class="stat-icon">
            <i class="fas fa-calendar-plus"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo $stats['tomorrow_reservations'] ?? 0; ?></h3>
            <p>Tomorrow's Reservations</p>
        </div>
    </a>

    <a href="?action=reservations&view=week" class="stat-card week clickable">
        <div class="stat-icon">
            <i class="fas fa-calendar-week"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo $stats['this_week_reservations'] ?? 0; ?></h3>
            <p>This Week</p>
        </div>
    </a>

    <a href="?action=customers" class="stat-card customers clickable">
        <div class="stat-icon">
            <i class="fas fa-users"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo $stats['total_customers'] ?? 0; ?></h3>
            <p>Total Customers</p>
        </div>
    </a>
</div>
<div class="dashboard-grid">

    <!-- Today's Schedule -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-calendar-day"></i> Today's Schedule</h2>
            <a href="?action=reservations&date=<?php echo htmlspecialchars($today ?? ''); ?>" class="btn btn-primary btn-sm">
                View All
            </a>
        </div>

        <div class="schedule-list">
            <?php
            $todayReservations = $reservationHandler->getReservationsByDate($today ?? date('Y-m-d'));
            $services = getServices();

            if (empty($todayReservations)): ?>
                <div class="empty-state">
                    <i class="fas fa-calendar-times"></i>
                    <p>No reservations scheduled for today</p>
                </div>
                <?php else:
                // Sort by time
                usort($todayReservations, function ($a, $b) {
                    return strcmp($a['time'], $b['time']);
                });

                foreach ($todayReservations as $reservation):
                    $serviceName = isset($services[$reservation['service']]) && isset($services[$reservation['service']]['name'])
                        ? $services[$reservation['service']]['name']
                        : ($reservation['service'] ?? 'Unknown Service');
                    $statusClass = ($reservation['status'] ?? '') === 'cancelled' ? 'cancelled' : 'confirmed';

                    // Get customer details - this was missing!
                    $customerName = $reservation['name'] ?? 'Unknown Customer';
                    $customerMobile = $reservation['mobile'] ?? 'No phone';
                    $customerEmail = $reservation['email'] ?? '';

                    // If name is missing, try to get from customer handler
                    if (empty($customerName) || $customerName === 'Unknown Customer') {
                        try {
                            $customer = $customerHandler->getCustomerById($reservation['customer_id'] ?? '');
                            if ($customer) {
                                $customerName = $customer['name'] ?? 'Unknown Customer';
                                $customerMobile = $customer['mobile'] ?? 'No phone';
                                $customerEmail = $customer['email'] ?? '';
                            }
                        } catch (Exception $e) {
                            // Keep default values
                        }
                    }
                ?>
                    <div class="schedule-item <?php echo $statusClass; ?>">
                        <div class="schedule-time">
                            <?php
                            if (isset($reservation['time']) && isset($reservation['duration'])) {
                                echo format_time_range($reservation['time'], (int)$reservation['duration']);
                            } else {
                                echo '--:--';
                            }
                            ?>
                        </div>
                        <div class="schedule-details">
                            <h4><?php echo htmlspecialchars($customerName); ?></h4>
                            <p class="service-name"><?php echo htmlspecialchars($serviceName); ?></p>
                            <small class="contact-info">
                                <i class="fas fa-phone"></i> <?php echo htmlspecialchars($customerMobile); ?>
                                <?php if (!empty($customerEmail)): ?>
                                    <br><i class="fas fa-envelope"></i> <?php echo htmlspecialchars($customerEmail); ?>
                                <?php endif; ?>
                            </small>
                        </div>
                        <div class="schedule-status">
                            <span class="status-badge <?php echo $statusClass; ?>">
                                <?php echo ucfirst($reservation['status'] ?? ''); ?>
                            </span>
                        </div>
                        <div class="schedule-actions">
                            <?php if (($reservation['status'] ?? '') === 'confirmed'): ?>
                                <button class="btn btn-sm btn-secondary"
                                    onclick="editReservation('<?php echo htmlspecialchars($reservation['id'] ?? ''); ?>')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger"
                                    onclick="cancelReservation('<?php echo htmlspecialchars($reservation['id'] ?? ''); ?>')">
                                    <i class="fas fa-times"></i>
                                </button>
                            <?php endif; ?>
                            <button class="btn btn-sm btn-info"
                                onclick="viewReservation('<?php echo htmlspecialchars($reservation['id'] ?? ''); ?>')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
            <?php endforeach;
            endif; ?>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-bolt"></i> Quick Actions</h2>
        </div>

        <div class="quick-actions">
            <button class="action-btn" onclick="openModal('Add Customer', 'views/modals/add_customer.php')">
                <i class="fas fa-user-plus"></i>
                <span>New Customer</span>
            </button>

            <button class="action-btn" onclick="quickAddService()">
                <i class="fas fa-cog"></i>
                <span>New Service</span>
            </button>

            <button class="action-btn" onclick="openModal('Add New Reservation', 'views/modals/add_reservation.php')">
                <i class="fas fa-plus-circle"></i>
                <span>New Reservation</span>
            </button>

            <button class="action-btn" onclick="exportData()">
                <i class="fas fa-download"></i>
                <span>Export Data</span>
            </button>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-history"></i> Recent Activity</h2>
            <a href="?action=reports&view=activity" class="btn btn-secondary btn-sm">
                View All
            </a>
        </div>

        <div class="activity-list">
            <?php
            $activityLog = getRecentActivity(10);
            if (empty($activityLog)): ?>
                <div class="empty-state">
                    <i class="fas fa-history"></i>
                    <p>No recent activity</p>
                </div>
                <?php else:
                foreach ($activityLog as $activity): ?>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-<?php echo htmlspecialchars(getActivityIcon($activity['type'] ?? '')); ?>"></i>
                        </div>
                        <div class="activity-content">
                            <p><?php echo htmlspecialchars($activity['message'] ?? ''); ?></p>
                            <small><?php echo isset($activity['timestamp']) ? date('M j, Y g:i A', strtotime($activity['timestamp'])) : ''; ?></small>
                        </div>
                    </div>
            <?php endforeach;
            endif; ?>
        </div>
    </div>

    <!-- System Status -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-server"></i> System Status</h2>
        </div>

        <div class="system-status">
            <?php
            $systemChecks = performSystemChecks();
            foreach ($systemChecks as $check): ?>
                <div class="status-item <?php echo htmlspecialchars($check['status'] ?? ''); ?>">
                    <div class="status-indicator">
                        <i class="fas fa-<?php echo ($check['status'] ?? '') === 'ok' ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    </div>
                    <div class="status-content">
                        <h4><?php echo htmlspecialchars($check['name'] ?? ''); ?></h4>
                        <p><?php echo htmlspecialchars($check['message'] ?? ''); ?></p>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Upcoming Reservations -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-calendar-alt"></i> Upcoming This Week</h2>
            <a href="?action=reservations&view=week" class="btn btn-secondary btn-sm">
                View Calendar
            </a>
        </div>

        <div class="upcoming-chart">
            <?php
            $weekDays = [];
            for ($i = 0; $i < 7; $i++) {
                $date = date('Y-m-d', strtotime("+$i days"));
                $dayReservations = $reservationHandler->getReservationsByDate($date);
                $confirmedCount = count(array_filter($dayReservations, function ($r) {
                    return ($r['status'] ?? '') === 'confirmed';
                }));

                $weekDays[] = [
                    'date' => $date,
                    'day' => date('D', strtotime($date)),
                    'count' => $confirmedCount,
                    'is_today' => ($date === ($today ?? date('Y-m-d')))
                ];
            }

            $maxCount = max(array_column($weekDays, 'count')) ?: 1;
            ?>

            <div class="chart-container">
                <?php foreach ($weekDays as $day): ?>
                    <div class="chart-bar <?php echo $day['is_today'] ? 'today' : ''; ?>">
                        <div class="bar" style="height: <?php echo ($day['count'] / $maxCount) * 100; ?>%"></div>
                        <div class="bar-label">
                            <strong><?php echo $day['count']; ?></strong>
                            <span><?php echo $day['day']; ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<script>
    // Dashboard specific JavaScript
    function editReservation(reservationId) {
        openModal('Edit Reservation', `views/modals/edit_reservation.php?id=${reservationId}`);
    }

    function cancelReservation(reservationId) {
        confirmAction('Are you sure you want to cancel this reservation?', () => {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
            <input type="hidden" name="action" value="cancel_reservation">
            <input type="hidden" name="reservation_id" value="${reservationId}">
            <input type="hidden" name="reason" value="Cancelled by admin">
        `;
            document.body.appendChild(form);
            form.submit();
        });
    }

    function viewReservation(reservationId) {
        openModal('Reservation Details', `views/modals/view_reservation.php?id=${reservationId}`);
    }

    function exportData() {
        const exportType = prompt('Export type (reservations/customers/all):', 'reservations');
        if (exportType) {
            window.location.href = `export.php?type=${exportType}`;
        }
    }

    // Auto-refresh dashboard every 5 minutes
    setInterval(() => {
        if (document.visibilityState === 'visible') {
            location.reload();
        }
    }, 300000);

    // Real-time updates for today's reservations
    function refreshTodaySchedule() {
        fetch('api/get_today_schedule.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the schedule list
                    updateScheduleList(data.reservations);
                }
            })
            .catch(error => console.error('Error refreshing schedule:', error));
    }

    // Refresh every 2 minutes
    setInterval(refreshTodaySchedule, 120000);
</script>