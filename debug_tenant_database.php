<?php
/**
 * Debug Tenant Database
 * This script checks what tenants exist in the database
 */

require_once 'includes/config.php';
require_once 'includes/Database.php';

echo "<h1>Tenant Database Debug</h1>";
echo "<p>Current URL: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . $_SERVER['REQUEST_URI'] . "</p>";

// Test 1: Check tenants table
echo "<h2>1. Tenants Table</h2>";
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Check if tenants table exists
    $result = $conn->query("SELECT name FROM sqlite_master WHERE type='table' AND name='tenants'");
    $row = $result->fetchArray(SQLITE3_ASSOC);
    
    if ($row) {
        echo "✅ Tenants table exists<br>";
        
        // Get all tenants
        $result = $conn->query("SELECT * FROM tenants ORDER BY created_at");
        $tenants = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $tenants[] = $row;
        }
        
        echo "✅ Found " . count($tenants) . " tenants:<br>";
        foreach ($tenants as $tenant) {
            echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ccc;'>";
            echo "<strong>ID:</strong> " . htmlspecialchars($tenant['id']) . "<br>";
            echo "<strong>Business Name:</strong> " . htmlspecialchars($tenant['business_name']) . "<br>";
            echo "<strong>Domain:</strong> " . htmlspecialchars($tenant['domain'] ?? 'N/A') . "<br>";
            echo "<strong>Subdomain:</strong> " . htmlspecialchars($tenant['subdomain'] ?? 'N/A') . "<br>";
            echo "<strong>Status:</strong> " . htmlspecialchars($tenant['status']) . "<br>";
            echo "<strong>Plan:</strong> " . htmlspecialchars($tenant['plan']) . "<br>";
            echo "</div>";
        }
    } else {
        echo "❌ Tenants table does not exist<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 2: Check subdomain lookup
echo "<h2>2. Subdomain Lookup Test</h2>";
$testSubdomains = ['default', 'realma', 'test'];

foreach ($testSubdomains as $subdomain) {
    try {
        $stmt = $conn->prepare("SELECT id, business_name FROM tenants WHERE subdomain = :subdomain AND status = 'active' LIMIT 1");
        $stmt->bindValue(':subdomain', $subdomain);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        
        if ($row) {
            echo "✅ Subdomain '$subdomain' found: " . htmlspecialchars($row['business_name']) . " (ID: " . htmlspecialchars($row['id']) . ")<br>";
        } else {
            echo "❌ Subdomain '$subdomain' not found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error checking subdomain '$subdomain': " . $e->getMessage() . "<br>";
    }
}

// Test 3: Current host analysis
echo "<h2>3. Current Host Analysis</h2>";
$host = $_SERVER['HTTP_HOST'] ?? '';
echo "📋 Current host: " . htmlspecialchars($host) . "<br>";

if (preg_match('/^([^.]+)\.skrtz\.gr$/', $host, $matches)) {
    $subdomain = $matches[1];
    echo "✅ Detected subdomain: " . htmlspecialchars($subdomain) . "<br>";
    
    // Check if this subdomain exists in database
    try {
        $stmt = $conn->prepare("SELECT id, business_name FROM tenants WHERE subdomain = :subdomain AND status = 'active' LIMIT 1");
        $stmt->bindValue(':subdomain', $subdomain);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        
        if ($row) {
            echo "✅ Subdomain exists in database: " . htmlspecialchars($row['business_name']) . "<br>";
        } else {
            echo "❌ Subdomain NOT found in database<br>";
            echo "<strong>This is why tenant detection is failing!</strong><br>";
        }
    } catch (Exception $e) {
        echo "❌ Error checking current subdomain: " . $e->getMessage() . "<br>";
    }
} else {
    echo "ℹ️ Not a subdomain pattern or not skrtz.gr domain<br>";
}

// Test 4: Create missing tenant (if needed)
if (isset($subdomain) && $subdomain === 'realma') {
    echo "<h2>4. Create Missing 'realma' Tenant</h2>";
    
    try {
        // Check if realma tenant already exists
        $stmt = $conn->prepare("SELECT id FROM tenants WHERE subdomain = 'realma'");
        $result = $stmt->execute();
        $existing = $result->fetchArray(SQLITE3_ASSOC);
        
        if (!$existing) {
            echo "🔧 Creating 'realma' tenant...<br>";
            
            $tenantId = 'TN-' . strtoupper(substr(md5(uniqid()), 0, 10));
            $stmt = $conn->prepare("
                INSERT INTO tenants 
                (id, business_name, domain, subdomain, plan, status, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->bindValue(1, $tenantId);
            $stmt->bindValue(2, 'Realma Beauty Spa');
            $stmt->bindValue(3, 'realma.skrtz.gr');
            $stmt->bindValue(4, 'realma');
            $stmt->bindValue(5, 'enterprise');
            $stmt->bindValue(6, 'active');
            $stmt->bindValue(7, date('Y-m-d H:i:s'));
            $stmt->bindValue(8, date('Y-m-d H:i:s'));
            
            if ($stmt->execute()) {
                echo "✅ Created 'realma' tenant with ID: " . htmlspecialchars($tenantId) . "<br>";
                
                // Initialize default settings for this tenant
                require_once 'includes/TenantSettingsManager.php';
                try {
                    TenantSettingsManager::initializeDefaultSettings($tenantId);
                    echo "✅ Initialized default settings for realma tenant<br>";
                } catch (Exception $e) {
                    echo "⚠️ Warning: Could not initialize settings: " . $e->getMessage() . "<br>";
                }
            } else {
                echo "❌ Failed to create 'realma' tenant<br>";
            }
        } else {
            echo "ℹ️ 'realma' tenant already exists with ID: " . htmlspecialchars($existing['id']) . "<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error creating tenant: " . $e->getMessage() . "<br>";
    }
}

// Test 5: Test tenant context after creation
echo "<h2>5. Test Tenant Context After Fix</h2>";
try {
    require_once 'includes/TenantContext.php';
    
    // Reset any cached tenant data
    TenantContext::setTenant(null);
    
    // Try to initialize from request again
    if (TenantContext::initializeFromRequest()) {
        $tenantId = TenantContext::getTenant();
        $tenantData = TenantContext::getTenantData();
        
        echo "✅ Tenant context initialized successfully<br>";
        echo "✅ Tenant ID: " . htmlspecialchars($tenantId ?? 'NULL') . "<br>";
        if ($tenantData) {
            echo "✅ Business Name: " . htmlspecialchars($tenantData['business_name'] ?? 'N/A') . "<br>";
            echo "✅ Subdomain: " . htmlspecialchars($tenantData['subdomain'] ?? 'N/A') . "<br>";
        }
    } else {
        echo "❌ Tenant context initialization still failing<br>";
    }
} catch (Exception $e) {
    echo "❌ Tenant context error: " . $e->getMessage() . "<br>";
}

echo "<h2>Summary</h2>";
echo "<p><strong>If 'realma' tenant was created:</strong></p>";
echo "<ul>";
echo "<li>The realma.skrtz.gr subdomain should now work</li>";
echo "<li>Tenant detection should succeed</li>";
echo "<li>Data isolation should work properly</li>";
echo "<li>Employee and service functionality should work on the subdomain</li>";
echo "</ul>";

echo "<p><strong>Next steps:</strong></p>";
echo "<ul>";
echo "<li>Test realma.skrtz.gr/test_tenant_subdomain_issues.php again</li>";
echo "<li>Test realma.skrtz.gr/admin/ access</li>";
echo "<li>Test employee functionality on the subdomain</li>";
echo "</ul>";
?>
