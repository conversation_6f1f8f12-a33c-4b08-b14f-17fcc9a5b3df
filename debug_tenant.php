<?php
/**
 * Debug script to test tenant functionality
 */

require_once 'includes/tenant_init.php';
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/admin_functions.php';
require_once 'includes/TenantSettingsManager.php';

echo "<h1>Tenant Debug Information</h1>";

// Test 1: Tenant Context
echo "<h2>1. Tenant Context</h2>";
try {
    $tenantId = TenantContext::getTenant();
    $tenantData = TenantContext::getTenantData();
    echo "✅ Tenant ID: " . ($tenantId ?? 'NULL') . "<br>";
    echo "✅ Tenant Data: " . json_encode($tenantData) . "<br>";
    echo "✅ Is Valid Tenant: " . (TenantContext::isValidTenant() ? 'Yes' : 'No') . "<br>";
} catch (Exception $e) {
    echo "❌ Tenant Context Error: " . $e->getMessage() . "<br>";
}

// Test 2: Settings
echo "<h2>2. Settings</h2>";
try {
    $settings = getSettings();
    echo "✅ Settings loaded: " . json_encode($settings) . "<br>";
    
    // Test setting a value
    $testResult = TenantSettingsManager::setSetting('test_setting', 'test_value');
    echo "✅ Test setting save: " . ($testResult ? 'Success' : 'Failed') . "<br>";
    
    $testValue = TenantSettingsManager::getSetting('test_setting', 'default');
    echo "✅ Test setting read: " . $testValue . "<br>";
} catch (Exception $e) {
    echo "❌ Settings Error: " . $e->getMessage() . "<br>";
}

// Test 3: Services
echo "<h2>3. Services</h2>";
try {
    $services = getServices();
    echo "✅ Services count: " . count($services) . "<br>";
    if (!empty($services)) {
        echo "✅ First service: " . json_encode(array_values($services)[0]) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Services Error: " . $e->getMessage() . "<br>";
}

// Test 4: Employees
echo "<h2>4. Employees</h2>";
try {
    $employees = getEmployees();
    echo "✅ Employees count: " . count($employees) . "<br>";
    if (!empty($employees)) {
        echo "✅ First employee: " . json_encode(array_values($employees)[0]) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Employees Error: " . $e->getMessage() . "<br>";
}

// Test 5: Database Connection
echo "<h2>5. Database</h2>";
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    echo "✅ Database connection: OK<br>";
    
    // Test tenant table
    $result = $conn->query("SELECT COUNT(*) as count FROM tenants");
    $row = $result->fetchArray(SQLITE3_ASSOC);
    echo "✅ Tenants in database: " . $row['count'] . "<br>";
    
    // Test current tenant data
    if ($tenantId) {
        $stmt = $conn->prepare("SELECT * FROM tenants WHERE id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $tenantRow = $result->fetchArray(SQLITE3_ASSOC);
        echo "✅ Current tenant in DB: " . json_encode($tenantRow) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Database Error: " . $e->getMessage() . "<br>";
}

// Test 6: Employee Add Function
echo "<h2>6. Employee Add Test</h2>";
try {
    $testEmployeeId = 'TEST-' . time();
    $result = addEmployee($testEmployeeId, 'Test Employee', '<EMAIL>', '1234567890', [], []);
    echo "✅ Test employee add: " . ($result ? 'Success' : 'Failed') . "<br>";
    
    if ($result) {
        // Clean up test employee
        $deleteResult = deleteEmployee($testEmployeeId);
        echo "✅ Test employee cleanup: " . ($deleteResult['success'] ? 'Success' : 'Failed') . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Employee Add Error: " . $e->getMessage() . "<br>";
}

echo "<h2>Debug Complete</h2>";
echo "<p>Check the results above to identify any issues.</p>";
?>
