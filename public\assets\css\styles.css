/* ========================================
   CSS CUSTOM PROPERTIES (MOBILE-FIRST)
   ======================================== */
:root {
  /* Color System */
  --primary: #487f79;
  --primary-dark: #3a6660;
  --primary-light: #6ba399;
  --primary-lighter: #e8f4f2;
  --secondary: #f8fafc;
  --secondary-dark: #e2e8f0;
  --secondary-darker: #cbd5e1;

  /* Status Colors */
  --danger: #ef4444;
  --danger-light: #fef2f2;
  --success: #10b981;
  --success-light: #f0fdf4;
  --warning: #f59e0b;
  --warning-light: #fffbeb;
  --info: #3b82f6;
  --info-dark: #1d4ed8;
  --info-light: #eff6ff;
  --error: #ef4444;  /* alias for --danger */

  /* Typography Scale (Mobile-First) */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --text-inverse: #ffffff;
  --text-light: #94a3b8;  /* alias for --text-muted */
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;

  /* Mobile Typography */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */

  /* Additional Typography (Legacy Support) */
  --font-size-small: 0.875rem;  /* 14px - alias for --font-size-sm */
  --font-size-large: 1.125rem;  /* 18px - alias for --font-size-lg */

  /* Mobile Spacing System */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */

  /* Additional Spacing (Legacy Support) */
  --spacing-xs: 0.25rem;   /* 4px - alias for --space-1 */
  --spacing-sm: 0.5rem;    /* 8px - alias for --space-2 */
  --spacing-md: 1rem;      /* 16px - alias for --space-4 */
  --spacing-lg: 1.5rem;    /* 24px - alias for --space-6 */
  --spacing-xl: 2rem;      /* 32px - alias for --space-8 */

  /* Visual System */
  --border-color: #e2e8f0;
  --border-color-light: #f1f5f9;
  --border-color-dark: #cbd5e1;
  --border-width: 1px;
  --border-radius-sm: 0.375rem;  /* 6px */
  --border-radius: 0.5rem;       /* 8px */
  --border-radius-lg: 0.75rem;   /* 12px */
  --border-radius-xl: 1rem;      /* 16px */

  /* Additional Border Variables (Legacy Support) */
  --border: #e2e8f0;              /* alias for --border-color */
  --border-light: #f1f5f9;        /* alias for --border-color-light */
  --border-radius-small: 0.375rem; /* alias for --border-radius-sm */

  /* Shadows (Mobile-Optimized) */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-base: 200ms ease;
  --transition-slow: 300ms ease;
  --transition: 200ms ease;  /* alias for --transition-base */

  /* Touch Targets (Mobile-First) */
  --touch-target: 44px;
  --touch-target-sm: 40px;
  --touch-target-lg: 48px;
}

/* ========================================
   GLOBAL RESET & BASE STYLES (MOBILE-FIRST)
   ======================================== */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--secondary);
  min-height: 100vh;
  min-height: 100dvh; /* Dynamic viewport height for mobile */
  display: flex;
  flex-direction: column; /* Dynamic viewport height for mobile */
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Main content wrapper for sticky footer */
main {
  flex: 1;
}

/* Mobile-First Container */
.container {
  width: 100%;
  background: #ffffff;
  position: relative;
  padding: 0;
  margin: 0;
}

/* Tablet (768px and up) */
@media (min-width: 48rem) {
  .container {
    max-width: 44rem; /* 704px */
    margin: var(--space-6) auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
  }
}

/* Desktop (1024px and up) */
@media (min-width: 64rem) {
  .container {
    max-width: 48rem; /* 768px */
    margin: var(--space-8) auto;
  }
}

/* ========================================
   LANGUAGE SELECTOR (MOBILE-FIRST)
   ======================================== */
.language-selector {
  position: fixed;
  top: var(--space-3);
  right: var(--space-3);
  z-index: 50;
}

.language-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--primary);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: var(--transition-base);
  box-shadow: var(--shadow);
  min-height: var(--touch-target-sm);
  touch-action: manipulation;
}

.language-btn:hover,
.language-btn:focus {
  background: var(--primary-dark);
  box-shadow: var(--shadow-md);
  outline: none;
}

.language-btn:active {
  transform: scale(0.98);
}

/* Tablet and up */
@media (min-width: 48rem) {
  .language-selector {
    position: absolute;
    top: var(--space-5);
    right: var(--space-5);
  }

  .language-btn {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base);
    gap: var(--space-3);
    min-height: var(--touch-target);
  }
}

.language-btn.secondary {
  background: var(--secondary-dark);
  color: var(--text-primary);
}

.language-btn.secondary:hover,
.language-btn.secondary:focus {
  background: var(--secondary-darker);
}

/* ========================================
   HEADER COMPONENT
   ======================================== */
header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--text-inverse);
  text-align: center;
  padding: var(--space-8) var(--space-4) var(--space-6);
  padding-top: calc(var(--space-8) + var(--touch-target-sm)); /* Account for fixed language selector */
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  box-shadow: var(--shadow-md);
}

header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

header h1 {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  margin-bottom: var(--space-2);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
  line-height: 1.2;
}

header p {
  font-size: var(--font-size-base);
  opacity: 0.9;
  position: relative;
  z-index: 1;
  max-width: 28rem;
  margin: 0 auto;
  line-height: 1.5;
}

/* Tablet and up */
@media (min-width: 48rem) {
  header {
    padding: var(--space-12) var(--space-6) var(--space-8);
    padding-top: var(--space-12); /* Reset padding-top */
  }

  header h1 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-3);
  }

  header p {
    font-size: var(--font-size-lg);
    max-width: 36rem;
  }
}

/* ========================================
   MAIN CONTENT PANELS (MOBILE-FIRST)
   ======================================== */
.booking-panel {
  background: #ffffff;
  padding: var(--space-6) var(--space-4);
  margin: 0;
  border-bottom: var(--border-width) solid var(--border-color-light);
  transition: var(--transition-base);
}

.booking-panel:hover {
  background: #fafbfc;
  border-bottom-color: var(--primary-light);
}

.booking-panel:last-child {
  border-bottom: none;
}

.booking-panel h2 {
  color: var(--primary-dark);
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--space-6);
  display: flex;
  align-items: center;
  gap: var(--space-3);
  line-height: 1.3;
}

.booking-panel h2::before {
  content: "";
  width: 3px;
  height: 20px;
  background: var(--primary);
  border-radius: var(--border-radius-sm);
  flex-shrink: 0;
}

.booking-panel p {
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  line-height: 1.6;
  font-size: var(--font-size-base);
}

/* Tablet and up */
@media (min-width: 48rem) {
  .booking-panel {
    padding: var(--space-8) var(--space-6);
  }

  .booking-panel h2 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-8);
    gap: var(--space-4);
  }

  .booking-panel h2::before {
    width: 4px;
    height: 24px;
  }

  .booking-panel p {
    font-size: var(--font-size-lg);
  }
}

/* ========================================
   FORM ELEMENTS
   ======================================== */
.form-group {
  margin-bottom: var(--space-5);
}

label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  line-height: 1.4;
}

input,
select,
textarea {
  width: 100%;
  padding: var(--space-4);
  border: var(--border-width) solid var(--border-color);
  font-size: 16px; /* Prevent zoom on iOS */
  border-radius: var(--border-radius);
  font-family: var(--font-family);
  transition: var(--transition-base);
  background: #ffffff;
  color: var(--text-primary);
  min-height: var(--touch-target);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  box-sizing: border-box;
}

input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(72, 127, 121, 0.1);
  background: #ffffff;
}

input:disabled,
select:disabled,
textarea:disabled {
  background: var(--secondary);
  color: var(--text-muted);
  cursor: not-allowed;
}

input::placeholder {
  color: var(--text-muted);
}

small,
.form-help {
  display: block;
  margin-top: var(--space-2);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

.form-error {
  display: block;
  margin-top: var(--space-2);
  color: var(--danger);
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

/* Select dropdown styling */
select {
  background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
  background-repeat: no-repeat;
  background-position: right var(--space-3) center;
  background-size: 12px;
  padding-right: var(--space-10);
}

/* Tablet and up */
@media (min-width: 48rem) {
  .form-group {
    margin-bottom: var(--space-6);
  }

  input,
  select,
  textarea {
    padding: var(--space-4) var(--space-5);
  }
}

/* ========================================
   BUTTON COMPONENTS (MOBILE-FIRST)
   ======================================== */
.btn {
  display: block;
  width: 100%;
  padding: var(--space-4);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  font-weight: 500;
  font-family: var(--font-family);
  text-decoration: none;
  text-align: center;
  cursor: pointer;
  transition: var(--transition-base);
  margin-bottom: var(--space-3);
  position: relative;
  overflow: hidden;
  min-height: var(--touch-target);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn:active:not(:disabled) {
  transform: scale(0.98);
}

/* Tablet and up */
@media (min-width: 48rem) {
  .btn {
    display: inline-flex;
    width: auto;
    padding: var(--space-4) var(--space-6);
    min-width: 120px;
    margin-bottom: 0;
  }

  .btn:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

/* Inline styles moved to CSS */
.language-flag {
  font-size: 1.2em;
  margin-right: 5px;
}

.smart-flow-verified {
  display: none;
}

.inline-form {
  display: inline-block;
  margin-right: 10px;
}

.inline-form:last-child {
  margin-right: 0;
}

.cancel-form {
  display: inline-block;
}

/* Button Variants */
.primary-btn {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--text-inverse);
  box-shadow: var(--shadow);
}

.primary-btn:hover:not(:disabled) {
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
}

.secondary-btn {
  background: var(--secondary);
  color: var(--text-primary);
  border: var(--border-width) solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.secondary-btn:hover:not(:disabled) {
  background: var(--secondary-dark);
  border-color: var(--primary);
  box-shadow: var(--shadow);
}

.info-btn {
  background: var(--info);
  color: var(--text-inverse);
  box-shadow: var(--shadow);
}

.info-btn:hover:not(:disabled) {
  background: var(--info-dark);
  box-shadow: var(--shadow-lg);
}

.cancel-btn,
.danger-btn {
  background: var(--danger);
  color: var(--text-inverse);
  font-size: var(--font-size-sm);
  padding: var(--space-3) var(--space-4);
  min-width: auto;
  box-shadow: var(--shadow);
}

.cancel-btn:hover:not(:disabled),
.danger-btn:hover:not(:disabled) {
  background: #dc2626;
  box-shadow: var(--shadow-lg);
}

/* Button Groups */
.form-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-top: var(--space-6);
}

/* Tablet and up */
@media (min-width: 48rem) {
  .form-actions {
    flex-direction: row;
    justify-content: flex-end;
    gap: var(--space-4);
  }

  .cancel-btn,
  .danger-btn {
    padding: var(--space-3) var(--space-5);
  }
}

/* Divider */
.divider {
  text-align: center;
  margin: var(--spacing-xl) 0;
  position: relative;
}

.divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border);
}

.divider span {
  background: white;
  padding: 0 var(--spacing-md);
  color: var(--text-secondary);
  font-size: var(--font-size-small);
  font-weight: 500;
}

/* ========================================
   BOOKING STEPS (MOBILE-FIRST)
   ======================================== */
.booking-step {
  margin-bottom: var(--space-6);
  padding: var(--space-6) var(--space-4);
  background: #ffffff;
 /* border-radius: var(--border-radius-lg);
  border: var(--border-width) solid var(--border-color);
  box-shadow: var(--shadow);*/
}

.booking-step h2 {
  font-size: var(--font-size-xl);
  color: var(--primary-dark);
  margin-bottom: var(--space-6);
  font-weight: 600;
  text-align: center;
  line-height: 1.3;
}

.booking-step:last-child {
  margin-bottom: 0;
}

/* Tablet and up */
@media (min-width: 48rem) {
  .booking-step {
    margin-bottom: var(--space-8);
    padding: var(--space-8) var(--space-6);
  }

  .booking-step h2 {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-8);
  }
}

/* ========================================
   DATE SELECTOR (MOBILE-FIRST)
   ======================================== */
.date-selector {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.year-month-selector {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.date-week {
  display: flex;
  justify-content: center;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
}

/* Calendar Grid Layout */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--space-2);
  max-width: 100%;
  margin: var(--space-4) 0;
}

.day-header {
  background: var(--primary-lighter);
  color: var(--primary-dark);
  padding: var(--space-2);
  text-align: center;
  font-weight: 600;
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.date-card {
  background: #ffffff;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--space-2);
  text-align: center;
  cursor: pointer;
  transition: var(--transition-base);
  user-select: none;
  -webkit-user-select: none;
  position: relative;
  overflow: hidden;
  min-height: var(--touch-target-sm);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  touch-action: manipulation;
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* Tablet and up */
@media (min-width: 48rem) {
  .calendar-grid {
    gap: var(--space-3);
  }

  .date-card {
    min-height: var(--touch-target);
    padding: var(--space-3);
    font-size: var(--font-size-base);
  }

  .day-header {
    padding: var(--space-3);
    font-size: var(--font-size-sm);
  }
}

/* Date Card States */
.date-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary);
  transform: scaleX(0);
  transition: var(--transition-base);
}

.date-card:hover:not(.disabled):not(.past) {
  border-color: var(--primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.date-card:hover:not(.disabled):not(.past)::before {
  transform: scaleX(1);
}

.date-card:active:not(.disabled):not(.past) {
  transform: scale(0.98);
}

.date-card.selected {
  border-color: var(--primary);
  background: var(--primary-lighter);
  color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.date-card.selected::before {
  transform: scaleX(1);
}

.date-card.disabled,
.date-card.past {
  border-color: var(--border-color-light);
  background: var(--secondary);
  color: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

.date-card.empty {
  visibility: hidden;
}

.date-card.today {
  border-color: var(--info);
  background: var(--info-light);
  color: var(--info-dark);
  font-weight: 600;
}

/* Desktop hover effects */
@media (min-width: 48rem) {
  .date-card:hover:not(.disabled):not(.past) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
}



.selected-month-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 5px;
}

.btn.small {
    padding: 5px 10px;
    font-size: 14px;
}

.loading, .error {
    text-align: center;
    padding: 20px;
    font-style: italic;
}

.error {
    color: #d32f2f;
}

.date-card.empty {
    visibility: hidden;
}

.date-card.past {
    opacity: 0.3;
    cursor: not-allowed;
}

.day-number {
    font-weight: 600;
    font-size: var(--font-size-base);
}

/* Date card availability backgrounds */
.date-card.has-availability {
    background: #f6fbf8; /* Softer light green background for available dates */
    border-color: #bbf7d0;
}

.date-card.no-availability {
    background: #fef2f2; /* Light red background for unavailable dates */
    border-color: #fecaca;
}

.date-card.has-availability:hover:not(.disabled):not(.past) {
    background: #ecfdf5;
    border-color: #86efac;
}

.date-card.no-availability:hover:not(.disabled):not(.past) {
    background: #fee2e2;
    border-color: #fca5a5;
}


.date-day {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.date-number {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-dark);
  margin: var(--spacing-xs) 0;
}

.date-month {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ========================================
   SERVICE SELECTOR (MOBILE-FIRST)
   ======================================== */
.service-selector {
  margin-bottom: var(--space-6);
}

.service-selector select {
  font-size: var(--font-size-base);
  padding: var(--space-4);
  width: 100%;
  background: #ffffff;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  min-height: var(--touch-target);
}

.service-selector label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: 500;
  color: var(--text-primary);
}

/* Enhanced Time Slots */
.time-slots {
  margin-bottom: var(--spacing-lg);
}

.time-slots-info {
  background: linear-gradient(135deg, var(--info-light) 0%, #e3f2fd 100%);
  border: 1px solid #bbdefb;
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  color: var(--text-primary);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.time-slots-info .service-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.time-slots-info .service-name {
  font-size: var(--font-size-large);
  font-weight: 600;
  color: var(--primary-dark);
  margin: 0;
}

.time-slots-info .service-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  font-size: var(--font-size-small);
  color: var(--text-secondary);
}

.time-slots-info .meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.time-slots-info .meta-item i {
  color: var(--primary);
}

.time-period {
  margin-bottom: var(--spacing-xl);
}

.time-period:last-child {
  margin-bottom: 0;
}

.time-period-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-light);
}

.time-period-header h3 {
  margin: 0;
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
}

.time-period-header i {
  font-size: var(--font-size-large);
  color: var(--primary);
}

.time-period-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: var(--spacing-md);
}

.time-slot {
  background: white;
  border: 2px solid var(--border);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  cursor: pointer;
  font-weight: 500;
  font-size: var(--font-size-base);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.time-slot::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  transition: left 0.5s;
}

.time-slot:hover::before {
  left: 100%;
}

.time-slot:hover {
  border-color: var(--primary);
  background: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

.time-slot.selected {
  border-color: var(--primary);
  background: var(--primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.time-slot.selected::after {
  content: '✓';
  position: absolute;
  top: 4px;
  right: 6px;
  font-size: 12px;
  font-weight: bold;
}

.time-slot-time {
  font-size: var(--font-size-sm);
  font-weight: 600;
  margin-bottom: 2px;
}

.time-slot-duration {
  font-size: var(--font-size-small);
  opacity: 0.8;
}

.time-slot-status {
  font-size: var(--font-size-small);
  opacity: 0.7;
  font-style: italic;
  margin-top: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.time-slot-status i {
  font-size: 10px;
}

/* Unavailable time slots */
.time-slot.unavailable {
  background: #f5f5f5;
  border-color: #e0e0e0;
  color: #999;
  cursor: not-allowed;
  opacity: 0.6;
}

.time-slot.unavailable:hover {
  background: #f5f5f5;
  border-color: #e0e0e0;
  transform: none;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.time-slot.unavailable::before {
  display: none;
}

.time-slot.unavailable .time-slot-time {
  text-decoration: line-through;
}

.time-slot.unavailable .time-slot-status {
  color: #d32f2f;
  font-weight: 500;
}

.no-slots-message {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
  font-style: italic;
}

.no-slots-message i {
  font-size: 2rem;
  color: var(--border);
  margin-bottom: var(--spacing-md);
  display: block;
}

.no-slots-message h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: var(--font-size-large);
}

.no-slots-message p {
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.5;
}

/* Loading animation for time slots */
.time-slots-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
}

.time-slots-loading i {
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-sm);
  font-size: var(--font-size-large);
  color: var(--primary);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Fade in animation for time periods */
.time-period {
  animation: fadeInUp 0.5s ease-out;
}

.time-period:nth-child(2) { animation-delay: 0.1s; }
.time-period:nth-child(3) { animation-delay: 0.2s; }
.time-period:nth-child(4) { animation-delay: 0.3s; }

/* Staggered animation for time slots */
.time-slot {
  animation: fadeInScale 0.4s ease-out;
}

.time-slot:nth-child(1) { animation-delay: 0.05s; }
.time-slot:nth-child(2) { animation-delay: 0.1s; }
.time-slot:nth-child(3) { animation-delay: 0.15s; }
.time-slot:nth-child(4) { animation-delay: 0.2s; }
.time-slot:nth-child(5) { animation-delay: 0.25s; }
.time-slot:nth-child(6) { animation-delay: 0.3s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pulse animation for selected time slot */
.time-slot.selected {
  animation: selectedPulse 0.6s ease-out;
}

@keyframes selectedPulse {
  0% {
    transform: translateY(-2px) scale(1);
  }
  50% {
    transform: translateY(-2px) scale(1.05);
  }
  100% {
    transform: translateY(-2px) scale(1);
  }
}

/* Smart Email Entry Flow Styles */
.choice-buttons {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-4);
  margin-top: var(--space-6);
}

.choice-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-6);
  text-align: center;
  border-radius: var(--border-radius-lg);
  transition: var(--transition-base);
  min-height: var(--space-16);
  justify-content: center;
  position: relative;
  overflow: hidden;
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
}

.choice-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.choice-btn:hover::before {
  left: 100%;
}

.choice-btn:active {
  transform: scale(0.98);
}

.choice-btn i {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-4);
}

.choice-btn span {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--space-2);
  display: block;
  line-height: 1.3;
}

.choice-btn small {
  font-size: var(--font-size-sm);
  opacity: 0.7;
  font-weight: normal;
  line-height: 1.4;
  color: var(--text-light);
}

/* Tablet and up */
@media (min-width: 48rem) {
  .choice-buttons {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
  }

  .choice-btn {
    padding: var(--space-8);
    min-height: 140px;
  }

  .choice-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .choice-btn i {
    font-size: 2.5rem;
  }

  .choice-btn span {
    font-size: var(--font-size-xl);
  }
}

.info-btn {
  background: var(--info);
  color: white;
  border: 2px solid var(--info);
}

.info-btn:hover {
  background: var(--info-dark);
  border-color: var(--info-dark);
}

.secondary-btn {
  background: var(--border);
  color: var(--text-primary);
  border: 2px solid var(--border);
}

.secondary-btn:hover {
  background: var(--text-secondary);
  color: white;
  border-color: var(--text-secondary);
}

/* Email check loading state */
.email-checking {
  position: relative;
  pointer-events: none;
}

.email-checking::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Step transitions */
.step-transition {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



/* ========================================
   CONFIRMATION MODAL (MOBILE-FIRST)
   ======================================== */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition-base);
  padding: var(--space-4);
}

.confirmation-overlay.show {
  opacity: 1;
  visibility: visible;
}

.confirmation-modal {
  background: #ffffff;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
  max-width: 480px;
  width: 100%;
  max-height: 90vh;
  max-height: 90dvh;
  overflow: hidden;
  transform: scale(0.8) translateY(20px);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.confirmation-overlay.show .confirmation-modal {
  transform: scale(1) translateY(0);
}

.confirmation-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--text-inverse);
  padding: var(--space-6) var(--space-4);
  text-align: center;
  position: relative;
}

.confirmation-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
  pointer-events: none;
}

.confirmation-icon {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--space-4);
  display: block;
  animation: bounceIn 0.6s ease-out 0.2s both;
  position: relative;
  z-index: 1;
}

.confirmation-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin: 0;
  position: relative;
  z-index: 1;
  line-height: 1.3;
}

.confirmation-content {
  padding: var(--space-6) var(--space-4);
}

/* Tablet and up */
@media (min-width: 48rem) {
  .confirmation-header {
    padding: var(--space-8) var(--space-6);
  }

  .confirmation-content {
    padding: var(--space-8) var(--space-6);
  }

  .confirmation-icon {
    font-size: 3rem;
  }

  .confirmation-title {
    font-size: var(--font-size-2xl);
  }
}

.confirmation-message {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  text-align: center;
  line-height: 1.6;
}

.confirmation-details {
  background: var(--secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--space-5);
  margin-bottom: var(--space-6);
  border: var(--border-width) solid var(--border-color-light);
}

.confirmation-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--space-3) 0;
  border-bottom: var(--border-width) solid var(--border-color-light);
  gap: var(--space-4);
}

.confirmation-detail-item:last-child {
  border-bottom: none;
}

.confirmation-detail-label {
  font-weight: 500;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.confirmation-detail-label i {
  color: var(--primary);
  width: 16px;
  text-align: center;
}

.confirmation-detail-value {
  font-weight: 600;
  color: var(--text-primary);
  text-align: right;
  font-size: var(--font-size-base);
  word-break: break-word;
}

/* Mobile adjustments for detail items */
@media (max-width: 30rem) {
  .confirmation-detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }

  .confirmation-detail-value {
    text-align: left;
    font-size: var(--font-size-sm);
  }
}

.confirmation-actions {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  justify-content: center;
  margin-top: var(--space-4);
}

.confirmation-btn {
  padding: var(--space-4) var(--space-6);
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: var(--transition-base);
  border: var(--border-width) solid;
  min-width: 120px;
  min-height: var(--touch-target);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
}

.confirmation-btn:active {
  transform: scale(0.98);
}

.confirmation-btn-cancel {
  background: #ffffff;
  color: var(--text-secondary);
  border-color: var(--border-color);
  order: 2;
}

.confirmation-btn-cancel:hover:not(:disabled) {
  background: var(--secondary);
  color: var(--text-primary);
  border-color: var(--border-color-dark);
  box-shadow: var(--shadow);
}

.confirmation-btn-confirm {
  background: var(--success);
  color: var(--text-inverse);
  border-color: var(--success);
  order: 1;
  box-shadow: var(--shadow);
}

.confirmation-btn-confirm:hover:not(:disabled) {
  background: #059669;
  border-color: #059669;
  box-shadow: var(--shadow-lg);
}

.confirmation-btn-danger {
  background: var(--danger);
  color: var(--text-inverse);
  border-color: var(--danger);
  box-shadow: var(--shadow);
}

.confirmation-btn-danger:hover:not(:disabled) {
  background: #dc2626;
  border-color: #dc2626;
  box-shadow: var(--shadow-lg);
}

/* Tablet and up */
@media (min-width: 48rem) {
  .confirmation-actions {
    flex-direction: row;
    gap: var(--space-4);
  }

  .confirmation-btn-cancel {
    order: 1;
  }

  .confirmation-btn-confirm {
    order: 2;
  }

  .confirmation-btn:hover:not(:disabled) {
    transform: translateY(-1px);
  }
}

.confirmation-note {
  background: var(--info-light);
  border: var(--border-width) solid #bbdefb;
  border-radius: var(--border-radius);
  padding: var(--space-4);
  margin-top: var(--space-5);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-align: center;
  line-height: 1.5;
}

.confirmation-note i {
  color: var(--info);
  margin-right: var(--space-2);
}

/* Animations */
@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Remove old mobile responsiveness - now handled by mobile-first approach */



/* ========================================
   MESSAGES (MOBILE-FIRST)
   ======================================== */
.success-message,
.error-message,
.loading-message {
  padding: var(--space-4) var(--space-5);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--space-6);
  font-size: var(--font-size-base);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  border: var(--border-width) solid;
  box-shadow: var(--shadow-sm);
  line-height: 1.5;
}

.success-message {
  background: var(--success-light);
  color: var(--success);
  border-color: #10b981;
}

.success-message::before {
  content: "✓";
  font-weight: bold;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.error-message {
  background: var(--danger-light);
  color: var(--danger);
  border-color: #ef4444;
}

.error-message::before {
  content: "⚠";
  font-weight: bold;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.loading-message {
  background: var(--info-light);
  color: var(--info);
  border-color: #3b82f6;
  justify-content: center;
}

.loading-message::before {
  content: "⟳";
  animation: spin 1s linear infinite;
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Notification Panel */
.notification-panel {
  background: linear-gradient(135deg, var(--warning-light) 0%, #fff8e1 100%);
  border: 2px solid var(--warning);
  border-radius: var(--border-radius);
}

.notification-message {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: rgba(255, 152, 0, 0.1);
  border-radius: var(--border-radius-small);
  border-left: 4px solid var(--warning);
}

.notification-message p {
  margin-bottom: var(--spacing-sm);
}

.notification-message p:last-child {
  margin-bottom: 0;
}

.notification-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* Reservations List */
.reservations-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.reservation-item {
  background: white;
  border: 2px solid var(--border);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.reservation-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--primary);
}

.reservation-item.upcoming {
  border-color: var(--success);
  background: var(--success-light);
}

.reservation-item.upcoming::before {
  background: var(--success);
}

.reservation-item.past {
  border-color: var(--border-light);
  background: #fafafa;
  opacity: 0.8;
}

.reservation-item.past::before {
  background: var(--text-light);
}

.reservation-item.cancelled {
  border-color: var(--danger);
  background: var(--danger-light);
}

.reservation-item.cancelled::before {
  background: var(--danger);
}

.reservation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.reservation-header h3 {
  color: var(--primary-dark);
  font-size: var(--font-size-large);
  font-weight: 600;
  margin: 0;
}

.reservation-id {
  font-size: var(--font-size-small);
  color: var(--text-secondary);
  font-weight: 500;
  background: var(--secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
  border: 1px solid var(--border);
}

.reservation-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.detail-item strong {
  color: var(--text-secondary);
  font-size: var(--font-size-small);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-small);
  font-size: var(--font-size-small);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.confirmed {
  background: var(--success-light);
  color: var(--success);
  border: 1px solid var(--success);
}

.status-badge.cancelled {
  background: var(--danger-light);
  color: var(--danger);
  border: 1px solid var(--danger);
}

.reservation-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
  flex-wrap: wrap;
}

/* ========================================
   BOOKING CONTROLS (MOBILE-FIRST)
   ======================================== */
#booking-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-8) var(--space-6);
  background: linear-gradient(135deg, var(--success-light) 0%, #f0fdf4 100%);
  border-radius: var(--border-radius-xl);
  border: 2px solid var(--success);
  margin: var(--space-8) var(--space-4) var(--space-6);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

#confirm-btn {
  font-size: var(--font-size-xl);
  font-weight: 700;
  padding: var(--space-5) var(--space-10);
  min-width: 280px;
  width: 100%;
  max-width: 320px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--text-inverse);
  border: 3px solid var(--primary);
  border-radius: var(--border-radius-xl);
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: var(--touch-target);
  text-align: center;
  user-select: none;
  -webkit-user-select: none;
  gap: var(--space-3);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

#confirm-btn:hover {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

#confirm-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow);
}

#review-btn {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: var(--text-inverse);
  border: var(--border-width) solid var(--primary);
  padding: var(--space-4) var(--space-6);
  font-size: var(--font-size-base);
  font-weight: 600;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition-base);
  min-width: 180px;
  width: 100%;
  max-width: 280px;
  min-height: var(--touch-target);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  touch-action: manipulation;
  user-select: none;
  -webkit-user-select: none;
  box-shadow: var(--shadow);
}

#review-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

#review-btn:active:not(:disabled) {
  transform: scale(0.98);
}

#review-btn:disabled {
  background: var(--secondary-dark);
  border-color: var(--border-color);
  color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
  opacity: 0.6;
}

/* Tablet and up */
@media (min-width: 48rem) {
  #booking-controls {
    flex-direction: row;
    justify-content: center;
    padding: var(--space-8) var(--space-8);
    gap: var(--space-6);
    margin: var(--space-8) var(--space-6) var(--space-6);
  }

  #confirm-btn {
    width: auto;
    min-width: 240px;
    max-width: none;
  }

  #review-btn {
    width: auto;
    min-width: 200px;
    max-width: none;
  }
}

/* Back to Booking */
.back-to-booking {
  text-align: center;
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

/* Footer */
footer {
  background: var(--secondary);
  color: var(--text-secondary);
  text-align: center;
  padding: var(--spacing-lg);
  font-size: var(--font-size-small);
  border-top: 1px solid var(--border-light);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  margin-top: auto; /* Stick to bottom */

}

/* ========================================
   RESPONSIVE DESIGN (MOBILE-FIRST)
   ======================================== */

/* Small Mobile (up to 480px) - Additional optimizations */
@media (max-width: 30rem) {
  .container {
    margin: var(--space-2);
  }

  header {
    padding: var(--space-6) var(--space-3) var(--space-4);
  }

  .booking-panel {
    padding: var(--space-4) var(--space-3);
  }

  .calendar-grid {
    gap: var(--space-1);
  }

  .date-card {
    min-height: var(--space-10);
    padding: var(--space-1);
    font-size: var(--font-size-xs);
  }

  .day-header {
    padding: var(--space-1);
    font-size: var(--font-size-xs);
  }

  .time-period-slots {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: var(--space-2);
  }

  .time-slot {
    padding: var(--space-2);
    font-size: var(--font-size-sm);
    min-height: var(--space-10);
  }

  .time-slot-status {
    font-size: var(--font-size-xs);
  }

  .time-slot-status i {
    font-size: 8px;
  }
}

/* Large Mobile/Small Tablet (481px - 767px) */
@media (min-width: 30.0625rem) and (max-width: 47.9375rem) {
  .time-period-slots {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .time-slots-info .service-details {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
}

/* ========================================
   ADVANCED RESPONSIVE FEATURES
   ======================================== */

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  .btn:hover {
    transform: none;
    box-shadow: var(--shadow);
  }

  .date-card:hover:not(.disabled):not(.past) {
    transform: none;
    box-shadow: var(--shadow);
  }

  .time-slot:hover {
    transform: none;
    box-shadow: var(--shadow);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .date-card::before,
  .time-slot::before {
    transform: translateZ(0);
  }
}

/* Landscape orientation on mobile */
@media (max-width: 48rem) and (orientation: landscape) {
  header {
    padding: var(--space-4) var(--space-4) var(--space-3);
  }

  .booking-panel {
    padding: var(--space-4);
  }

  .calendar-grid {
    gap: var(--space-1);
  }

  .date-card {
    min-height: var(--space-8);
    padding: var(--space-1);
  }
}

/* Utility classes */
.mobile-hidden {
  display: none;
}

.mobile-only {
  display: block;
}

@media (min-width: 48rem) {
  .mobile-hidden {
    display: block;
  }

  .mobile-only {
    display: none;
  }
}

/* Print Styles */
@media print {
  body {
    background: white;
  }

  .container {
    box-shadow: none;
    margin: 0;
  }

  header {
    background: white !important;
    color: var(--text-primary) !important;
  }

  .btn,
  .reservation-actions,
  .notification-actions,
  #booking-controls {
    display: none !important;
  }

  .reservation-item {
    break-inside: avoid;
    border: 1px solid var(--border) !important;
    background: white !important;
  }
}

/* Focus Styles for Accessibility */
.date-card:focus,
.time-slot:focus,
.btn:focus,
input:focus,
select:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border: #000;
    --text-secondary: #000;
    --shadow: rgba(0, 0, 0, 0.5);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #e0e0e0;
    --text-secondary: #bdbdbd;
    --text-light: #9e9e9e;
    --border: #424242;
    --border-light: #616161;
    --secondary: #303030;
    --secondary-dark: #212121;
  }

  body {
    background: linear-gradient(135deg, #212121 0%, #303030 100%);
  }

  .container {
    background: #424242;
    color: var(--text-primary);
  }

  .booking-panel,
  .booking-step,
  .date-card,
  .time-slot,
  .reservation-item {
    background: #303030;
    border-color: var(--border);
    color: var(--text-primary);
  }

  .time-slot.unavailable {
    background: #2a2a2a;
    border-color: #404040;
    color: #666;
  }

  .time-slot.unavailable:hover {
    background: #2a2a2a;
    border-color: #404040;
  }

  .time-slot.unavailable .time-slot-status {
    color: #f44336;
  }

  input,
  select {
    background: #303030;
    border-color: var(--border);
    color: var(--text-primary);
  }
}

/* ========================================
   EMPLOYEE DOTS & LEGEND (MOBILE-FIRST)
   ======================================== */
.employee-dots {
  display: flex;
  justify-content: center;
  gap: var(--space-1);
  margin-top: var(--space-1);
  flex-wrap: wrap;
}

.employee-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
  transition: var(--transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: 700;
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
  line-height: 1;
}

/* Tablet and up */
@media (min-width: 48rem) {
  .employee-dot {
    width: 18px;
    height: 18px;
    font-size: 9px;
  }
}

/* Employee Color Legend */
.employee-legend {
  margin-top: var(--space-4);
  padding: var(--space-3) var(--space-4);
  background: rgba(255, 255, 255, 0.9);
  border-radius: var(--border-radius);
  border: var(--border-width) solid rgba(59, 130, 246, 0.2);
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  width: 100%;
}

.employee-legend h4 {
  margin: 0;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: 100%;
}

.not-auto-assign .employee-legend-items{
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.employee-legend-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
}

.employee-legend-item .employee-dot {
  width: 14px;
  height: 14px;
  font-size: 7px;
  flex-shrink: 0;
}

/* Auto-assignment message in employee legend */
.auto-assign-message {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
  width: 100%;
}

.auto-assign-message i {
  color: var(--primary);
  flex-shrink: 0;
}

/* Tablet and up */
@media (min-width: 48rem) {
  .employee-legend-item .employee-dot {
    width: 16px;
    height: 16px;
    font-size: 8px;
  }
}

/* Employee Selection in Summary Modal */
.employee-selection {
  margin: var(--space-4) 0;
  padding: var(--space-4);
  background: var(--secondary);
  border-radius: var(--border-radius);
  border: var(--border-width) solid var(--border-color);
}

.employee-selection h4 {
  margin: 0 0 var(--space-3) 0;
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
}

.employee-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.employee-option {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: #ffffff;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition-base);
  min-height: var(--touch-target-sm);
}

.employee-option:hover {
  border-color: var(--primary);
  background: var(--primary-lighter);
}

.employee-option input[type="radio"] {
  width: auto;
  margin: 0;
  min-height: auto;
}

.employee-option label {
  margin: 0;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-primary);
  flex: 1;
}

.employee-assigned {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-weight: 500;
  color: var(--text-primary);
}

.employee-assigned .employee-dot {
  width: 12px;
  height: 12px;
}

/* Dark mode support for employee elements */
@media (prefers-color-scheme: dark) {
  .employee-selection {
    background: #2a2a2a;
    border-color: var(--border);
  }

  .employee-option {
    background: #303030;
    border-color: var(--border);
    color: var(--text-primary);
  }

  .employee-option:hover {
    background: #404040;
    border-color: var(--primary);
  }
}
