<?php

/**
 * Text Management System
 * Handles loading and saving of customizable text strings
 */

class TextManager
{
    private static $customerTexts = null;
    private static $adminTexts = null;
    private static $emailTexts = null;

    /**
     * Get customer-facing text
     */
    public static function getCustomerText(string $key, string $default = '', string $language = 'primary'): string
    {
        if (self::$customerTexts === null) {
            self::loadCustomerTexts();
        }

        $textData = self::$customerTexts[$key] ?? null;

        if (!$textData) {
            return $default;
        }

        // Handle old format (string) and new format (array)
        if (is_string($textData)) {
            return $textData;
        }

        if (is_array($textData)) {
            $text = $textData[$language] ?? $textData['primary'] ?? '';
            // If secondary language is requested but empty, fall back to primary
            if ($language === 'secondary' && empty($text)) {
                $text = $textData['primary'] ?? '';
            }
            return $text ?: $default;
        }

        return $default;
    }

    /**
     * Get all customer texts
     */
    public static function getAllCustomerTexts(): array
    {
        if (self::$customerTexts === null) {
            self::loadCustomerTexts();
        }

        return self::$customerTexts;
    }

    /**
     * Update customer text
     */
    public static function updateCustomerText(string $key, string $value): bool
    {
        if (self::$customerTexts === null) {
            self::loadCustomerTexts();
        }

        self::$customerTexts[$key] = $value;
        return self::saveCustomerTexts();
    }

    /**
     * Update multiple customer texts
     */
    public static function updateCustomerTexts(array $texts): bool
    {
        if (self::$customerTexts === null) {
            self::loadCustomerTexts();
        }

        foreach ($texts as $key => $value) {
            self::$customerTexts[$key] = $value;
        }

        return self::saveCustomerTexts();
    }

    /**
     * Load customer texts from database (tenant-aware)
     */
    private static function loadCustomerTexts(): void
    {
        $defaultTexts = require __DIR__ . '/customer_texts.php';

        try {
            require_once __DIR__ . '/TenantSettingsManager.php';

            // Load custom texts from database (both languages) - same logic as UnifiedTextManager
            $customPrimaryTexts = TenantSettingsManager::getAllTexts('customer', 'primary');
            $customSecondaryTexts = TenantSettingsManager::getAllTexts('customer', 'secondary');

            // Merge custom texts with original texts
            $mergedTexts = $defaultTexts;

            // Update with custom texts, preserving the primary/secondary structure
            foreach ($customPrimaryTexts as $key => $primaryValue) {
                $secondaryValue = $customSecondaryTexts[$key] ?? '';
                $mergedTexts[$key] = [
                    'primary' => $primaryValue,
                    'secondary' => $secondaryValue
                ];
            }

            // Also handle any secondary-only custom texts
            foreach ($customSecondaryTexts as $key => $secondaryValue) {
                if (!isset($customPrimaryTexts[$key])) {
                    $primaryValue = $defaultTexts[$key]['primary'] ?? '';
                    $mergedTexts[$key] = [
                        'primary' => $primaryValue,
                        'secondary' => $secondaryValue
                    ];
                }
            }

            self::$customerTexts = $mergedTexts;
        } catch (Exception $e) {
            // Fallback to file-based loading for backward compatibility
            $customFile = DATA_DIR . '/custom_customer_texts.php';
            if (file_exists($customFile)) {
                $customTexts = require $customFile;
                self::$customerTexts = array_merge($defaultTexts, $customTexts);
            } else {
                self::$customerTexts = $defaultTexts;
            }
        }
    }

    /**
     * Save customer texts to database (tenant-aware)
     */
    private static function saveCustomerTexts(): bool
    {
        try {
            require_once __DIR__ . '/TenantSettingsManager.php';

            // Save to database
            return TenantSettingsManager::setMultipleTexts('customer', 'en', self::$customerTexts);
        } catch (Exception $e) {
            // Fallback to file-based saving for backward compatibility
            $customFile = DATA_DIR . '/custom_customer_texts.php';
            $content = "<?php\n/**\n * Custom Customer Texts\n * Generated: " . date('Y-m-d H:i:s') . "\n */\n\nreturn " . var_export(self::$customerTexts, true) . ";\n";
            return file_put_contents($customFile, $content) !== false;
        }
    }

    /**
     * Reset customer texts to defaults
     */
    public static function resetCustomerTexts(): bool
    {
        $customFile = DATA_DIR . '/custom_customer_texts.php';
        if (file_exists($customFile)) {
            unlink($customFile);
        }

        self::$customerTexts = null;
        self::loadCustomerTexts();

        return true;
    }

    /**
     * Check if multilingual is enabled (any secondary text is filled)
     */
    public static function isMultilingualEnabled(): bool
    {
        if (self::$customerTexts === null) {
            self::loadCustomerTexts();
        }

        $hasSecondary = false;
        foreach (self::$customerTexts as $key => $textData) {
            if (is_array($textData) && !empty($textData['secondary'])) {
                $hasSecondary = true;
                break;
            }
        }

        return $hasSecondary;
    }

    /**
     * Get all texts for a specific language
     */
    public static function getAllTextsForLanguage(string $language = 'primary'): array
    {
        if (self::$customerTexts === null) {
            self::loadCustomerTexts();
        }

        $result = [];
        foreach (self::$customerTexts as $key => $textData) {
            $result[$key] = self::getCustomerText($key, '', $language);
        }

        return $result;
    }

    /**
     * Get text categories for organization
     */
    public static function getCustomerTextCategories(): array
    {
        return [
            'general' => [
                'title' => 'General',
                'description' => 'Site titles, headers, and general messages',
                'keys' => ['site_title', 'welcome_title', 'welcome_subtitle', 'wellness_tagline', 'check_registration_message', 'footer_copyright', 'year_label', 'month_label']
            ],
            'email_verification' => [
                'title' => 'Email & Verification',
                'description' => 'Email entry and verification process',
                'keys' => ['email_step_title', 'email_step_subtitle', 'email_placeholder', 'email_button', 'verification_title', 'verification_subtitle', 'verification_code_placeholder', 'verification_button', 'verification_resend', 'email_verification_required', 'email_registered_message', 'access_hash_label', 'access_hash_placeholder', 'access_hash_help', 'verify_continue_button', 'verification_code_sent']
            ],
            'customer_flow' => [
                'title' => 'Customer Flow',
                'description' => 'New vs existing customer experience',
                'keys' => ['customer_choice_title', 'customer_choice_subtitle', 'book_new_appointment', 'book_new_subtitle', 'view_reservations', 'view_reservations_subtitle']
            ],
            'registration' => [
                'title' => 'Registration & Security',
                'description' => 'New customer account creation and security verification',
                'keys' => ['new_customer_title', 'new_customer_subtitle', 'full_name_label', 'full_name_placeholder', 'email_label', 'mobile_label', 'mobile_placeholder', 'create_account_button', 'create_account_continue_button', 'all_fields_required_error', 'invalid_email_error', 'invalid_mobile_error', 'account_created_success', 'security_verification_title', 'access_hash_instruction', 'access_hash_sent_info', 'continue_to_booking', 'no_access_hash_help']
            ],
            'booking' => [
                'title' => 'Booking Process',
                'description' => 'Appointment booking steps and messages',
                'keys' => ['booking_step1_title', 'booking_step2_title', 'booking_step3_title', 'service_select_placeholder', 'loading_times_message', 'review_book_button', 'confirm_reservation_button', 'reservation_confirmed_message']
            ],
            'reservations' => [
                'title' => 'My Reservations',
                'description' => 'Reservation viewing and management',
                'keys' => ['my_reservations_title', 'no_reservations_message', 'book_first_appointment', 'upcoming_reservations', 'past_reservations', 'cancel_reservation_button', 'book_another_button', 'cancel_reservation_title', 'make_new_booking_text', 'reservation_details', 'service_label', 'date_label', 'time_label', 'duration_label', 'price_label', 'status_label']
            ],
            'cancellation' => [
                'title' => 'Cancellation',
                'description' => 'Reservation cancellation process',
                'keys' => ['cancel_confirmation_title', 'cancel_confirmation_message', 'yes_cancel_button', 'keep_reservation_button', 'cancellation_success', 'cancellation_error', 'cancel_reservation_header', 'cancel_confirmation_text', 'keep_reservation_text', 'yes_cancel_text']
            ],
            'messages' => [
                'title' => 'Messages & Notifications',
                'description' => 'Success, error, and status messages',
                'keys' => ['booking_success', 'booking_error', 'general_error', 'network_error', 'session_expired', 'email_sent_success', 'verification_success', 'session_expired_error', 'failed_send_verification', 'failed_generate_code', 'customer_data_error']
            ],
            'buttons' => [
                'title' => 'Buttons & Navigation',
                'description' => 'Common buttons and navigation elements',
                'keys' => ['back_button', 'next_button', 'continue_button', 'cancel_button', 'close_button', 'save_button', 'edit_button', 'delete_button', 'refresh_button', 'back_button_text', 'send_verification_code', 'review_book_appointment']
            ],
            'javascript' => [
                'title' => 'JavaScript & Dynamic Content',
                'description' => 'Month names, day names, time periods, modal dialogs, and dynamic messages',
                'keys' => [
                    // Day names
                    'day_monday_short',
                    'day_tuesday_short',
                    'day_wednesday_short',
                    'day_thursday_short',
                    'day_friday_short',
                    'day_saturday_short',
                    'day_sunday_short',
                    // Month names
                    'month_january',
                    'month_february',
                    'month_march',
                    'month_april',
                    'month_may',
                    'month_june',
                    'month_july',
                    'month_august',
                    'month_september',
                    'month_october',
                    'month_november',
                    'month_december',
                    // Time periods
                    'time_morning',
                    'time_afternoon',
                    'time_evening',
                    'am_suffix',
                    'pm_suffix',
                    // JavaScript messages
                    'js_loading_days',
                    'js_error_loading_days',
                    'js_error_loading_times',
                    'js_error_sending_code',
                    'js_no_times_available',
                    'js_no_times_message',
                    'js_try_different_date',
                    'js_select_month',
                    'js_available_slot',
                    'js_available_slots',
                    // Modal texts
                    'js_confirm_appointment',
                    'js_review_booking_details',
                    'js_book_now_button',
                    'js_cancel_modal_button',
                    'js_confirmation_email_note'
                ]
            ]
        ];
    }
}

/**
 * Helper function to get customer text (shorthand)
 */
function customerText(string $key, string $default = ''): string
{
    // Get language from URL parameter, default to primary
    $language = $_GET['lang'] ?? 'primary';

    return TextManager::getCustomerText($key, $default, $language);
}

/**
 * Helper function to echo customer text
 */
function echoCustomerText(string $key, string $default = ''): void
{
    echo htmlspecialchars(customerText($key, $default));
}
