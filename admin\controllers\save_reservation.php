<?php

require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/reservation_handler.php';
require_once '../../includes/customer_handler.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$reservationId = sanitize_input($_POST['reservation_id'] ?? '');
$customerName = sanitize_input($_POST['customer_name'] ?? '');
$customerEmail = sanitize_input($_POST['customer_email'] ?? '');
$customerMobile = sanitize_input($_POST['customer_mobile'] ?? '');
$service = sanitize_input($_POST['service'] ?? '');
$date = sanitize_input($_POST['date'] ?? '');
$time = sanitize_input($_POST['time'] ?? '');
$employeeId = sanitize_input($_POST['employee_id'] ?? '');

if (!$reservationId || !$customerName || !$customerEmail || !$service || !$date || !$time) {
    echo json_encode(['success' => false, 'message' => 'All required fields must be filled']);
    exit;
}

$reservationHandler = new ReservationHandler();
$customerHandler = new CustomerHandler();

// Update customer info
$customerHandler->getOrCreateCustomer($customerName, $customerEmail, $customerMobile);

// Get customer data for email
$customer = $customerHandler->getCustomerByEmail($customerEmail);

// Prepare reservation data for email
$reservationData = [
    'id' => $reservationId,
    'service' => $service,
    'date' => $date,
    'time' => $time
];

// Update reservation
if (!method_exists($reservationHandler, 'updateReservation')) {
    echo json_encode(['success' => false, 'message' => 'Update function not implemented']);
    exit;
}

// Update reservation with employee assignment
$result = $reservationHandler->updateReservation(
    $reservationId,
    $date,
    $time,
    $service,
    $employeeId
);

if ($result['success'] ?? $result) {
    // Send confirmation email after successful update
    send_confirmation_email($customerEmail, $reservationData, $customer);

    echo json_encode(['success' => true, 'message' => 'Reservation updated successfully', 'refresh' => true]);
} else {
    echo json_encode(['success' => false, 'message' => $result['message'] ?? 'Failed to update reservation']);
}
