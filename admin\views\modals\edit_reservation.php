<?php

require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
require_once '../../../includes/reservation_handler.php';
require_once '../../../includes/customer_handler.php';
require_once '../../../includes/admin_functions.php';

$reservationId = $_GET['id'] ?? '';
$reservationHandler = new ReservationHandler();
$reservation = $reservationHandler->getReservationById($reservationId);

if (!$reservation) {
    echo '<div class="alert alert-error">Reservation not found.</div>';
    exit;
}

$services = getServices();
$employees = getEmployees();
$customerHandler = new CustomerHandler();
$customer = $customerHandler->getCustomerById($reservation['customer_id']);
?>

<form method="post" action="actions/save_reservation.php" data-validate>
    <input type="hidden" name="reservation_id" value="<?= htmlspecialchars($reservationId) ?>">
    <div class="form-group">
        <label>Customer Name</label>
        <input type="text" name="customer_name" class="form-control" value="<?= htmlspecialchars($customer['name'] ?? '') ?>" required>
    </div>
    <div class="form-group">
        <label>Customer Email</label>
        <input type="email" name="customer_email" class="form-control" value="<?= htmlspecialchars($customer['email'] ?? '') ?>" required>
    </div>
    <div class="form-group">
        <label>Customer Mobile</label>
        <input type="text" name="customer_mobile" class="form-control" value="<?= htmlspecialchars($customer['mobile'] ?? '') ?>">
    </div>
    <div class="form-group">
        <label>Service</label>
        <select name="service" class="form-control" required>
            <?php foreach ($services as $key => $svc): ?>
                <option value="<?= htmlspecialchars($key) ?>" <?= $reservation['service'] === $key ? 'selected' : '' ?>>
                    <?= htmlspecialchars($svc['name']) ?>
                </option>
            <?php endforeach; ?>
        </select>
    </div>
    <div class="form-group">
        <label>Employee</label>
        <select name="employee_id" class="form-control">
            <option value="">Auto-assign best available employee</option>
            <?php foreach ($employees as $empId => $employee): ?>
                <option value="<?= htmlspecialchars($empId) ?>" <?= ($reservation['employee_id'] ?? '') === $empId ? 'selected' : '' ?>>
                    <?= htmlspecialchars($employee['name']) ?>
                </option>
            <?php endforeach; ?>
        </select>
    </div>
    <div class="form-group">
        <label>Date</label>
        <input type="date" name="date" class="form-control" value="<?= htmlspecialchars($reservation['date']) ?>" required>
    </div>
    <div class="form-group">
        <label>Time</label>
        <input type="time" name="time" class="form-control" value="<?= htmlspecialchars($reservation['time']) ?>" required>
    </div>
    <button type="submit" class="btn btn-primary" data-original-text="Save">Save</button>
</form>