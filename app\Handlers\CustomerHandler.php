<?php

/**
 * Customer Handler Class
 *
 * Manages all customer-related operations including:
 * - Customer creation and updates
 * - Email verification and validation
 * - Customer data retrieval and statistics
 * - Security and privacy protection
 *
 * <AUTHOR> System
 * @version 1.0
 */
class CustomerHandler
{
    /** @var Database Database instance */
    private $db;

    /**
     * Initialize customer handler with database connection
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Check if email exists in database (tenant-aware)
     */
    public function emailExists(string $email): bool
    {
        $email = strtolower(trim($email));
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT COUNT(*) FROM customers WHERE email = :email AND tenant_id = :tenant_id");
        $stmt->bindValue(':email', $email);
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();

        $row = $result->fetchArray(SQLITE3_NUM);
        return $row && $row[0] > 0;
    }

    /**
     * Get or create customer from reservation data
     */
    public function getOrCreateCustomer(string $name, string $email, string $mobile, string $preferredLanguage = 'el'): array
    {
        $email = strtolower(trim($email));
        $userHash = get_user_hash($email);

        // Check if customer exists
        $existingCustomer = $this->getCustomerByEmail($email);

        if ($existingCustomer) {
            // Update customer info if needed
            $this->updateCustomer($existingCustomer['id'], $name, $email, $mobile);
            return $existingCustomer;
        }

        // Create new customer
        $customerId = generate_customer_id();
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("INSERT INTO customers
            (id, name, email, mobile, address, date_of_birth, notes, preferred_contact, preferred_language, total_reservations, total_spent, last_visit, created_at, user_hash, avatar_url, tenant_id)
            VALUES (:id, :name, :email, :mobile, :address, :date_of_birth, :notes, :preferred_contact, :preferred_language, :total_reservations, :total_spent, :last_visit, :created_at, :user_hash, :avatar_url, :tenant_id)");

        $createdAt = date('Y-m-d H:i:s');
        $stmt->bindValue(':id', $customerId);
        $stmt->bindValue(':name', $name);
        $stmt->bindValue(':email', $email);
        $stmt->bindValue(':mobile', $mobile);
        $stmt->bindValue(':address', '');
        $stmt->bindValue(':date_of_birth', '');
        $stmt->bindValue(':notes', '');
        $stmt->bindValue(':preferred_contact', 'email');
        $stmt->bindValue(':preferred_language', $preferredLanguage);
        $stmt->bindValue(':total_reservations', 0);
        $stmt->bindValue(':total_spent', 0);
        $stmt->bindValue(':last_visit', '');
        $stmt->bindValue(':created_at', $createdAt);
        $stmt->bindValue(':user_hash', $userHash);
        $stmt->bindValue(':avatar_url', '');
        $stmt->bindValue(':tenant_id', $tenantId);

        $stmt->execute();

        log_activity("New customer created: $customerId - $name ($email)");

        // Prepare customer array for return and emails
        $customer = [
            'id' => $customerId,
            'name' => $name,
            'email' => $email,
            'mobile' => $mobile,
            'address' => '',
            'date_of_birth' => '',
            'notes' => '',
            'preferred_contact' => 'email',
            'preferred_language' => $preferredLanguage,
            'total_reservations' => 0,
            'total_spent' => 0,
            'last_visit' => '',
            'created_at' => $createdAt,
            'user_hash' => $userHash,
            'avatar_url' => ''
        ];

        // Set customer language in session for verification emails
        $_SESSION['email_language'] = $preferredLanguage;
        $_SESSION['temp_customer_id'] = $customerId;

        // Send welcome email
        try {
            send_welcome_email($email, $customer);
        } catch (Exception $e) {
            // Don't fail customer creation if emails fail
            log_activity("Email error for new customer {$customer['email']}: " . $e->getMessage());
        }

        return $customer;
    }

    /**
     * Get customer by email (tenant-aware)
     */
    public function getCustomerByEmail(string $email): ?array
    {
        $email = strtolower(trim($email));
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT * FROM customers WHERE email = :email AND tenant_id = :tenant_id LIMIT 1");
        $stmt->bindValue(':email', $email);
        $stmt->bindValue(':tenant_id', $tenantId);

        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);

        if ($row) {
            return $row;
        }

        return null;
    }

    /**
     * Get customer by ID (tenant-aware)
     */
    public function getCustomerById(string $id): ?array
    {
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT * FROM customers WHERE id = :id AND tenant_id = :tenant_id LIMIT 1");
        $stmt->bindValue(':id', $id);
        $stmt->bindValue(':tenant_id', $tenantId);

        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);

        if ($row) {
            return $row;
        }

        return null;
    }

    /**
     * Get all customers (tenant-aware)
     */
    public function getAllCustomers(): array
    {
        $customers = [];
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT * FROM customers WHERE tenant_id = :tenant_id ORDER BY name");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $customers[] = $row;
        }

        return $customers;
    }

    /**
     * Get customers with pagination (tenant-aware)
     */
    public function getCustomersPaginated(int $limit = 20, int $offset = 0): array
    {
        $customers = [];
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT * FROM customers WHERE tenant_id = :tenant_id ORDER BY name LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':tenant_id', $tenantId);
        $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
        $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);

        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $customers[] = $row;
        }

        return $customers;
    }

    /**
     * Get total customer count (tenant-aware)
     */
    public function getCustomerCount(): int
    {
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM customers WHERE tenant_id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        return (int)($row['count'] ?? 0);
    }

    /**
     * Search customers with pagination (tenant-aware)
     */
    public function searchCustomers(string $searchTerm, int $limit = 20, int $offset = 0): array
    {
        $customers = [];
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $searchTerm = '%' . strtolower(trim($searchTerm)) . '%';

        $stmt = $conn->prepare("
            SELECT * FROM customers
            WHERE tenant_id = :tenant_id
              AND (LOWER(name) LIKE :search
               OR LOWER(email) LIKE :search
               OR LOWER(mobile) LIKE :search
               OR LOWER(address) LIKE :search
               OR LOWER(preferred_language) LIKE :search)
            ORDER BY name
            LIMIT :limit OFFSET :offset
        ");

        $stmt->bindValue(':tenant_id', $tenantId);
        $stmt->bindValue(':search', $searchTerm);
        $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
        $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);

        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $customers[] = $row;
        }

        return $customers;
    }

    /**
     * Get total count of search results (tenant-aware)
     */
    public function getSearchCustomerCount(string $searchTerm): int
    {
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $searchTerm = '%' . strtolower(trim($searchTerm)) . '%';

        $stmt = $conn->prepare("
            SELECT COUNT(*) as count FROM customers
            WHERE tenant_id = :tenant_id
               AND (LOWER(name) LIKE :search
               OR LOWER(email) LIKE :search
               OR LOWER(mobile) LIKE :search
               OR LOWER(address) LIKE :search
               OR LOWER(preferred_language) LIKE :search)
        ");

        $stmt->bindValue(':tenant_id', $tenantId);
        $stmt->bindValue(':search', $searchTerm);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);

        return (int)($row['count'] ?? 0);
    }

    /**
     * Update customer information
     */
    public function updateCustomer(string $id, string $name, string $email, string $mobile, string $address = '', string $dateOfBirth = '', string $notes = '', string $preferredContact = 'email', string $preferredLanguage = 'el'): array
    {
        // Validate inputs
        if (empty($name) || empty($email)) {
            return ['success' => false, 'message' => 'Name and email are required'];
        }

        if (!validate_email($email)) {
            return ['success' => false, 'message' => 'Invalid email format'];
        }

        // Check if customer exists
        $existingCustomer = $this->getCustomerById($id);
        if (!$existingCustomer) {
            return ['success' => false, 'message' => 'Customer not found'];
        }

        // Check if email is already used by another customer
        $emailCustomer = $this->getCustomerByEmail($email);
        if ($emailCustomer && $emailCustomer['id'] !== $id) {
            return ['success' => false, 'message' => 'Email is already used by another customer'];
        }

        $conn = $this->db->getConnection();

        $stmt = $conn->prepare("
            UPDATE customers
            SET name = :name, email = :email, mobile = :mobile, address = :address,
                date_of_birth = :date_of_birth, notes = :notes, preferred_contact = :preferred_contact,
                preferred_language = :preferred_language
            WHERE id = :id
        ");

        $stmt->bindValue(':id', $id);
        $stmt->bindValue(':name', $name);
        $stmt->bindValue(':email', $email);
        $stmt->bindValue(':mobile', $mobile);
        $stmt->bindValue(':address', $address);
        $stmt->bindValue(':date_of_birth', $dateOfBirth);
        $stmt->bindValue(':notes', $notes);
        $stmt->bindValue(':preferred_contact', $preferredContact);
        $stmt->bindValue(':preferred_language', $preferredLanguage);

        $result = $stmt->execute();

        if ($result !== false) {
            log_activity("Customer updated: $id - $name ($email)");
            return ['success' => true, 'message' => 'Customer updated successfully'];
        } else {
            log_activity("Failed to update customer: " . $conn->lastErrorMsg());
            return ['success' => false, 'message' => 'Failed to update customer: ' . $conn->lastErrorMsg()];
        }
    }

    /**
     * Delete a customer
     */
    public function deleteCustomer(string $id): array
    {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        $stmt = $conn->prepare("DELETE FROM customers WHERE id = :id");
        $stmt->bindValue(':id', $id);

        $result = $stmt->execute();

        if ($result !== false) {
            return ['success' => true, 'message' => 'Customer deleted successfully'];
        } else {
            return ['success' => false, 'message' => 'Failed to delete customer'];
        }
    }

    /**
     * Add a new customer
     */
    public function addCustomer(string $name, string $email, string $mobile, string $address = '', string $dateOfBirth = '', string $notes = '', string $preferredContact = 'email', string $preferredLanguage = 'el'): array
    {
        // Validate inputs
        if (empty($name) || empty($email)) {
            return ['success' => false, 'message' => 'Name and email are required'];
        }

        if (!validate_email($email)) {
            return ['success' => false, 'message' => 'Invalid email format'];
        }

        // Check if customer with this email already exists
        $existingCustomer = $this->getCustomerByEmail($email);
        if ($existingCustomer) {
            return ['success' => false, 'message' => 'A customer with this email already exists'];
        }

        // Generate customer ID and user hash
        $customerId = generate_customer_id();
        $userHash = get_user_hash($email);

        // Insert customer into database
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("
            INSERT INTO customers
            (id, name, email, mobile, address, date_of_birth, notes, preferred_contact, preferred_language, total_reservations, total_spent, last_visit, created_at, user_hash, avatar_url, tenant_id)
            VALUES
            (:id, :name, :email, :mobile, :address, :date_of_birth, :notes, :preferred_contact, :preferred_language, :total_reservations, :total_spent, :last_visit, :created_at, :user_hash, :avatar_url, :tenant_id)
        ");

        $createdAt = date('Y-m-d H:i:s');
        $stmt->bindValue(':id', $customerId);
        $stmt->bindValue(':name', $name);
        $stmt->bindValue(':email', $email);
        $stmt->bindValue(':mobile', $mobile);
        $stmt->bindValue(':address', $address);
        $stmt->bindValue(':date_of_birth', $dateOfBirth);
        $stmt->bindValue(':notes', $notes);
        $stmt->bindValue(':preferred_contact', $preferredContact);
        $stmt->bindValue(':preferred_language', $preferredLanguage);
        $stmt->bindValue(':total_reservations', 0);
        $stmt->bindValue(':total_spent', 0);
        $stmt->bindValue(':last_visit', '');
        $stmt->bindValue(':created_at', $createdAt);
        $stmt->bindValue(':user_hash', $userHash);
        $stmt->bindValue(':avatar_url', '');
        $stmt->bindValue(':tenant_id', $tenantId);

        $result = $stmt->execute();

        if (!$result) {
            log_activity("Failed to add customer: " . $conn->lastErrorMsg());
            return ['success' => false, 'message' => 'Database error: ' . $conn->lastErrorMsg()];
        }

        log_activity("New customer added: $customerId - $name ($email)");

        // Set customer language in session for verification emails
        $_SESSION['email_language'] = $preferredLanguage;
        $_SESSION['temp_customer_id'] = $customerId;

        // Send welcome email
        try {
            $customer = [
                'id' => $customerId,
                'name' => $name,
                'email' => $email,
                'mobile' => $mobile,
                'user_hash' => $userHash
            ];

            send_welcome_email($email, $customer);
        } catch (Exception $e) {
            log_activity("Failed to send welcome email to $email: " . $e->getMessage());
        }

        return [
            'success' => true,
            'message' => 'Customer added successfully',
            'customer_id' => $customerId
        ];
    }



    /**
     * Update customer statistics
     * 
     * @param string $customerId The ID of the customer to update
     * @param string|null $lastVisitDate Optional date to set as last visit
     * @return bool Success status
     */
    public function updateCustomerStats(string $customerId, ?string $lastVisitDate = null): bool
    {
        $conn = $this->db->getConnection();

        // Get current total reservations
        $stmt = $conn->prepare("SELECT total_reservations FROM customers WHERE id = :id");
        $stmt->bindValue(':id', $customerId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);

        if (!$row) {
            return false; // Customer not found
        }

        $totalReservations = (int)($row['total_reservations'] ?? 0) + 1;

        // Use provided date or current date
        $lastVisit = $lastVisitDate ?? date('Y-m-d');

        // Update customer stats
        $stmt = $conn->prepare("
            UPDATE customers 
            SET total_reservations = :total_reservations, last_visit = :last_visit
            WHERE id = :id
        ");

        $stmt->bindValue(':total_reservations', $totalReservations);
        $stmt->bindValue(':last_visit', $lastVisit);
        $stmt->bindValue(':id', $customerId);

        return (bool)$stmt->execute();
    }

    /**
     * Get customer with detailed statistics and reservations
     */
    public function getCustomerWithDetails(string $customerId): ?array
    {
        $customer = $this->getCustomerById($customerId);
        if (!$customer) {
            return null;
        }

        $conn = $this->db->getConnection();

        // Get reservation statistics
        $stmt = $conn->prepare("
            SELECT
                COUNT(*) as total_reservations,
                SUM(CASE WHEN status = 'completed' THEN price ELSE 0 END) as total_spent,
                SUM(CASE WHEN status = 'confirmed' AND date >= date('now') THEN 1 ELSE 0 END) as upcoming_reservations,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_reservations,
                MAX(date) as last_reservation_date
            FROM reservations
            WHERE customer_id = :customer_id
        ");
        $stmt->bindValue(':customer_id', $customerId);
        $result = $stmt->execute();
        $stats = $result->fetchArray(SQLITE3_ASSOC);

        // Get recent reservations (last 5)
        $stmt = $conn->prepare("
            SELECT r.*, s.name as service_name, s.duration as service_duration
            FROM reservations r
            LEFT JOIN services s ON r.service = s.id
            WHERE r.customer_id = :customer_id
            ORDER BY r.date DESC, r.time DESC
            LIMIT 5
        ");
        $stmt->bindValue(':customer_id', $customerId);
        $result = $stmt->execute();

        $recentReservations = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $recentReservations[] = $row;
        }

        // Get upcoming reservations
        $stmt = $conn->prepare("
            SELECT r.*, s.name as service_name, s.duration as service_duration
            FROM reservations r
            LEFT JOIN services s ON r.service = s.id
            WHERE r.customer_id = :customer_id
            AND r.date >= date('now')
            AND r.status = 'confirmed'
            ORDER BY r.date ASC, r.time ASC
        ");
        $stmt->bindValue(':customer_id', $customerId);
        $result = $stmt->execute();

        $upcomingReservations = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $upcomingReservations[] = $row;
        }

        // Calculate customer age if date of birth is available
        $age = null;
        if (!empty($customer['date_of_birth'])) {
            $birthDate = new DateTime($customer['date_of_birth']);
            $today = new DateTime();
            $age = $today->diff($birthDate)->y;
        }

        // Calculate customer since duration
        $customerSince = '';
        if (!empty($customer['created_at'])) {
            $createdDate = new DateTime($customer['created_at']);
            $today = new DateTime();
            $diff = $today->diff($createdDate);

            if ($diff->y > 0) {
                $customerSince = $diff->y . ' year' . ($diff->y > 1 ? 's' : '');
            } elseif ($diff->m > 0) {
                $customerSince = $diff->m . ' month' . ($diff->m > 1 ? 's' : '');
            } else {
                $customerSince = $diff->d . ' day' . ($diff->d > 1 ? 's' : '');
            }
        }

        return [
            'customer' => $customer,
            'stats' => $stats,
            'recent_reservations' => $recentReservations,
            'upcoming_reservations' => $upcomingReservations,
            'age' => $age,
            'customer_since' => $customerSince
        ];
    }
}
