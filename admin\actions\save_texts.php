<?php
require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/unified_text_manager.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $texts = $_POST['texts'] ?? [];
    $emailTexts = $_POST['email_texts'] ?? [];

    if (empty($texts) && empty($emailTexts)) {
        echo json_encode(['success' => false, 'message' => 'No texts provided']);
        exit;
    }

    $updateCount = 0;
    $success = true;
    $errors = [];
    $templatesRegenerated = false;

    // Handle customer texts
    if (!empty($texts)) {
        $sanitizedTexts = [];
        foreach ($texts as $key => $value) {
            // Basic validation - ensure key is alphanumeric with underscores
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $key)) {
                $errors[] = 'Invalid text key: ' . $key;
                continue;
            }

            // Handle multilingual format
            if (is_array($value)) {
                $sanitizedTexts[$key] = [
                    'primary' => trim($value['primary'] ?? ''),
                    'secondary' => trim($value['secondary'] ?? '')
                ];
            } else {
                // Handle old single-value format
                $sanitizedTexts[$key] = trim($value);
            }
        }

        if (!empty($sanitizedTexts)) {
            $customerSuccess = UnifiedTextManager::saveCustomerTexts($sanitizedTexts);
            if ($customerSuccess) {
                $updateCount += count($sanitizedTexts);
            } else {
                $success = false;
                $errors[] = 'Failed to save customer texts';
            }
        }
    }

    // Handle email texts
    if (!empty($emailTexts)) {
        // Organize email texts by language
        $emailTextsByLang = ['en' => [], 'el' => []];

        foreach ($emailTexts as $key => $languages) {
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $key)) {
                $errors[] = 'Invalid email text key: ' . $key;
                continue;
            }

            if (is_array($languages)) {
                if (isset($languages['en'])) {
                    $emailTextsByLang['en'][$key] = trim($languages['en']);
                }
                if (isset($languages['el'])) {
                    $emailTextsByLang['el'][$key] = trim($languages['el']);
                }
            }
        }

        // Save email texts using unified manager
        if (!empty($emailTextsByLang['en']) || !empty($emailTextsByLang['el'])) {
            // Get existing email texts first
            $existingTexts = UnifiedTextManager::getEmailTexts();

            // Merge new texts with existing texts (preserve other email types)
            $mergedTexts = $existingTexts;

            // Update only the submitted texts
            foreach ($emailTextsByLang['en'] as $key => $value) {
                $mergedTexts['en'][$key] = $value;
            }
            foreach ($emailTextsByLang['el'] as $key => $value) {
                $mergedTexts['el'][$key] = $value;
            }

            $emailSuccess = UnifiedTextManager::saveEmailTexts($mergedTexts);
            if ($emailSuccess) {
                $updateCount += count($emailTextsByLang['en']) + count($emailTextsByLang['el']);
                $templatesRegenerated = true;
            } else {
                $success = false;
                $errors[] = 'Failed to save email texts';
            }
        }
    }

    if ($success && $updateCount > 0) {
        $message = 'Texts updated successfully';

        if ($templatesRegenerated) {
            $message .= '. Email templates automatically regenerated.';
        }

        echo json_encode([
            'success' => true,
            'message' => $message,
            'updated_count' => $updateCount,
            'templates_regenerated' => $templatesRegenerated
        ]);
    } else if (!empty($errors)) {
        echo json_encode([
            'success' => false,
            'message' => 'Errors occurred: ' . implode(', ', $errors)
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'No valid texts to save']);
    }
} catch (Exception $e) {
    error_log('Text save error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server error occurred']);
}
