<?php
require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/Database.php';
require_once '../../../includes/customer_handler.php';
require_once '../../../includes/reservation_handler.php';

// System status for database-based project

$phpVersion = phpversion();
$freeSpace = disk_free_space(__DIR__);
$totalSpace = disk_total_space(__DIR__);
$usedSpace = $totalSpace - $freeSpace;
$diskUsagePercent = round(($usedSpace / $totalSpace) * 100, 1);

// Database status
$dbStatus = 'Unknown';
$dbSize = 0;
$dbError = '';
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    if ($conn) {
        $dbStatus = 'Connected';
        // Get database file size if it's SQLite
        if (file_exists(DB_FILE)) {
            $dbSize = filesize(DB_FILE);
        }
    }
} catch (Exception $e) {
    $dbStatus = 'Error';
    $dbError = $e->getMessage();
}

// Get data statistics
$customerHandler = new CustomerHandler();
$reservationHandler = new ReservationHandler();

$totalCustomers = 0;
$totalReservations = 0;
$todayReservations = 0;
$upcomingReservations = 0;

try {
    $customers = $customerHandler->getAllCustomers();
    $totalCustomers = count($customers);

    $reservations = $reservationHandler->getAllReservations();
    $totalReservations = count($reservations);

    $today = date('Y-m-d');
    $todayReservations = count($reservationHandler->getReservationsByDate($today));

    // Count upcoming reservations
    foreach ($reservations as $reservation) {
        if ($reservation['date'] >= $today && $reservation['status'] === 'confirmed') {
            $upcomingReservations++;
        }
    }
} catch (Exception $e) {
    // Handle errors silently for status display
}

// System health indicators
$healthChecks = [
    'php' => [
        'name' => 'PHP Version',
        'status' => version_compare($phpVersion, '7.4.0', '>=') ? 'good' : 'warning',
        'value' => $phpVersion,
        'icon' => 'fa-code'
    ],
    'database' => [
        'name' => 'Database',
        'status' => $dbStatus === 'Connected' ? 'good' : 'error',
        'value' => $dbStatus,
        'icon' => 'fa-database'
    ],
    'disk' => [
        'name' => 'Disk Usage',
        'status' => $diskUsagePercent < 80 ? 'good' : ($diskUsagePercent < 95 ? 'warning' : 'error'),
        'value' => $diskUsagePercent . '%',
        'icon' => 'fa-hdd'
    ],
    'memory' => [
        'name' => 'Memory Limit',
        'status' => 'good',
        'value' => ini_get('memory_limit'),
        'icon' => 'fa-memory'
    ]
];

// Calculate overall system health
$healthScore = 0;
$totalChecks = count($healthChecks);
foreach ($healthChecks as $check) {
    if ($check['status'] === 'good') $healthScore += 100;
    elseif ($check['status'] === 'warning') $healthScore += 60;
    else $healthScore += 0;
}
$overallHealth = round($healthScore / $totalChecks);
$overallStatus = $overallHealth >= 80 ? 'good' : ($overallHealth >= 60 ? 'warning' : 'error');
?>

<div class="system-status-enhanced">
    <!-- Overall Health Header -->
    <div class="health-header">
        <div class="health-indicator">
            <div class="health-circle health-<?= $overallStatus ?>">
                <span class="health-percentage"><?= $overallHealth ?>%</span>
            </div>
            <div class="health-info">
                <h3>System Health</h3>
                <p class="health-status status-<?= $overallStatus ?>">
                    <?= $overallStatus === 'good' ? 'All Systems Operational' : ($overallStatus === 'warning' ? 'Some Issues Detected' : 'Critical Issues Found') ?>
                </p>
            </div>
        </div>
        <div class="last-updated">
            <i class="fas fa-clock"></i>
            <span>Last updated: <?= date('M j, Y \a\t H:i') ?></span>
        </div>
    </div>

    <!-- System Components Grid -->
    <div class="status-grid">
        <?php foreach ($healthChecks as $key => $check): ?>
            <div class="status-card status-<?= $check['status'] ?>">
                <div class="status-icon">
                    <i class="fas <?= $check['icon'] ?>"></i>
                </div>
                <div class="status-content">
                    <h5><?= $check['name'] ?></h5>
                    <p class="status-value"><?= htmlspecialchars($check['value']) ?></p>
                    <div class="status-indicator">
                        <span class="indicator-dot indicator-<?= $check['status'] ?>"></span>
                        <span class="indicator-text"><?= ucfirst($check['status']) ?></span>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Detailed Information -->
    <div class="details-grid">
        <!-- Storage Information -->
        <div class="detail-card">
            <div class="card-header">
                <h4><i class="fas fa-hdd"></i> Storage Information</h4>
            </div>
            <div class="card-body">
                <div class="storage-info">
                    <div class="storage-item">
                        <span class="storage-label">Total Space:</span>
                        <span class="storage-value"><?= formatBytes($totalSpace) ?></span>
                    </div>
                    <div class="storage-item">
                        <span class="storage-label">Used Space:</span>
                        <span class="storage-value"><?= formatBytes($usedSpace) ?></span>
                    </div>
                    <div class="storage-item">
                        <span class="storage-label">Free Space:</span>
                        <span class="storage-value"><?= formatBytes($freeSpace) ?></span>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?= $diskUsagePercent ?>%"></div>
                </div>
                <p class="progress-text"><?= $diskUsagePercent ?>% used</p>
            </div>
        </div>

        <!-- Database Information -->
        <div class="detail-card">
            <div class="card-header">
                <h4><i class="fas fa-database"></i> Database Information</h4>
            </div>
            <div class="card-body">
                <div class="db-info">
                    <div class="db-item">
                        <span class="db-label">Status:</span>
                        <span class="db-value status-<?= $dbStatus === 'Connected' ? 'good' : 'error' ?>">
                            <?= htmlspecialchars($dbStatus) ?>
                        </span>
                    </div>
                    <?php if ($dbSize > 0): ?>
                        <div class="db-item">
                            <span class="db-label">Database Size:</span>
                            <span class="db-value"><?= formatBytes($dbSize) ?></span>
                        </div>
                    <?php endif; ?>
                    <?php if ($dbError): ?>
                        <div class="db-item">
                            <span class="db-label">Error:</span>
                            <span class="db-value error"><?= htmlspecialchars($dbError) ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Statistics -->
    <div class="stats-section">
        <div class="card-header">
            <h4><i class="fas fa-chart-bar"></i> Data Statistics</h4>
        </div>
        <div class="card-body">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number"><?= number_format($totalCustomers) ?></span>
                        <span class="stat-label">Total Customers</span>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number"><?= number_format($totalReservations) ?></span>
                        <span class="stat-label">Total Reservations</span>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number"><?= number_format($todayReservations) ?></span>
                        <span class="stat-label">Today's Reservations</span>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <span class="stat-number"><?= number_format($upcomingReservations) ?></span>
                        <span class="stat-label">Upcoming Reservations</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
function formatBytes($bytes, $precision = 2)
{
    $units = array('B', 'KB', 'MB', 'GB', 'TB');

    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}
?>