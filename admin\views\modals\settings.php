<?php
require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

// Get tenant-aware settings with error handling
try {
    $settings = getSettings();
} catch (Exception $e) {
    error_log('Settings modal error: ' . $e->getMessage());
    // Fallback to default settings
    $settings = [
        'site_name' => 'Booking System',
        'email_from' => '<EMAIL>',
        'admin_email' => '<EMAIL>',
        'business_hours' => [],
        'special_days' => []
    ];
}

// Prepare business hours for display
$businessHours = $settings['business_hours'] ?? [];
$days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

// Prepare special days text
$specialDays = $settings['special_days'] ?? [];
$specialDaysText = '';
foreach ($specialDays as $date => $intervals) {
    if (empty($intervals)) {
        $specialDaysText .= $date . ' | ';
    } else {
        $specialDaysText .= $date . ' ';
        foreach ($intervals as $interval) {
            $specialDaysText .= $interval['start'] . '-' . $interval['end'] . ' ';
        }
        $specialDaysText .= '| ';
    }
}
$specialDaysText = rtrim($specialDaysText, ' | ');

?>

<div class="settings-modal-content">
    <form method="post" action="actions/save_settings.php" data-validate>
        <!-- Basic Settings -->
        <div class="form-section">
            <h3>Basic Settings</h3>
            <div class="form-group">
                <label>Site Name</label>
                <input type="text" name="site_name" class="form-control" value="<?= htmlspecialchars($settings['site_name'] ?? '') ?>" required>
            </div>
            <div class="form-group">
                <label>Email From</label>
                <input type="email" name="email_from" class="form-control" value="<?= htmlspecialchars($settings['email_from'] ?? '') ?>">
            </div>
            <div class="form-group">
                <label>Admin Email</label>
                <input type="email" name="admin_email" class="form-control" value="<?= htmlspecialchars($settings['admin_email'] ?? '') ?>" required>
            </div>
        </div>

        <!-- Business Hours -->
        <div class="form-section">
            <h3>Business Hours</h3>
            <div class="business-hours-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Day</th>
                            <th>First Period</th>
                            <th>Second Period</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($days as $day): ?>
                            <?php
                            $dayHours = $businessHours[$day] ?? [];
                            $period1 = $dayHours[0] ?? ['start' => '', 'end' => ''];
                            $period2 = $dayHours[1] ?? ['start' => '', 'end' => ''];
                            ?>
                            <tr>
                                <td><strong><?= $day ?></strong></td>
                                <td>
                                    <input type="time" name="bh_<?= $day ?>_start1" value="<?= htmlspecialchars($period1['start']) ?>" class="form-control time-input">
                                    <span>to</span>
                                    <input type="time" name="bh_<?= $day ?>_end1" value="<?= htmlspecialchars($period1['end']) ?>" class="form-control time-input">
                                </td>
                                <td>
                                    <input type="time" name="bh_<?= $day ?>_start2" value="<?= htmlspecialchars($period2['start']) ?>" class="form-control time-input">
                                    <span>to</span>
                                    <input type="time" name="bh_<?= $day ?>_end2" value="<?= htmlspecialchars($period2['end']) ?>" class="form-control time-input">
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Special Days -->
        <div class="form-section">
            <h3>Special Days & Holidays</h3>
            <div class="form-group">
                <label for="special_days_text">Special Days Configuration</label>
                <textarea name="special_days_text" id="special_days_text" rows="4" class="form-control"
                    placeholder="2024-12-25 | 2024-12-24 09:00-14:00"><?= htmlspecialchars($specialDaysText) ?></textarea>
                <small class="form-text text-muted">Format: YYYY-MM-DD for closed days, YYYY-MM-DD HH:MM-HH:MM for special hours. Separate with |</small>
            </div>
        </div>

        <button type="submit" class="btn btn-primary" data-original-text="Save">Save Settings</button>
    </form>
</div>

<style>
    .settings-modal-content {
        max-height: 70vh;
        overflow-y: auto;
    }

    .form-section {
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .form-section h3 {
        margin-bottom: 15px;
        color: #333;
        font-size: 16px;
    }

    .business-hours-table table {
        width: 100%;
        font-size: 14px;
    }

    .business-hours-table td {
        padding: 8px;
        vertical-align: middle;
    }

    .time-input {
        width: 80px;
        display: inline-block;
        margin: 0 5px;
    }

    .business-hours-table td span {
        margin: 0 5px;
        font-size: 12px;
        color: #666;
    }
</style>