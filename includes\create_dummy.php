<?php

/**
 * Dummy Data Creation for GK Radevou Reservation System
 * 
 * This file contains all sample/dummy data creation logic.
 * It's separated from the main database initialization to allow
 * optional creation of test data.
 * 
 * Usage:
 * - Include this file and call createDummyData() when needed
 * - Comment out the call in Database.php to disable dummy data
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/functions.php';

/**
 * Create all dummy data for testing and demonstration
 * 
 * @return bool True if successful, false otherwise
 */
function createDummyData(): bool
{
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        echo "Creating dummy data...\n";

        // Get default tenant ID for dummy data
        $defaultTenantId = null;
        try {
            $result = $conn->query("SELECT id FROM tenants WHERE subdomain = 'default' LIMIT 1");
            $tenant = $result->fetchArray(SQLITE3_ASSOC);
            $defaultTenantId = $tenant ? $tenant['id'] : 'TN-DEFAULT001';
        } catch (Exception $e) {
            $defaultTenantId = 'TN-DEFAULT001';
        }

        // Insert 15 sample services
        $serviceTemplates = [
            ['name' => 'Relaxing Massage', 'duration' => 60, 'price' => 50.00, 'description' => 'A full body relaxing massage to relieve stress and tension'],
            ['name' => 'Deep Cleansing Facial', 'duration' => 45, 'price' => 35.00, 'description' => 'Professional facial treatment for all skin types'],
            ['name' => 'Classic Manicure', 'duration' => 30, 'price' => 25.00, 'description' => 'Professional nail care and polish application'],
            ['name' => 'Luxury Pedicure', 'duration' => 45, 'price' => 30.00, 'description' => 'Complete foot care with relaxing massage'],
            ['name' => 'Hot Stone Massage', 'duration' => 90, 'price' => 75.00, 'description' => 'Therapeutic massage using heated stones'],
            ['name' => 'Anti-Aging Facial', 'duration' => 60, 'price' => 45.00, 'description' => 'Advanced facial treatment for mature skin'],
            ['name' => 'Gel Manicure', 'duration' => 40, 'price' => 35.00, 'description' => 'Long-lasting gel polish manicure'],
            ['name' => 'Swedish Massage', 'duration' => 60, 'price' => 55.00, 'description' => 'Classic Swedish massage technique'],
            ['name' => 'Hydrating Facial', 'duration' => 50, 'price' => 40.00, 'description' => 'Moisturizing facial for dry skin'],
            ['name' => 'French Manicure', 'duration' => 35, 'price' => 28.00, 'description' => 'Classic French style manicure'],
            ['name' => 'Aromatherapy Massage', 'duration' => 75, 'price' => 65.00, 'description' => 'Relaxing massage with essential oils'],
            ['name' => 'Acne Treatment Facial', 'duration' => 55, 'price' => 42.00, 'description' => 'Specialized treatment for acne-prone skin'],
            ['name' => 'Spa Pedicure', 'duration' => 50, 'price' => 35.00, 'description' => 'Luxury pedicure with exfoliation and massage'],
            ['name' => 'Express Facial', 'duration' => 30, 'price' => 25.00, 'description' => 'Quick refreshing facial treatment'],
            ['name' => 'Couples Massage', 'duration' => 60, 'price' => 100.00, 'description' => 'Relaxing massage for two people']
        ];

        $services = [];
        foreach ($serviceTemplates as $index => $template) {
            // Mix of auto-assign and customer choice services for testing
            $allowEmployeeSelection = ($index % 3 === 0) ? 1 : 0; // Every 3rd service allows customer choice

            $services[] = [
                'id' => generate_service_id(),
                'name' => $template['name'],
                'duration' => $template['duration'],
                'price' => $template['price'],
                'description' => $template['description'],
                'allow_employee_selection' => $allowEmployeeSelection
            ];
        }

        foreach ($services as $service) {
            $stmt = $conn->prepare("
                INSERT INTO services (id, name, duration, price, description, allow_employee_selection, tenant_id)
                VALUES (:id, :name, :duration, :price, :description, :allow_employee_selection, :tenant_id)
            ");
            $stmt->bindValue(':id', $service['id']);
            $stmt->bindValue(':name', $service['name']);
            $stmt->bindValue(':duration', $service['duration']);
            $stmt->bindValue(':price', $service['price']);
            $stmt->bindValue(':description', $service['description']);
            $stmt->bindValue(':allow_employee_selection', $service['allow_employee_selection']);
            $stmt->bindValue(':tenant_id', $defaultTenantId);
            $stmt->execute();
        }
        echo "✓ Sample services created\n";

        // Insert 8 sample employees
        $employeeTemplates = [
            ['name' => 'Maria Papadopoulos', 'email' => '<EMAIL>', 'phone' => '+30 6901234567'],
            ['name' => 'Dimitris Kostas', 'email' => '<EMAIL>', 'phone' => '+30 6901234568'],
            ['name' => 'Elena Georgiou', 'email' => '<EMAIL>', 'phone' => '+30 6901234569'],
            ['name' => 'Nikos Stavros', 'email' => '<EMAIL>', 'phone' => '+30 6901234570'],
            ['name' => 'Sofia Alexandrou', 'email' => '<EMAIL>', 'phone' => '+30 6901234571'],
            ['name' => 'Yannis Petrou', 'email' => '<EMAIL>', 'phone' => '+30 6901234572'],
            ['name' => 'Christina Michail', 'email' => '<EMAIL>', 'phone' => '+30 6901234573'],
            ['name' => 'Andreas Nikolaou', 'email' => '<EMAIL>', 'phone' => '+30 6901234574']
        ];

        $settings = include __DIR__ . '/settings.php';
        $workingHours = json_encode($settings['business_hours'] ?? []);
        $employees = [];

        foreach ($employeeTemplates as $template) {
            $employeeId = generate_employee_id();
            $employees[] = $employeeId;

            $stmt = $conn->prepare("
                INSERT INTO employees (id, name, email, phone, working_hours, status, created_at, tenant_id)
                VALUES (:id, :name, :email, :phone, :working_hours, :status, :created_at, :tenant_id)
            ");
            $stmt->bindValue(':id', $employeeId);
            $stmt->bindValue(':name', $template['name']);
            $stmt->bindValue(':email', $template['email']);
            $stmt->bindValue(':phone', $template['phone']);
            $stmt->bindValue(':working_hours', $workingHours);
            $stmt->bindValue(':status', 'active');
            $stmt->bindValue(':created_at', date('Y-m-d H:i:s', strtotime('-' . rand(1, 90) . ' days')));
            $stmt->bindValue(':tenant_id', $defaultTenantId);
            $stmt->execute();
        }
        echo "✓ Sample employees created\n";

        // Assign services to employees (each employee gets 3-8 random services)
        foreach ($employees as $employeeId) {
            $numServices = rand(3, 8);
            $assignedServices = array_rand($services, $numServices);
            if (!is_array($assignedServices)) $assignedServices = [$assignedServices];

            foreach ($assignedServices as $serviceIndex) {
                $stmt = $conn->prepare("
                    INSERT INTO employee_services (employee_id, service_id)
                    VALUES (:employee_id, :service_id)
                ");
                $stmt->bindValue(':employee_id', $employeeId);
                $stmt->bindValue(':service_id', $services[$serviceIndex]['id']);
                $stmt->execute();
            }
        }
        echo "✓ Employee-service assignments created\n";

        // Insert 25 sample customers
        $customerTemplates = [
            ['name' => 'Georgios Ktistakis', 'email' => '<EMAIL>', 'mobile' => '+30 6912345678', 'city' => 'Athens'],
            ['name' => 'Maria Papadopoulos', 'email' => '<EMAIL>', 'mobile' => '+30 6912345679', 'city' => 'Thessaloniki'],
            ['name' => 'Dimitris Kostas', 'email' => '<EMAIL>', 'mobile' => '+30 6912345680', 'city' => 'Patras'],
            ['name' => 'Elena Georgiou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345681', 'city' => 'Athens'],
            ['name' => 'Nikos Stavros', 'email' => '<EMAIL>', 'mobile' => '+30 6912345682', 'city' => 'Heraklion'],
            ['name' => 'Sofia Alexandrou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345683', 'city' => 'Athens'],
            ['name' => 'Yannis Petrou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345684', 'city' => 'Thessaloniki'],
            ['name' => 'Christina Michail', 'email' => '<EMAIL>', 'mobile' => '+30 6912345685', 'city' => 'Volos'],
            ['name' => 'Andreas Nikolaou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345686', 'city' => 'Athens'],
            ['name' => 'Katerina Dimitriou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345687', 'city' => 'Larissa'],
            ['name' => 'Panagiotis Ioannou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345688', 'city' => 'Athens'],
            ['name' => 'Despina Christou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345689', 'city' => 'Rhodes'],
            ['name' => 'Kostas Antoniou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345690', 'city' => 'Athens'],
            ['name' => 'Ioanna Vasileiou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345691', 'city' => 'Kavala'],
            ['name' => 'Spyros Karagiannis', 'email' => '<EMAIL>', 'mobile' => '+30 6912345692', 'city' => 'Athens'],
            ['name' => 'Anna Theodorou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345693', 'city' => 'Chania'],
            ['name' => 'Michalis Pappas', 'email' => '<EMAIL>', 'mobile' => '+30 6912345694', 'city' => 'Athens'],
            ['name' => 'Eleni Konstantinou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345695', 'city' => 'Ioannina'],
            ['name' => 'Thanasis Makris', 'email' => '<EMAIL>', 'mobile' => '+30 6912345696', 'city' => 'Athens'],
            ['name' => 'Vicky Panou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345697', 'city' => 'Corfu'],
            ['name' => 'Alexandros Giannis', 'email' => '<EMAIL>', 'mobile' => '+30 6912345698', 'city' => 'Athens'],
            ['name' => 'Fotini Lambrou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345699', 'city' => 'Mytilene'],
            ['name' => 'Giorgos Manolis', 'email' => '<EMAIL>', 'mobile' => '+30 6912345700', 'city' => 'Athens'],
            ['name' => 'Stella Komninou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345701', 'city' => 'Tripoli'],
            ['name' => 'Vasilis Economou', 'email' => '<EMAIL>', 'mobile' => '+30 6912345702', 'city' => 'Athens']
        ];

        $customers = [];
        foreach ($customerTemplates as $i => $template) {
            $customerId = generate_customer_id();
            $customers[] = $customerId;

            $totalReservations = rand(0, 8);
            $totalSpent = $totalReservations * rand(25, 75);
            $lastVisit = $totalReservations > 0 ? date('Y-m-d', strtotime('-' . rand(1, 60) . ' days')) : '';

            $customer = [
                'id' => $customerId,
                'name' => $template['name'],
                'email' => $template['email'],
                'mobile' => $template['mobile'],
                'address' => rand(1, 999) . ' ' . ['Kifisias Avenue', 'Vouliagmenis Avenue', 'Panepistimiou Street', 'Ermou Street'][rand(0, 3)] . ', ' . $template['city'] . ', Greece',
                'date_of_birth' => date('Y-m-d', strtotime('-' . rand(20, 65) . ' years -' . rand(1, 365) . ' days')),
                'notes' => ['', 'Prefers morning appointments', 'Regular customer', 'Allergic to certain products', 'VIP customer'][rand(0, 4)],
                'preferred_contact' => ['email', 'mobile'][rand(0, 1)],
                'preferred_language' => 'el',
                'total_reservations' => $totalReservations,
                'total_spent' => $totalSpent,
                'last_visit' => $lastVisit,
                'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 180) . ' days')),
                'user_hash' => hash('sha256', $template['email'] . time() . $i),
                'avatar_url' => '',
                'tenant_id' => $defaultTenantId
            ];

            $stmt = $conn->prepare("
                INSERT INTO customers
                (id, name, email, mobile, address, date_of_birth, notes, preferred_contact, preferred_language, total_reservations, total_spent, last_visit, created_at, user_hash, avatar_url, tenant_id)
                VALUES
                (:id, :name, :email, :mobile, :address, :date_of_birth, :notes, :preferred_contact, :preferred_language, :total_reservations, :total_spent, :last_visit, :created_at, :user_hash, :avatar_url, :tenant_id)
            ");

            foreach ($customer as $key => $value) {
                $stmt->bindValue(':' . $key, $value);
            }
            $stmt->execute();
        }
        echo "✓ Sample customers created\n";

        // Insert 50 sample reservations
        $timeSlots = ['09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '12:00', '12:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30', '18:00'];
        $statusWeights = [40, 35, 10, 15]; // Percentage weights for each status

        for ($i = 0; $i < 50; $i++) {
            $reservationId = generate_reservation_id();
            $customerId = $customers[array_rand($customers)];
            $service = $services[array_rand($services)];
            $employeeId = $employees[array_rand($employees)];

            // Weighted random status selection
            $rand = rand(1, 100);
            $status = 'confirmed';
            if ($rand <= $statusWeights[3]) $status = 'pending';
            elseif ($rand <= $statusWeights[3] + $statusWeights[2]) $status = 'cancelled';
            elseif ($rand <= $statusWeights[3] + $statusWeights[2] + $statusWeights[1]) $status = 'completed';

            // Generate date based on status
            if ($status === 'completed') {
                $date = date('Y-m-d', strtotime('-' . rand(1, 90) . ' days'));
            } elseif ($status === 'cancelled') {
                $date = date('Y-m-d', strtotime(rand(-30, 30) . ' days'));
            } else {
                $date = date('Y-m-d', strtotime('+' . rand(1, 60) . ' days'));
            }

            $time = $timeSlots[array_rand($timeSlots)];
            $createdDaysAgo = rand(1, 120);

            $reservation = [
                'id' => $reservationId,
                'customer_id' => $customerId,
                'service' => $service['id'],
                'date' => $date,
                'time' => $time,
                'duration' => $service['duration'],
                'price' => $service['price'],
                'status' => $status,
                'employee_id' => $employeeId,
                'created_at' => date('Y-m-d H:i:s', strtotime('-' . $createdDaysAgo . ' days')),
                'tenant_id' => $defaultTenantId
            ];

            $stmt = $conn->prepare("
                INSERT INTO reservations
                (id, customer_id, service, date, time, duration, price, status, employee_id, created_at, tenant_id)
                VALUES
                (:id, :customer_id, :service, :date, :time, :duration, :price, :status, :employee_id, :created_at, :tenant_id)
            ");

            foreach ($reservation as $key => $value) {
                $stmt->bindValue(':' . $key, $value);
            }
            $stmt->execute();
        }
        echo "✓ Sample reservations created\n";

        echo "✅ All dummy data created successfully!\n";
        return true;
    } catch (Exception $e) {
        echo "❌ Error creating dummy data: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * Check if dummy data already exists
 *
 * @return bool True if dummy data exists
 */
function dummyDataExists(): bool
{
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Check if any customers exist (indicating dummy data was created)
        // We check for specific dummy emails since IDs are now random
        $result = $conn->query("SELECT COUNT(*) as count FROM customers WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')");
        $row = $result->fetchArray(SQLITE3_ASSOC);

        return $row['count'] > 0;
    } catch (Exception) {
        return false;
    }
}

// Allow direct execution for testing
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    echo "=== Dummy Data Creator ===\n\n";

    if (dummyDataExists()) {
        echo "⚠️  Dummy data already exists. Skipping creation.\n";
        echo "To recreate, delete the database file first.\n";
    } else {
        createDummyData();
    }

    echo "\n=== Complete ===\n";
}
