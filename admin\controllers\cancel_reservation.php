<?php
require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/reservation_handler.php';
require_once '../../includes/customer_handler.php';
require_once '../../includes/email_templates/email_sender.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$reservationId = sanitize_input($data['reservation_id'] ?? '');

if (!$reservationId) {
    echo json_encode(['success' => false, 'message' => 'Reservation ID is required']);
    exit;
}

$reservationHandler = new ReservationHandler();
if (!method_exists($reservationHandler, 'cancelReservation')) {
    echo json_encode(['success' => false, 'message' => 'Cancel function not implemented']);
    exit;
}

// Get reservation details before cancellation for emails
$reservation = $reservationHandler->getReservationById($reservationId);
if (!$reservation) {
    echo json_encode(['success' => false, 'message' => 'Reservation not found']);
    exit;
}

// Get customer details for emails
$customerHandler = new CustomerHandler();
$customer = $customerHandler->getCustomerById($reservation['customer_id']);

// Cancel the reservation
$result = $reservationHandler->cancelReservation($reservationId, null, 'Cancelled by admin', false, true);

if ($result['success']) {
    // Send emails (cancellation to customer + notification to admin)
    if ($customer && !empty($customer['email'])) {
        try {
            $reservationData = [
                'id' => $reservation['id'],
                'service' => $reservation['service'],
                'date' => $reservation['date'],
                'time' => $reservation['time'],
                'duration' => $reservation['duration'],
                'price' => $reservation['price']
            ];

            // Send cancellation email to customer using modern EmailSender
            $emailSender = new EmailSender();
            $cancellationSent = $emailSender->sendCancellationEmail($customer['email'], $reservationData, $customer);
            if ($cancellationSent) {
                log_activity("Cancellation email sent successfully to {$customer['email']} for reservation {$reservationId} (cancelled by admin)");
            } else {
                log_activity("Failed to send cancellation email to {$customer['email']} for reservation {$reservationId} (cancelled by admin)");
            }

            // Send admin notification using modern EmailSender
            $adminNotificationSent = $emailSender->sendAdminCancellationNotification($reservationData, $customer);
            if ($adminNotificationSent) {
                log_activity("Admin cancellation notification sent successfully for reservation {$reservationId} (cancelled by admin)");
            } else {
                log_activity("Failed to send admin cancellation notification for reservation {$reservationId} (cancelled by admin)");
            }
        } catch (Exception $e) {
            log_activity("Email error for admin cancellation {$reservationId}: " . $e->getMessage());
        }
    }

    echo json_encode(['success' => true, 'message' => $result['message']]);
} else {
    echo json_encode(['success' => false, 'message' => $result['message']]);
}
