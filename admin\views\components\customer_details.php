<?php

/**
 * Simplified Customer Details Modal
 */
require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
require_once '../../../includes/customer_handler.php';

$customerId = $_GET['id'] ?? '';
$customerHandler = new CustomerHandler();
$customerDetails = $customerHandler->getCustomerWithDetails($customerId);

if (!$customerDetails) {
    echo '<div class="alert alert-error">Customer not found.</div>';
    exit;
}

$customer = $customerDetails['customer'];
$stats = $customerDetails['stats'];
$recentReservations = $customerDetails['recent_reservations'];
$upcomingReservations = $customerDetails['upcoming_reservations'];

// Generate initials for avatar
$initials = '';
$nameParts = explode(' ', $customer['name']);
foreach ($nameParts as $part) {
    if (!empty($part)) {
        $initials .= strtoupper($part[0]);
    }
}
$initials = substr($initials, 0, 2);
?>

<div class="customer-details">
    <!-- Customer Header -->
    <div class="customer-header">
        <div class="customer-avatar">
            <div class="avatar-initials"><?= $initials ?></div>
        </div>
        <div class="customer-info">
            <h3><?= htmlspecialchars($customer['name']) ?></h3>
            <p class="customer-id"><?= htmlspecialchars($customer['id']) ?></p>
            <p class="customer-email"><?= htmlspecialchars($customer['email']) ?></p>
            <?php if (!empty($customer['mobile'])): ?>
                <p class="customer-mobile"><?= htmlspecialchars($customer['mobile']) ?></p>
            <?php endif; ?>
            <p class="customer-language">
                <?php
                $lang = $customer['preferred_language'] ?? 'el';
                $flag = $lang === 'el' ? '🇬🇷' : '🇬🇧';
                $langName = $lang === 'el' ? 'Greek (Ελληνικά)' : 'English';
                echo $flag . ' ' . $langName;
                ?>
            </p>
        </div>
        <div class="customer-actions">
            <button class="btn btn-primary btn-sm" onclick="editCustomer('<?= htmlspecialchars($customer['id']) ?>')">
                <i class="fas fa-edit"></i> Edit
            </button>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="stats-row">
        <div class="stat-item">
            <span class="stat-number"><?= $stats['total_reservations'] ?? 0 ?></span>
            <span class="stat-label">Total Bookings</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">€<?= number_format($stats['total_spent'] ?? 0, 2) ?></span>
            <span class="stat-label">Total Spent</span>
        </div>
        <div class="stat-item">
            <span class="stat-number"><?= $stats['upcoming_reservations'] ?? 0 ?></span>
            <span class="stat-label">Upcoming</span>
        </div>
    </div>

    <!-- Additional Info -->
    <?php if (!empty($customer['address']) || !empty($customer['date_of_birth']) || !empty($customer['notes'])): ?>
        <div class="customer-info-section">
            <h4>Additional Information</h4>
            <?php if (!empty($customer['address'])): ?>
                <p><strong>Address:</strong> <?= htmlspecialchars($customer['address']) ?></p>
            <?php endif; ?>
            <?php if (!empty($customer['date_of_birth'])): ?>
                <p><strong>Date of Birth:</strong> <?= date('F j, Y', strtotime($customer['date_of_birth'])) ?></p>
            <?php endif; ?>
            <?php if (!empty($customer['notes'])): ?>
                <p><strong>Notes:</strong> <?= nl2br(htmlspecialchars($customer['notes'])) ?></p>
            <?php endif; ?>
            <p><strong>Customer Since:</strong> <?= date('F j, Y', strtotime($customer['created_at'])) ?></p>
        </div>
    <?php endif; ?>

    <!-- Upcoming Reservations -->
    <?php if (!empty($upcomingReservations)): ?>
        <div class="reservations-section">
            <h4>Upcoming Reservations</h4>
            <div class="reservations-list">
                <?php foreach ($upcomingReservations as $reservation): ?>
                    <div class="reservation-item">
                        <div class="reservation-info">
                            <strong><?= htmlspecialchars($reservation['service_name'] ?? $reservation['service']) ?></strong>
                            <span><?= date('M j, Y', strtotime($reservation['date'])) ?> at <?= $reservation['time'] ?></span>
                            <span>€<?= number_format($reservation['price'], 2) ?></span>
                        </div>
                        <div class="reservation-actions">
                            <button class="btn btn-sm btn-primary" onclick="editReservation('<?= htmlspecialchars($reservation['id']) ?>')">
                                Edit
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Recent Reservations -->
    <?php if (!empty($recentReservations)): ?>
        <div class="reservations-section">
            <h4>Recent Reservations</h4>
            <div class="reservations-list">
                <?php foreach (array_slice($recentReservations, 0, 5) as $reservation): ?>
                    <div class="reservation-item">
                        <div class="reservation-info">
                            <strong><?= htmlspecialchars($reservation['service_name'] ?? $reservation['service']) ?></strong>
                            <span><?= date('M j, Y', strtotime($reservation['date'])) ?> - <?= ucfirst($reservation['status']) ?></span>
                            <span>€<?= number_format($reservation['price'], 2) ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
</div>