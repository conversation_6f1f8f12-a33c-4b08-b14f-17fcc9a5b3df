<?php

/**
 * Reservation Handler Class
 *
 * Manages all reservation-related operations including:
 * - Reservation creation and validation
 * - Time slot availability checking
 * - Reservation updates and cancellations
 * - Reservation data retrieval and filtering
 * - Business logic enforcement
 *
 * <AUTHOR> System
 * @version 1.0
 */
class ReservationHandler
{
    /** @var Database Database instance */
    private $db;

    /**
     * Initialize reservation handler with database connection
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Create a new reservation
     */
    public function createReservation(string $customerId, string $service, string $date, string $time, string $employeeId = ''): array
    {
        // Validate inputs
        if (empty($customerId) || empty($service) || empty($date) || empty($time)) {
            return ['success' => false, 'message' => 'All fields are required.'];
        }

        // Validate date format (YYYY-MM-DD)
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            return ['success' => false, 'message' => 'Invalid date format.'];
        }

        // Validate time format (HH:MM)
        if (!preg_match('/^\d{2}:\d{2}$/', $time)) {
            return ['success' => false, 'message' => 'Invalid time format.'];
        }

        // Get service details
        $services = getServices();
        if (!isset($services[$service])) {
            return ['success' => false, 'message' => 'Invalid service selected.'];
        }

        $serviceDetails = $services[$service];
        $duration = $serviceDetails['duration'];
        $price = $serviceDetails['price'];
        $allowEmployeeSelection = $serviceDetails['allow_employee_selection'] ?? false;

        // Determine employee assignment based on service settings
        require_once __DIR__ . '/availability_handler.php';
        $availabilityHandler = new AvailabilityHandler();

        if ($allowEmployeeSelection && !empty($employeeId)) {
            // Customer selected employee - validate availability
            $availableSlots = $availabilityHandler->getAvailableTimeSlots($date, $service);
            $isEmployeeAvailable = false;

            foreach ($availableSlots as $slot) {
                if ($slot['time'] === $time && in_array($employeeId, $slot['available_employees'])) {
                    $isEmployeeAvailable = true;
                    break;
                }
            }

            if (!$isEmployeeAvailable) {
                return ['success' => false, 'message' => 'Selected employee is no longer available for this time slot.'];
            }

            $assignedEmployeeId = $employeeId;
        } else {
            // Auto-assign employee with least bookings
            $assignedEmployeeId = $availabilityHandler->getBestAvailableEmployee($date, $time, $service);

            if (!$assignedEmployeeId) {
                return ['success' => false, 'message' => 'This time slot is no longer available. Please choose another time.'];
            }
        }

        // Generate reservation ID
        $reservationId = $this->generateReservationId();

        // Insert reservation into database
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("INSERT INTO reservations
            (id, customer_id, service, date, time, duration, price, status, employee_id, created_at, tenant_id)
            VALUES (:id, :customer_id, :service, :date, :time, :duration, :price, :status, :employee_id, :created_at, :tenant_id)");

        $createdAt = date('Y-m-d H:i:s');
        $stmt->bindValue(':id', $reservationId);
        $stmt->bindValue(':customer_id', $customerId);
        $stmt->bindValue(':service', $service);
        $stmt->bindValue(':date', $date);
        $stmt->bindValue(':time', $time);
        $stmt->bindValue(':duration', $duration);
        $stmt->bindValue(':price', $price);
        $stmt->bindValue(':status', 'confirmed');
        $stmt->bindValue(':employee_id', $assignedEmployeeId);
        $stmt->bindValue(':created_at', $createdAt);
        $stmt->bindValue(':tenant_id', $tenantId);

        $result = $stmt->execute();

        if (!$result) {
            log_activity("Failed to create reservation: " . $conn->lastErrorMsg());
            return ['success' => false, 'message' => 'Failed to create reservation. Please try again.'];
        }

        // Update customer's total_reservations and last_visit
        $this->updateCustomerStats($customerId, $date);

        log_activity("New reservation created: $reservationId for customer $customerId on $date at $time");

        return [
            'success' => true,
            'message' => 'Your reservation has been confirmed!',
            'reservation_id' => $reservationId,
            'date' => $date,
            'time' => $time,
            'service' => $serviceDetails['name'],
            'duration' => $duration,
            'price' => $price
        ];
    }

    /**
     * Create a reservation with specific employee assignment (for admin interface)
     */
    public function createReservationWithEmployee(string $customerId, string $service, string $date, string $time, string $employeeId): array
    {
        // Validate inputs
        if (empty($customerId) || empty($service) || empty($date) || empty($time) || empty($employeeId)) {
            return ['success' => false, 'message' => 'All fields are required.'];
        }

        // Validate date format (YYYY-MM-DD)
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            return ['success' => false, 'message' => 'Invalid date format.'];
        }

        // Validate time format (HH:MM)
        if (!preg_match('/^\d{2}:\d{2}$/', $time)) {
            return ['success' => false, 'message' => 'Invalid time format.'];
        }

        // Check if the specific employee is available for this time slot
        require_once __DIR__ . '/availability_handler.php';
        $availabilityHandler = new AvailabilityHandler();

        if (!$availabilityHandler->isEmployeeAvailable($date, $time, $service, $employeeId)) {
            return ['success' => false, 'message' => 'The selected employee is not available at this time.'];
        }

        // Get service details
        $services = getServices();
        if (!isset($services[$service])) {
            return ['success' => false, 'message' => 'Invalid service selected.'];
        }

        $serviceDetails = $services[$service];
        $duration = $serviceDetails['duration'];
        $price = $serviceDetails['price'];

        // Generate reservation ID
        $reservationId = $this->generateReservationId();

        // Insert reservation into database
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("INSERT INTO reservations
            (id, customer_id, service, date, time, duration, price, status, employee_id, created_at, tenant_id)
            VALUES (:id, :customer_id, :service, :date, :time, :duration, :price, :status, :employee_id, :created_at, :tenant_id)");

        $createdAt = date('Y-m-d H:i:s');
        $stmt->bindValue(':id', $reservationId);
        $stmt->bindValue(':customer_id', $customerId);
        $stmt->bindValue(':service', $service);
        $stmt->bindValue(':date', $date);
        $stmt->bindValue(':time', $time);
        $stmt->bindValue(':duration', $duration);
        $stmt->bindValue(':price', $price);
        $stmt->bindValue(':status', 'confirmed');
        $stmt->bindValue(':employee_id', $employeeId);
        $stmt->bindValue(':created_at', $createdAt);
        $stmt->bindValue(':tenant_id', $tenantId);

        $result = $stmt->execute();

        if (!$result) {
            log_activity("Failed to create reservation: " . $conn->lastErrorMsg());
            return ['success' => false, 'message' => 'Failed to create reservation. Please try again.'];
        }

        // Update customer's total_reservations and last_visit
        $this->updateCustomerStats($customerId, $date);

        log_activity("New reservation created: $reservationId for customer $customerId with employee $employeeId on $date at $time");

        return [
            'success' => true,
            'message' => 'Reservation has been confirmed!',
            'reservation_id' => $reservationId,
            'date' => $date,
            'time' => $time,
            'service' => $serviceDetails['name'],
            'duration' => $duration,
            'price' => $price,
            'employee_id' => $employeeId
        ];
    }

    /**
     * Add a new reservation (alias for createReservation for admin interface)
     */
    public function addReservation(string $customerId, string $service, string $date, string $time, string $employeeId = ''): array
    {
        if (!empty($employeeId)) {
            return $this->createReservationWithEmployee($customerId, $service, $date, $time, $employeeId);
        }
        return $this->createReservation($customerId, $service, $date, $time);
    }

    /**
     * Get available time slots for a date and service
     */
    public function getAvailableTimeSlots(string $date, string $service): array
    {
        require_once __DIR__ . '/availability_handler.php';
        $availabilityHandler = new AvailabilityHandler();
        return $availabilityHandler->getAvailableTimeSlots($date, $service);
    }

    /**
     * Check if a time slot is available
     */
    private function isTimeSlotAvailable(string $date, string $time, string $service): bool
    {
        $services = getServices();
        if (!isset($services[$service])) {
            return false;
        }

        $duration = $services[$service]['duration'];

        // Convert times to minutes for easier calculation
        $requestedStartMinutes = $this->timeToMinutes($time);
        $requestedEndMinutes = $requestedStartMinutes + $duration;

        // Get all existing reservations for this date
        $conn = $this->db->getConnection();
        $stmt = $conn->prepare("
            SELECT time, duration FROM reservations
            WHERE date = :date AND status != 'cancelled'
        ");

        $stmt->bindValue(':date', $date);
        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $existingStartMinutes = $this->timeToMinutes($row['time']);
            $existingEndMinutes = $existingStartMinutes + (int)$row['duration'];

            // Check for overlap: two time slots overlap if one starts before the other ends
            if ($requestedStartMinutes < $existingEndMinutes && $requestedEndMinutes > $existingStartMinutes) {
                return false; // Overlap found
            }
        }

        return true; // No overlap found
    }

    /**
     * Convert time string (HH:MM) to minutes since midnight
     */
    private function timeToMinutes(string $time): int
    {
        list($hours, $minutes) = explode(':', $time);
        return (int)$hours * 60 + (int)$minutes;
    }

    /**
     * Get reservations for a specific date (tenant-aware)
     */
    public function getReservationsByDate(string $date): array
    {
        $reservations = [];
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("
            SELECT r.*, c.name as customer_name, c.email as customer_email, c.mobile as customer_mobile
            FROM reservations r
            JOIN customers c ON r.customer_id = c.id
            WHERE r.date = :date AND r.tenant_id = :tenant_id
            ORDER BY r.time
        ");

        $stmt->bindValue(':date', $date);
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $reservations[] = $row;
        }

        return $reservations;
    }

    /**
     * Get future reservations for a customer
     */
    public function getFutureReservationsForCustomer(string $customerId): array
    {
        $today = date('Y-m-d');
        $reservations = [];
        $conn = $this->db->getConnection();

        $stmt = $conn->prepare("
            SELECT r.*, s.name as service_name
            FROM reservations r
            LEFT JOIN services s ON r.service = s.id
            WHERE r.customer_id = :customer_id
            AND (r.date > :today OR (r.date = :today AND r.time >= :current_time))
            AND r.status != 'cancelled'
            ORDER BY r.date, r.time
        ");

        $stmt->bindValue(':customer_id', $customerId);
        $stmt->bindValue(':today', $today);
        $stmt->bindValue(':current_time', date('H:i'));

        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $reservations[] = $row;
        }

        return $reservations;
    }

    /**
     * Generate a unique reservation ID
     */
    private function generateReservationId(): string
    {
        return generate_reservation_id();
    }

    /**
     * Update customer statistics after a reservation
     */
    private function updateCustomerStats(string $customerId, string $reservationDate): void
    {
        $conn = $this->db->getConnection();

        // Get current total reservations
        $stmt = $conn->prepare("SELECT total_reservations FROM customers WHERE id = :id");
        $stmt->bindValue(':id', $customerId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);

        $totalReservations = ($row ? (int)$row['total_reservations'] : 0) + 1;

        // Update customer stats
        $stmt = $conn->prepare("
            UPDATE customers 
            SET total_reservations = :total_reservations, last_visit = :last_visit
            WHERE id = :id
        ");

        $stmt->bindValue(':total_reservations', $totalReservations);
        $stmt->bindValue(':last_visit', $reservationDate);
        $stmt->bindValue(':id', $customerId);

        $stmt->execute();
    }

    /**
     * Get all reservations (tenant-aware)
     */
    public function getAllReservations(): array
    {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("
            SELECT r.*, c.name as customer_name, c.email as customer_email, c.mobile as customer_mobile
            FROM reservations r
            JOIN customers c ON r.customer_id = c.id
            WHERE r.tenant_id = :tenant_id
            ORDER BY r.date DESC, r.time DESC
        ");

        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();

        $reservations = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $reservations[] = $row;
        }

        return $reservations;
    }

    /**
     * Get reservations with pagination (tenant-aware)
     */
    public function getReservationsPaginated(int $limit = 20, int $offset = 0): array
    {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("
            SELECT r.*, c.name as customer_name, c.email as customer_email, c.mobile as customer_mobile
            FROM reservations r
            JOIN customers c ON r.customer_id = c.id
            WHERE r.tenant_id = :tenant_id
            ORDER BY r.date DESC, r.time DESC
            LIMIT :limit OFFSET :offset
        ");

        $stmt->bindValue(':tenant_id', $tenantId);
        $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
        $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);

        $result = $stmt->execute();

        $reservations = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $reservations[] = $row;
        }

        return $reservations;
    }

    /**
     * Get total reservation count (tenant-aware)
     */
    public function getReservationCount(): int
    {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM reservations WHERE tenant_id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        return (int)($row['count'] ?? 0);
    }

    /**
     * Search reservations with pagination
     */
    public function searchReservations(string $searchTerm, int $limit = 20, int $offset = 0): array
    {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        $searchTerm = '%' . strtolower(trim($searchTerm)) . '%';

        $stmt = $conn->prepare("
            SELECT r.*, c.name as customer_name, c.email as customer_email, c.mobile as customer_mobile,
                   s.name as service_name, e.name as employee_name
            FROM reservations r
            JOIN customers c ON r.customer_id = c.id
            LEFT JOIN services s ON r.service = s.id
            LEFT JOIN employees e ON r.employee_id = e.id
            WHERE LOWER(c.name) LIKE :search
               OR LOWER(c.email) LIKE :search
               OR LOWER(c.mobile) LIKE :search
               OR LOWER(s.name) LIKE :search
               OR LOWER(e.name) LIKE :search
               OR LOWER(r.service) LIKE :search
               OR LOWER(r.status) LIKE :search
               OR r.date LIKE :search
               OR r.time LIKE :search
            ORDER BY r.date DESC, r.time DESC
            LIMIT :limit OFFSET :offset
        ");

        $stmt->bindValue(':search', $searchTerm);
        $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
        $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);

        $result = $stmt->execute();

        $reservations = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $reservations[] = $row;
        }

        return $reservations;
    }

    /**
     * Get total count of search results (tenant-aware)
     */
    public function getSearchReservationCount(string $searchTerm): int
    {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $searchTerm = '%' . strtolower(trim($searchTerm)) . '%';

        $stmt = $conn->prepare("
            SELECT COUNT(*) as count
            FROM reservations r
            JOIN customers c ON r.customer_id = c.id AND c.tenant_id = :tenant_id
            LEFT JOIN services s ON r.service = s.id AND s.tenant_id = :tenant_id
            LEFT JOIN employees e ON r.employee_id = e.id AND e.tenant_id = :tenant_id
            WHERE r.tenant_id = :tenant_id
               AND (LOWER(c.name) LIKE :search
               OR LOWER(c.email) LIKE :search
               OR LOWER(c.mobile) LIKE :search
               OR LOWER(s.name) LIKE :search
               OR LOWER(e.name) LIKE :search
               OR LOWER(r.service) LIKE :search
               OR LOWER(r.status) LIKE :search
               OR r.date LIKE :search
               OR r.time LIKE :search)
        ");

        $stmt->bindValue(':tenant_id', $tenantId);
        $stmt->bindValue(':search', $searchTerm);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);

        return (int)($row['count'] ?? 0);
    }

    /**
     * Get reservations by date range
     */
    public function getReservationsByDateRange(string $startDate, string $endDate): array
    {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        $stmt = $conn->prepare("
            SELECT r.*, c.name as customer_name, c.email as customer_email, c.mobile as customer_mobile
            FROM reservations r
            JOIN customers c ON r.customer_id = c.id
            WHERE r.date BETWEEN :start_date AND :end_date
            ORDER BY r.date, r.time
        ");

        $stmt->bindValue(':start_date', $startDate);
        $stmt->bindValue(':end_date', $endDate);

        $result = $stmt->execute();

        $reservations = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $reservations[] = $row;
        }

        return $reservations;
    }

    /**
     * Get reservations for a specific customer
     */
    public function getReservationsForCustomer(string $customerId): array
    {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        $stmt = $conn->prepare("
            SELECT r.*, s.name as service_name, s.duration as service_duration, s.price as service_price
            FROM reservations r
            JOIN services s ON r.service = s.id
            WHERE r.customer_id = :customer_id
            ORDER BY r.date DESC, r.time DESC
        ");

        $stmt->bindValue(':customer_id', $customerId);
        $result = $stmt->execute();

        $reservations = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $reservations[] = $row;
        }

        return $reservations;
    }

    /**
     * Get a specific reservation by ID (tenant-aware)
     */
    public function getReservationById(string $id): ?array
    {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("
            SELECT r.*, c.name as customer_name, c.email as customer_email, c.mobile as customer_mobile,
                   s.name as service_name, s.duration as service_duration, s.price as service_price
            FROM reservations r
            JOIN customers c ON r.customer_id = c.id
            JOIN services s ON r.service = s.id
            WHERE r.id = :id AND r.tenant_id = :tenant_id
            LIMIT 1
        ");

        $stmt->bindValue(':id', $id);
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();

        $row = $result->fetchArray(SQLITE3_ASSOC);
        return $row ?: null;
    }

    /**
     * Cancel a reservation
     * 
     * @param string $reservationId The ID of the reservation to cancel
     * @param string|null $customerId Optional customer ID for verification
     * @param string $reason Reason for cancellation
     * @param bool $notifyCustomer Whether to send notification email
     * @param bool $isAdmin Whether the cancellation is done by admin
     * @return array Result with success status and message
     */
    public function cancelReservation(string $reservationId, ?string $customerId = null, string $reason = '', bool $notifyCustomer = true, bool $isAdmin = false): array
    {
        // Check if reservation exists
        $reservation = $this->getReservationById($reservationId);
        if (!$reservation) {
            return ['success' => false, 'message' => 'Reservation not found'];
        }

        // If customer ID is provided, verify it matches
        if ($customerId !== null && $reservation['customer_id'] !== $customerId) {
            return ['success' => false, 'message' => 'Unauthorized cancellation attempt'];
        }

        // Check if reservation is already cancelled
        if ($reservation['status'] === 'cancelled') {
            return ['success' => false, 'message' => 'Reservation is already cancelled'];
        }

        // Check if reservation is in the past
        if (strtotime($reservation['date'] . ' ' . $reservation['time']) < time() && !$isAdmin) {
            return ['success' => false, 'message' => 'Cannot cancel past reservations'];
        }

        // Update status in database
        $conn = $this->db->getConnection();

        // Check if cancel_reason column exists
        $hasReasonColumn = false;
        $result = $conn->query("PRAGMA table_info(reservations)");
        while ($column = $result->fetchArray(SQLITE3_ASSOC)) {
            if ($column['name'] === 'cancel_reason') {
                $hasReasonColumn = true;
                break;
            }
        }

        if ($hasReasonColumn) {
            $stmt = $conn->prepare("UPDATE reservations SET status = 'cancelled', cancel_reason = :reason WHERE id = :id");
            $stmt->bindValue(':reason', $reason);
        } else {
            $stmt = $conn->prepare("UPDATE reservations SET status = 'cancelled' WHERE id = :id");
        }

        $stmt->bindValue(':id', $reservationId);
        $result = $stmt->execute();

        if ($result) {
            // Log the cancellation
            log_activity("Cancelled reservation: $reservationId (Reason: $reason)");

            // Send notification email if requested
            if ($notifyCustomer) {
                $customerHandler = new CustomerHandler();
                $customer = $customerHandler->getCustomerById($reservation['customer_id']);

                if ($customer && !empty($customer['email'])) {
                    // Send cancellation email using modern EmailSender
                    require_once __DIR__ . '/email_templates/email_sender.php';
                    $emailSender = new EmailSender();
                    $emailSender->sendCancellationEmail($customer['email'], $reservation, $customer);
                }
            }

            return [
                'success' => true,
                'message' => 'Reservation cancelled successfully',
                'reservation' => $this->getReservationById($reservationId)
            ];
        } else {
            return ['success' => false, 'message' => 'Failed to cancel reservation'];
        }
    }

    /**
     * Get reservations by status
     */
    public function getReservationsByStatus(string $status): array
    {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        $stmt = $conn->prepare("
            SELECT r.*, c.name as customer_name, c.email as customer_email, c.mobile as customer_mobile
            FROM reservations r
            JOIN customers c ON r.customer_id = c.id
            WHERE r.status = :status
            ORDER BY r.date, r.time
        ");

        $stmt->bindValue(':status', $status);
        $result = $stmt->execute();

        $reservations = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $reservations[] = $row;
        }

        return $reservations;
    }

    /**
     * Get reservations by date and status
     */
    public function getReservationsByDateAndStatus(string $date, string $status): array
    {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        $stmt = $conn->prepare("
            SELECT r.*, c.name as customer_name, c.email as customer_email, c.mobile as customer_mobile
            FROM reservations r
            JOIN customers c ON r.customer_id = c.id
            WHERE r.date = :date AND r.status = :status
            ORDER BY r.time
        ");

        $stmt->bindValue(':date', $date);
        $stmt->bindValue(':status', $status);
        $result = $stmt->execute();

        $reservations = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $reservations[] = $row;
        }

        return $reservations;
    }

    /**
     * Update an existing reservation
     * 
     * @param string $reservationId The ID of the reservation to update
     * @param string $newDate New date for the reservation
     * @param string $newTime New time for the reservation
     * @param string|null $newService New service ID (optional)
     * @return array Result with success status and message
     */
    public function updateReservation(string $reservationId, string $newDate, string $newTime, ?string $newService = null, ?string $newEmployeeId = null): array
    {
        // Validate inputs
        if (empty($reservationId) || empty($newDate) || empty($newTime)) {
            return ['success' => false, 'message' => 'Missing required fields'];
        }

        // Check if reservation exists
        $reservation = $this->getReservationById($reservationId);
        if (!$reservation) {
            return ['success' => false, 'message' => 'Reservation not found'];
        }

        // Check if date is valid
        if (strtotime($newDate) < strtotime(date('Y-m-d'))) {
            return ['success' => false, 'message' => 'Cannot schedule for a past date'];
        }

        // Prepare update data
        $updateData = [
            'date' => $newDate,
            'time' => $newTime
        ];

        // Add service if provided
        if ($newService !== null) {
            $updateData['service'] = $newService;
        }

        // Handle employee assignment
        if ($newEmployeeId !== null) {
            if ($newEmployeeId === '') {
                // Auto-assign best available employee
                require_once __DIR__ . '/availability_handler.php';
                $availabilityHandler = new AvailabilityHandler();
                $serviceToCheck = $newService ?? $reservation['service'];
                $assignedEmployeeId = $availabilityHandler->getBestAvailableEmployee($newDate, $newTime, $serviceToCheck);

                if ($assignedEmployeeId) {
                    $updateData['employee_id'] = $assignedEmployeeId;
                }
            } else {
                // Use specified employee
                $updateData['employee_id'] = $newEmployeeId;
            }
        }

        // Update in database
        $conn = $this->db->getConnection();

        $setClause = [];
        $params = [];

        foreach ($updateData as $key => $value) {
            $setClause[] = "$key = :$key";
            $params[":$key"] = $value;
        }

        $params[':id'] = $reservationId;

        $sql = "UPDATE reservations SET " . implode(', ', $setClause) . " WHERE id = :id";
        $stmt = $conn->prepare($sql);

        foreach ($params as $param => $value) {
            $stmt->bindValue($param, $value);
        }

        $result = $stmt->execute();

        if ($result) {
            // Log the update
            log_activity("Updated reservation: $reservationId (Date: $newDate, Time: $newTime)");

            return [
                'success' => true,
                'message' => 'Reservation updated successfully',
                'reservation' => $this->getReservationById($reservationId)
            ];
        } else {
            return ['success' => false, 'message' => 'Failed to update reservation'];
        }
    }

    /**
     * Mark a reservation as completed
     * 
     * @param string $reservationId The ID of the reservation to complete
     * @return array Result with success status and message
     */
    public function completeReservation(string $reservationId): array
    {
        // Check if reservation exists
        $reservation = $this->getReservationById($reservationId);
        if (!$reservation) {
            return ['success' => false, 'message' => 'Reservation not found'];
        }

        // Check if reservation is in a valid state to be completed
        if ($reservation['status'] !== 'confirmed') {
            return ['success' => false, 'message' => 'Only confirmed reservations can be marked as completed'];
        }

        // Update status in database
        $conn = $this->db->getConnection();
        $stmt = $conn->prepare("UPDATE reservations SET status = 'completed' WHERE id = :id");
        $stmt->bindValue(':id', $reservationId);
        $result = $stmt->execute();

        if ($result) {
            // Log the completion
            log_activity("Completed reservation: $reservationId");

            // Update customer stats if needed
            if (isset($reservation['customer_id'])) {
                $customerHandler = new CustomerHandler();
                $customerHandler->updateCustomerStats($reservation['customer_id']);
            }

            return [
                'success' => true,
                'message' => 'Reservation marked as completed',
                'reservation' => $this->getReservationById($reservationId)
            ];
        } else {
            return ['success' => false, 'message' => 'Failed to update reservation status'];
        }
    }
}
