<?php

/**
 * ========================================
 * EMAIL TEMPLATE CREATOR
 * ========================================
 * 
 * Generates consistent email templates with inline CSS
 * for maximum email client compatibility.
 * 
 * Features:
 * - Unified base styling across all templates
 * - Color variations per template type
 * - Inline CSS generation for email compatibility
 * - Variable-based content system
 * 
 * <AUTHOR> System
 * @version 1.0
 */

class EmailTemplateCreator
{
    /**
     * Constructor - Load email texts from unified manager
     */
    public function __construct()
    {
        require_once __DIR__ . '/../unified_text_manager.php';
        $this->emailTexts = UnifiedTextManager::getEmailTexts();
    }

    /* ========================================
       BASE STYLING VARIABLES
       ======================================== */

    private $baseStyles = [
        // Body styles
        'body' => 'margin:0;padding:20px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif;background:linear-gradient(135deg,#f5f7fa 0%,#c3cfe2 100%);line-height:1.6;color:#333;min-height:100vh',

        // Container styles
        'container' => 'max-width:600px;margin:0 auto;background:#ffffff;border-radius:12px;overflow:hidden;box-shadow:0 10px 30px rgba(0,0,0,0.1);border:1px solid #e0e0e0',

        // Header styles (color will be dynamic)
        'header' => 'color:white;padding:40px 30px;text-align:center;background:linear-gradient(135deg,var(--header-color) 0%,var(--header-dark) 100%);position:relative',
        'header_icon' => 'font-size:48px;margin-bottom:15px;opacity:0.9',
        'header_h1' => 'margin:0;font-size:28px;font-weight:600;margin-bottom:8px',
        'header_p' => 'margin:0;font-size:16px;opacity:0.9;font-weight:300',

        // Content styles
        'content' => 'padding:40px 30px',
        'greeting' => 'font-size:18px;margin-bottom:25px;font-weight:500;color:#2c3e50',
        'paragraph' => 'margin:0 0 20px 0;font-size:16px;line-height:1.6;color:#555',

        // Detail sections
        'detail_section' => 'background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%);border-radius:10px;padding:25px;margin:25px 0;border:1px solid #dee2e6;box-shadow:0 2px 8px rgba(0,0,0,0.05)',
        'detail_section_h3' => 'margin:0 0 20px 0;font-size:18px;color:#2c3e50;font-weight:600;display:flex;align-items:center',
        'detail_section_icon' => 'margin-right:10px;font-size:20px;color:#6c757d',
        'detail_row' => 'display:flex;justify-content:space-between;align-items:center;padding:12px 0;border-bottom:1px solid #e9ecef',
        'detail_row_last' => 'display:flex;justify-content:space-between;align-items:center;padding:12px 0;border-bottom:none',
        'detail_label' => 'font-weight:600;color:#495057;font-size:15px',
        'detail_value' => 'color:#212529;font-size:15px;font-weight:500',

        // Special elements with dashed borders
        'reservation_id' => 'background:#fff;border:2px dashed #007bff;border-radius:8px;padding:15px;margin:25px 0;text-align:center;font-weight:700;color:#007bff;font-size:16px;font-family:"Courier New",monospace;letter-spacing:1px',
        'verification_code' => 'background:#fff;border:3px dashed #28a745;border-radius:12px;padding:25px;margin:30px 0;font-size:32px;font-weight:700;color:#28a745;letter-spacing:6px;font-family:"Courier New",monospace;text-align:center;box-shadow:0 4px 12px rgba(40,167,69,0.15)',
        'access_code_section' => 'background:linear-gradient(135deg,#e3f2fd 0%,#bbdefb 100%);border-radius:10px;padding:25px;margin:25px 0;text-align:center;border:1px solid #90caf9',
        'access_code_title' => 'margin:0 0 15px 0;font-size:18px;color:#1565c0;font-weight:600',
        'access_code_desc' => 'margin:0 0 20px 0;color:#1976d2;font-size:14px',
        'access_code_display' => 'background:#fff;border:2px dashed #2196f3;border-radius:8px;padding:15px 20px;margin:15px 0;font-family:"Courier New",monospace;font-weight:700;font-size:18px;color:#1976d2;letter-spacing:2px;display:inline-block;box-shadow:0 2px 8px rgba(33,150,243,0.15)',
        'access_code_note' => 'margin:15px 0 0 0;font-size:12px;color:#1565c0;font-style:italic',

        // Highlight boxes
        'highlight_box' => 'background:linear-gradient(135deg,#fff3cd 0%,#ffeaa7 100%);border:1px solid #ffc107;border-radius:8px;padding:20px;margin:25px 0;text-align:center',
        'highlight_title' => 'margin:0 0 10px 0;font-size:18px;color:#856404;font-weight:600',
        'highlight_text' => 'margin:0;color:#856404;font-size:15px',

        // Footer styles
        'footer' => 'background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%);padding:25px 30px;text-align:center;border-top:1px solid #dee2e6',
        'footer_text' => 'margin:0;font-size:13px;color:#6c757d;line-height:1.5',
        'footer_brand' => 'font-weight:600;color:#495057',

        // Responsive
        'mobile_container' => '@media (max-width: 600px) { .email-container { margin:10px;border-radius:8px } .header,.content,.footer { padding:25px 20px } .detail-section { padding:20px } .header h1 { font-size:24px } .header-icon { font-size:36px } }'
    ];

    /* ========================================
       COLOR SCHEMES PER TEMPLATE TYPE
       ======================================== */

    private $colorSchemes = [
        'confirmation' => ['primary' => '#28a745', 'dark' => '#1e7e34', 'icon' => '✅'],      // Green - success
        'cancellation' => ['primary' => '#dc3545', 'dark' => '#bd2130', 'icon' => '❌'],      // Red - cancellation
        'reminder' => ['primary' => '#ffc107', 'dark' => '#e0a800', 'icon' => '⏰'],          // Yellow/Orange - reminder
        'welcome' => ['primary' => '#6f42c1', 'dark' => '#59359a', 'icon' => '🎉'],           // Purple - welcome
        'verification' => ['primary' => '#667eea', 'dark' => '#5a6fd8', 'icon' => '🔐'],      // Blue - verification
        'admin_new_reservation' => ['primary' => '#28a745', 'dark' => '#1e7e34', 'icon' => '📅'],    // Green - new booking
        'admin_cancellation' => ['primary' => '#dc3545', 'dark' => '#bd2130', 'icon' => '🚫'],       // Red - cancellation
        'admin_new_customer' => ['primary' => '#6f42c1', 'dark' => '#59359a', 'icon' => '👤'],       // Purple - new customer
        'admin_customer_deletion' => ['primary' => '#6c757d', 'dark' => '#545b62', 'icon' => '🗑️']   // Gray - deletion
    ];

    /* ========================================
       MULTILINGUAL TEXT SYSTEM
       ======================================== */

    private $emailTexts = [
        'en' => [
            // Common
            'site_name_placeholder' => '{{site_name}}',
            'customer_name_placeholder' => '{{customer_name}}',
            'year_placeholder' => '{{year}}',
            'footer_text' => '&copy; {{year}} {{site_name}}. All rights reserved.',

            // Confirmation
            'confirmation_title' => 'Reservation Confirmed',
            'confirmation_header' => 'Reservation Confirmed',
            'confirmation_greeting' => 'Hello {{customer_name}},',
            'confirmation_message' => 'Your reservation has been confirmed! We look forward to seeing you.',
            'confirmation_details_title' => '📋 Reservation Details',
            'confirmation_thanks' => 'Thank you for choosing {{site_name}}!',

            // Cancellation
            'cancellation_title' => 'Reservation Cancelled',
            'cancellation_header' => 'Reservation Cancelled',
            'cancellation_greeting' => 'Hello {{customer_name}},',
            'cancellation_message' => 'Your reservation has been cancelled as requested.',
            'cancellation_details_title' => '📋 Cancelled Reservation',
            'cancellation_thanks' => 'Thank you for using {{site_name}}.',

            // Reminder
            'reminder_title' => 'Appointment Reminder',
            'reminder_header' => 'Appointment Reminder',
            'reminder_greeting' => 'Hello {{customer_name}},',
            'reminder_message' => 'This is a friendly reminder about your upcoming appointment with us!',
            'reminder_countdown_title' => '🗓️ Your appointment is {{time_until}}',
            'reminder_countdown_text' => 'Don\'t forget - we\'re excited to see you!',
            'reminder_details_title' => '📋 Appointment Details',
            'reminder_thanks' => 'We look forward to seeing you soon!',

            // Welcome
            'welcome_title' => 'Welcome to {{site_name}}',
            'welcome_header' => 'Welcome!',
            'welcome_greeting' => 'Welcome, {{customer_name}}!',
            'welcome_message' => 'Thank you for joining {{site_name}}! We\'re excited to have you as part of our community.',
            'welcome_access_title' => '🔑 Your Access Code',
            'welcome_access_desc' => 'Use this code to access your reservations:',
            'welcome_access_note' => 'Keep this code safe for future use.',
            'welcome_final' => 'You can now book appointments and manage your reservations.',

            // Verification
            'verification_title' => 'Verification Code',
            'verification_header' => 'Email Verification',
            'verification_main_title' => 'Your Verification Code',
            'verification_desc' => 'Please enter this code to continue:',
            'verification_expiry' => 'This code expires in {{expiry_minutes}} minutes.',



            // Field labels for detail sections
            'field_service' => 'Service',
            'field_date' => 'Date',
            'field_time' => 'Time',
            'field_price' => 'Price',
            'field_duration' => 'Duration',
            'field_id' => 'ID',
            'field_name' => 'Name',
            'field_email' => 'Email',
            'field_mobile' => 'Mobile',

            // Admin templates
            'admin_new_reservation_title' => 'New Reservation',
            'admin_new_reservation_header' => 'New Reservation',
            'admin_customer_section' => '👤 Customer Information',
            'admin_reservation_section' => '📅 Reservation Details',

            'admin_cancellation_title' => 'Reservation Cancelled',
            'admin_cancellation_header' => 'Reservation Cancelled',
            'admin_cancelled_section' => '🚫 Cancelled Reservation',

            'admin_new_customer_title' => 'New Customer',
            'admin_new_customer_header' => 'New Customer Registration',
            'admin_customer_details_section' => '👤 Customer Details',

            'admin_customer_deletion_title' => 'Customer Deleted',
            'admin_customer_deletion_header' => 'Customer Account Deleted',
            'admin_deleted_customer_section' => '🗑️ Deleted Customer Information'
        ],

        'el' => [
            // Common
            'site_name_placeholder' => '{{site_name}}',
            'customer_name_placeholder' => '{{customer_name}}',
            'year_placeholder' => '{{year}}',
            'footer_text' => '&copy; {{year}} {{site_name}}. Όλα τα δικαιώματα διατηρούνται.',

            // Confirmation
            'confirmation_title' => 'Επιβεβαίωση Κράτησης',
            'confirmation_header' => 'Κράτηση Επιβεβαιώθηκε',
            'confirmation_greeting' => 'Γεια σας {{customer_name}},',
            'confirmation_message' => 'Η κράτησή σας έχει επιβεβαιωθεί! Ανυπομονούμε να σας δούμε.',
            'confirmation_details_title' => '📋 Στοιχεία Κράτησης',
            'confirmation_thanks' => 'Σας ευχαριστούμε που επιλέξατε το {{site_name}}!',

            // Cancellation
            'cancellation_title' => 'Ακύρωση Κράτησης',
            'cancellation_header' => 'Κράτηση Ακυρώθηκε',
            'cancellation_greeting' => 'Γεια σας {{customer_name}},',
            'cancellation_message' => 'Η κράτησή σας έχει ακυρωθεί όπως ζητήσατε.',
            'cancellation_details_title' => '📋 Ακυρωμένη Κράτηση',
            'cancellation_thanks' => 'Σας ευχαριστούμε που χρησιμοποιήσατε το {{site_name}}.',

            // Reminder
            'reminder_title' => 'Υπενθύμιση Ραντεβού',
            'reminder_header' => 'Υπενθύμιση Ραντεβού',
            'reminder_greeting' => 'Γεια σας {{customer_name}},',
            'reminder_message' => 'Αυτή είναι μια φιλική υπενθύμιση για το επερχόμενο ραντεβού σας!',
            'reminder_countdown_title' => '🗓️ Το ραντεβού σας είναι {{time_until}}',
            'reminder_countdown_text' => 'Μην ξεχάσετε - ανυπομονούμε να σας δούμε!',
            'reminder_details_title' => '📋 Στοιχεία Ραντεβού',
            'reminder_thanks' => 'Ανυπομονούμε να σας δούμε σύντομα!',

            // Welcome
            'welcome_title' => 'Καλώς ήρθατε στο {{site_name}}',
            'welcome_header' => 'Καλώς ήρθατε!',
            'welcome_greeting' => 'Καλώς ήρθατε, {{customer_name}}!',
            'welcome_message' => 'Σας ευχαριστούμε που εγγραφήκατε στο {{site_name}}! Χαιρόμαστε που είστε μέλος της κοινότητάς μας.',
            'welcome_access_title' => '🔑 Ο Κωδικός Πρόσβασής Σας',
            'welcome_access_desc' => 'Χρησιμοποιήστε αυτόν τον κωδικό για πρόσβαση στις κρατήσεις σας:',
            'welcome_access_note' => 'Κρατήστε αυτόν τον κωδικό ασφαλή για μελλοντική χρήση.',
            'welcome_final' => 'Μπορείτε τώρα να κάνετε κρατήσεις και να διαχειριστείτε τα ραντεβού σας.',

            // Verification
            'verification_title' => 'Κωδικός Επιβεβαίωσης',
            'verification_header' => 'Επιβεβαίωση Email',
            'verification_main_title' => 'Ο Κωδικός Επιβεβαίωσής Σας',
            'verification_desc' => 'Παρακαλώ εισάγετε αυτόν τον κωδικό για να συνεχίσετε:',
            'verification_expiry' => 'Αυτός ο κωδικός λήγει σε {{expiry_minutes}} λεπτά.',



            // Field labels for detail sections
            'field_service' => 'Υπηρεσία',
            'field_date' => 'Ημερομηνία',
            'field_time' => 'Ώρα',
            'field_price' => 'Τιμή',
            'field_duration' => 'Διάρκεια',
            'field_id' => 'Κωδικός',
            'field_name' => 'Όνομα',
            'field_email' => 'Email',
            'field_mobile' => 'Κινητό',

            // Admin templates
            'admin_new_reservation_title' => 'Νέα Κράτηση',
            'admin_new_reservation_header' => 'Νέα Κράτηση',
            'admin_customer_section' => '👤 Στοιχεία Πελάτη',
            'admin_reservation_section' => '📅 Στοιχεία Κράτησης',

            'admin_cancellation_title' => 'Ακύρωση Κράτησης',
            'admin_cancellation_header' => 'Κράτηση Ακυρώθηκε',
            'admin_cancelled_section' => '🚫 Ακυρωμένη Κράτηση',

            'admin_new_customer_title' => 'Νέος Πελάτης',
            'admin_new_customer_header' => 'Εγγραφή Νέου Πελάτη',
            'admin_customer_details_section' => '👤 Στοιχεία Πελάτη',

            'admin_customer_deletion_title' => 'Διαγραφή Πελάτη',
            'admin_customer_deletion_header' => 'Λογαριασμός Πελάτη Διαγράφηκε',
            'admin_deleted_customer_section' => '🗑️ Στοιχεία Διαγραμμένου Πελάτη'
        ]
    ];

    /* ========================================
       TEMPLATE GENERATION METHODS
       ======================================== */

    /**
     * Generate complete email template with inline CSS and multilingual support
     * @param string $templateType - Type of template (confirmation, cancellation, etc.)
     * @param string $language - Language code (en, el)
     * @param string $titleKey - Text key for title
     * @param string $headerKey - Text key for header subtitle
     * @param string $contentHtml - Main content HTML
     * @return string Complete HTML email template
     */
    public function generateTemplate($templateType, $language, $titleKey, $headerKey, $contentHtml)
    {
        $colorScheme = $this->colorSchemes[$templateType] ?? $this->colorSchemes['confirmation'];
        $texts = $this->emailTexts[$language] ?? $this->emailTexts['en'];

        $headerColor = $colorScheme['primary'];
        $headerDark = $colorScheme['dark'];
        $headerIcon = $colorScheme['icon'];

        $title = $texts[$titleKey] ?? $titleKey;
        $headerText = $texts[$headerKey] ?? $headerKey;
        $footerText = $texts['footer_text'] ?? '&copy; {{year}} {{site_name}}';

        $html = '<!DOCTYPE html>
<html lang="' . $language . '">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($title) . '</title>
    <style>
        :root { --header-color: ' . $headerColor . '; --header-dark: ' . $headerDark . '; }
        body { ' . $this->baseStyles['body'] . ' }
        .email-container { ' . $this->baseStyles['container'] . ' }
        .header { ' . str_replace(['var(--header-color)', 'var(--header-dark)'], [$headerColor, $headerDark], $this->baseStyles['header']) . ' }
        .header-icon { ' . $this->baseStyles['header_icon'] . ' }
        .header h1 { ' . $this->baseStyles['header_h1'] . ' }
        .header p { ' . $this->baseStyles['header_p'] . ' }
        .content { ' . $this->baseStyles['content'] . ' }
        .greeting { ' . $this->baseStyles['greeting'] . ' }
        .paragraph { ' . $this->baseStyles['paragraph'] . ' }
        .detail-section { ' . $this->baseStyles['detail_section'] . ' }
        .detail-section h3 { ' . $this->baseStyles['detail_section_h3'] . ' }
        .detail-section-icon { ' . $this->baseStyles['detail_section_icon'] . ' }
        .detail-row { ' . $this->baseStyles['detail_row'] . ' }
        .detail-row:last-child { ' . $this->baseStyles['detail_row_last'] . ' }
        .detail-label { ' . $this->baseStyles['detail_label'] . ' }
        .detail-value { ' . $this->baseStyles['detail_value'] . ' }
        .reservation-id { ' . $this->baseStyles['reservation_id'] . ' }
        .verification-code { ' . $this->baseStyles['verification_code'] . ' }
        .access-code-section { ' . $this->baseStyles['access_code_section'] . ' }
        .access-code-title { ' . $this->baseStyles['access_code_title'] . ' }
        .access-code-desc { ' . $this->baseStyles['access_code_desc'] . ' }
        .access-code { ' . $this->baseStyles['access_code_display'] . ' }
        .access-code-note { ' . $this->baseStyles['access_code_note'] . ' }
        .highlight-box { ' . $this->baseStyles['highlight_box'] . ' }
        .highlight-title { ' . $this->baseStyles['highlight_title'] . ' }
        .highlight-text { ' . $this->baseStyles['highlight_text'] . ' }
        .footer { ' . $this->baseStyles['footer'] . ' }
        .footer-text { ' . $this->baseStyles['footer_text'] . ' }
        .footer-brand { ' . $this->baseStyles['footer_brand'] . ' }
        ' . $this->baseStyles['mobile_container'] . '
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="header-icon">' . $headerIcon . '</div>
            <h1>{{site_name}}</h1>
            <p>' . htmlspecialchars($headerText) . '</p>
        </div>

        <div class="content">
            ' . $contentHtml . '
        </div>

        <div class="footer">
            <div class="footer-text">' . $footerText . '</div>
        </div>
    </div>
</body>
</html>';

        return $html;
    }

    /**
     * Get text for specific language and key
     * @param string $language - Language code
     * @param string $key - Text key
     * @param string $fallback - Fallback text if key not found
     * @return string Localized text
     */
    public function getText($language, $key, $fallback = '')
    {
        return $this->emailTexts[$language][$key] ?? $this->emailTexts['en'][$key] ?? $fallback;
    }

    /**
     * Get all email texts for admin interface
     * @return array All email texts by language
     */
    public function getEmailTexts()
    {
        return $this->emailTexts;
    }





    /**
     * Generate greeting section with multilingual support
     * @param string $language - Language code
     * @param string $greetingKey - Text key for greeting
     * @return string HTML for greeting
     */
    public function generateGreeting($language, $greetingKey)
    {
        $greeting = $this->getText($language, $greetingKey, $greetingKey);
        return '<div class="greeting">' . $greeting . '</div>';
    }

    /**
     * Generate paragraph with multilingual support
     * @param string $language - Language code
     * @param string $textKey - Text key for paragraph
     * @return string HTML for paragraph
     */
    public function generateParagraph($language, $textKey)
    {
        $text = $this->getText($language, $textKey, $textKey);
        return '<div class="paragraph">' . $text . '</div>';
    }

    /**
     * Generate detail section with rows and multilingual support
     * @param string $language - Language code
     * @param string $titleKey - Text key for section title
     * @param string $icon - Icon for section
     * @param array $details - Array of ['labelKey' => 'value'] pairs
     * @return string HTML for detail section
     */
    public function generateDetailSection($language, $titleKey, $icon, $details)
    {
        $title = $this->getText($language, $titleKey, $titleKey);

        $html = '<div class="detail-section">
            <h3><span class="detail-section-icon">' . $icon . '</span>' . $title . '</h3>';

        foreach ($details as $labelKey => $value) {
            $label = $this->getText($language, $labelKey, $labelKey);
            $html .= '<div class="detail-row">
                <div class="detail-label">' . $label . '</div>
                <div class="detail-value">' . $value . '</div>
            </div>';
        }

        $html .= '</div>';
        return $html;
    }

    /**
     * Generate reservation ID display with dashed border
     * @param string $text - Text to display (e.g., "ID: {{reservation_id}}")
     * @return string HTML for reservation ID
     */
    public function generateReservationId($text)
    {
        return '<div class="reservation-id">' . $text . '</div>';
    }

    /**
     * Generate verification code display with dashed border
     * @param string $code - Code variable (e.g., "{{code}}")
     * @return string HTML for verification code
     */
    public function generateVerificationCode($code)
    {
        return '<div class="verification-code">' . $code . '</div>';
    }

    /**
     * Generate access code section with multilingual support
     * @param string $language - Language code
     * @param string $titleKey - Text key for title
     * @param string $descKey - Text key for description
     * @param string $code - Code variable
     * @param string $noteKey - Text key for note
     * @return string HTML for access code section
     */
    public function generateAccessCode($language, $titleKey, $descKey, $code, $noteKey = '')
    {
        $title = $this->getText($language, $titleKey, $titleKey);
        $description = $this->getText($language, $descKey, $descKey);

        $html = '<div class="access-code-section">
            <div class="access-code-title">' . $title . '</div>
            <div class="access-code-desc">' . $description . '</div>
            <div class="access-code">' . $code . '</div>';

        if ($noteKey) {
            $note = $this->getText($language, $noteKey, $noteKey);
            $html .= '<div class="access-code-note">' . $note . '</div>';
        }

        $html .= '</div>';
        return $html;
    }

    /**
     * Generate highlight box (for reminders, important info)
     * @param string $language - Language code
     * @param string $titleKey - Text key for title
     * @param string $textKey - Text key for content
     * @return string HTML for highlight box
     */
    public function generateHighlightBox($language, $titleKey, $textKey)
    {
        $title = $this->getText($language, $titleKey, $titleKey);
        $text = $this->getText($language, $textKey, $textKey);

        return '<div class="highlight-box">
            <div class="highlight-title">' . $title . '</div>
            <div class="highlight-text">' . $text . '</div>
        </div>';
    }
}
