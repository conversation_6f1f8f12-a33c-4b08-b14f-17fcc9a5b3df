<?php
require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

$services = getServices();
?>

<form method="post" action="actions/add_reservation.php" data-validate>
    <div class="form-group">
        <label>Customer Name</label>
        <input type="text" name="customer_name" class="form-control" required>
    </div>
    <div class="form-group">
        <label>Customer Email</label>
        <input type="email" name="customer_email" class="form-control" required>
    </div>
    <div class="form-group">
        <label>Customer Mobile</label>
        <input type="text" name="customer_mobile" class="form-control">
    </div>
    <div class="form-group">
        <label>Service</label>
        <select name="service" class="form-control" required>
            <?php foreach ($services as $key => $svc): ?>
                <option value="<?= htmlspecialchars($key) ?>"><?= htmlspecialchars($svc['name']) ?></option>
            <?php endforeach; ?>
        </select>
    </div>
    <div class="form-group">
        <label>Date</label>
        <input type="date" name="date" class="form-control" required>
    </div>
    <div class="form-group">
        <label>Time</label>
        <input type="time" name="time" class="form-control" required>
    </div>
    <button type="submit" class="btn btn-primary" data-original-text="Add">Quick Add</button>
</form>