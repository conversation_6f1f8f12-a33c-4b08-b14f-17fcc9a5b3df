<?php

/**
 * Centralized ID Generation System
 * Enhanced Short ID with 2-letter prefixes and 10-char unique part
 * Format: XX-XXXXXXXXXX (3 + 10 = 13 chars total)
 * 
 * For GK Radevou Reservation System
 */

class IdGenerator
{
    // Entity prefixes (2 letters + dash)
    const CUSTOMER_PREFIX = 'CU-';
    const EMPLOYEE_PREFIX = 'EM-';
    const SERVICE_PREFIX = 'SV-';
    const RESERVATION_PREFIX = 'RS-';

    // ID part length (after prefix)
    const ID_LENGTH = 10;

    /**
     * Generate a secure random ID with specified prefix
     * 
     * @param string $prefix The prefix to use (e.g., 'CU-')
     * @return string The generated ID (e.g., 'CU-2E67D2EBAF')
     */
    private static function generateId(string $prefix): string
    {
        // Generate random bytes (5 bytes = 10 hex chars)
        $bytes = random_bytes(5);

        // Convert to uppercase hex
        $hexString = strtoupper(bin2hex($bytes));

        return $prefix . $hexString;
    }

    /**
     * Generate unique customer ID
     * Format: CU-XXXXXXXXXX
     * 
     * @return string Customer ID
     */
    public static function generateCustomerId(): string
    {
        return self::generateId(self::CUSTOMER_PREFIX);
    }

    /**
     * Generate unique employee ID  
     * Format: EM-XXXXXXXXXX
     * 
     * @return string Employee ID
     */
    public static function generateEmployeeId(): string
    {
        return self::generateId(self::EMPLOYEE_PREFIX);
    }

    /**
     * Generate unique service ID
     * Format: SV-XXXXXXXXXX
     * 
     * @return string Service ID
     */
    public static function generateServiceId(): string
    {
        return self::generateId(self::SERVICE_PREFIX);
    }

    /**
     * Generate unique reservation ID
     * Format: RS-XXXXXXXXXX
     * 
     * @return string Reservation ID
     */
    public static function generateReservationId(): string
    {
        return self::generateId(self::RESERVATION_PREFIX);
    }

    /**
     * Validate ID format
     * 
     * @param string $id The ID to validate
     * @param string $expectedPrefix Expected prefix (optional)
     * @return bool True if valid format
     */
    public static function validateId(string $id, string $expectedPrefix = ''): bool
    {
        // Check total length (3 + 10 = 13)
        if (strlen($id) !== 13) {
            return false;
        }

        // Check format: XX-XXXXXXXXXX (hex digits only)
        if (!preg_match('/^[A-Z]{2}-[0-9A-F]{10}$/', $id)) {
            return false;
        }

        // Check specific prefix if provided
        if ($expectedPrefix && !str_starts_with($id, $expectedPrefix)) {
            return false;
        }

        return true;
    }

    /**
     * Get entity type from ID
     * 
     * @param string $id The ID to analyze
     * @return string|null Entity type or null if invalid
     */
    public static function getEntityType(string $id): ?string
    {
        if (!self::validateId($id)) {
            return null;
        }

        $prefix = substr($id, 0, 3);

        switch ($prefix) {
            case self::CUSTOMER_PREFIX:
                return 'customer';
            case self::EMPLOYEE_PREFIX:
                return 'employee';
            case self::SERVICE_PREFIX:
                return 'service';
            case self::RESERVATION_PREFIX:
                return 'reservation';
            default:
                return null;
        }
    }

    /**
     * Check if ID belongs to specific entity type
     * 
     * @param string $id The ID to check
     * @param string $entityType Expected entity type
     * @return bool True if matches
     */
    public static function isEntityType(string $id, string $entityType): bool
    {
        return self::getEntityType($id) === $entityType;
    }

    /**
     * Generate multiple unique IDs (for testing/bulk operations)
     * 
     * @param string $prefix The prefix to use
     * @param int $count Number of IDs to generate
     * @return array Array of unique IDs
     */
    public static function generateMultiple(string $prefix, int $count): array
    {
        $ids = [];

        for ($i = 0; $i < $count; $i++) {
            $ids[] = self::generateId($prefix);
        }

        return $ids;
    }

    /**
     * Get collision probability info
     * 
     * @return array Statistics about collision probability
     */
    public static function getCollisionStats(): array
    {
        $totalCombinations = pow(16, self::ID_LENGTH); // 16^10 for hex

        return [
            'id_length' => self::ID_LENGTH,
            'total_combinations' => $totalCombinations,
            'combinations_formatted' => number_format($totalCombinations),
            'collision_probability_at_1M' => (1000000 * 999999) / (2 * $totalCombinations),
            'safe_up_to_ids' => sqrt($totalCombinations / 2) // Birthday paradox
        ];
    }
}

// Note: Backward compatibility functions are defined in includes/functions.php

// Example usage and testing
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    echo "ID Generator Test:\n\n";

    // Generate sample IDs
    echo "Sample IDs:\n";
    echo "Customer:    " . IdGenerator::generateCustomerId() . "\n";
    echo "Employee:    " . IdGenerator::generateEmployeeId() . "\n";
    echo "Service:     " . IdGenerator::generateServiceId() . "\n";
    echo "Reservation: " . IdGenerator::generateReservationId() . "\n\n";

    // Validation test
    $testId = IdGenerator::generateCustomerId();
    echo "Validation Test:\n";
    echo "ID: $testId\n";
    echo "Valid: " . (IdGenerator::validateId($testId) ? 'Yes' : 'No') . "\n";
    echo "Type: " . IdGenerator::getEntityType($testId) . "\n";
    echo "Is Customer: " . (IdGenerator::isEntityType($testId, 'customer') ? 'Yes' : 'No') . "\n\n";

    // Collision statistics
    $stats = IdGenerator::getCollisionStats();
    echo "Collision Statistics:\n";
    echo "Total combinations: " . $stats['combinations_formatted'] . "\n";
    echo "Safe up to: " . number_format($stats['safe_up_to_ids']) . " IDs\n";
    echo "Collision probability at 1M IDs: " . number_format($stats['collision_probability_at_1M'] * 100, 8) . "%\n";
}
