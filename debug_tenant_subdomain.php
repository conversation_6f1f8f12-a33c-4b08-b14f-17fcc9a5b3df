<?php
/**
 * Debug Tenant Subdomain Issues
 * This script helps debug why realma.skrtz.gr/admin returns HTTP 500
 */

echo "<h1>Tenant Subdomain Debug</h1>";
echo "<p>Current URL: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . $_SERVER['REQUEST_URI'] . "</p>";

// Test 1: Basic PHP and includes
echo "<h2>1. Basic PHP Test</h2>";
try {
    echo "✅ PHP is working<br>";
    echo "✅ Current host: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . "<br>";
    echo "✅ Request URI: " . ($_SERVER['REQUEST_URI'] ?? 'unknown') . "<br>";
    echo "✅ Server name: " . ($_SERVER['SERVER_NAME'] ?? 'unknown') . "<br>";
} catch (Exception $e) {
    echo "❌ Basic PHP error: " . $e->getMessage() . "<br>";
}

// Test 2: Config loading
echo "<h2>2. Config Loading</h2>";
try {
    echo "🔍 Loading config...<br>";
    require_once 'includes/config.php';
    echo "✅ Config loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ Config error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
    exit;
}

// Test 3: Tenant initialization
echo "<h2>3. Tenant Initialization</h2>";
try {
    echo "🔍 Loading tenant_init...<br>";
    require_once 'includes/tenant_init.php';
    echo "✅ Tenant init loaded<br>";
    
    echo "🔍 Getting tenant context...<br>";
    require_once 'includes/TenantContext.php';
    
    $tenantId = TenantContext::getTenant();
    echo "✅ Tenant ID: " . ($tenantId ?? 'NULL') . "<br>";
    
    $tenantData = TenantContext::getTenantData();
    echo "✅ Tenant data: " . json_encode($tenantData) . "<br>";
    
    $isValid = TenantContext::isValidTenant();
    echo "✅ Is valid tenant: " . ($isValid ? 'YES' : 'NO') . "<br>";
    
} catch (Exception $e) {
    echo "❌ Tenant init error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
    echo "❌ Stack trace: <pre>" . $e->getTraceAsString() . "</pre><br>";
}

// Test 4: Functions loading
echo "<h2>4. Functions Loading</h2>";
try {
    echo "🔍 Loading functions...<br>";
    require_once 'includes/functions.php';
    echo "✅ Functions loaded<br>";
} catch (Exception $e) {
    echo "❌ Functions error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Test 5: Settings loading
echo "<h2>5. Settings Loading</h2>";
try {
    echo "🔍 Loading settings...<br>";
    $settings = getSettings();
    echo "✅ Settings loaded: " . json_encode($settings) . "<br>";
} catch (Exception $e) {
    echo "❌ Settings error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal settings error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Test 6: Admin functions loading
echo "<h2>6. Admin Functions Loading</h2>";
try {
    echo "🔍 Loading admin functions...<br>";
    require_once 'includes/admin_functions.php';
    echo "✅ Admin functions loaded<br>";
} catch (Exception $e) {
    echo "❌ Admin functions error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Test 7: Handler classes loading
echo "<h2>7. Handler Classes Loading</h2>";
try {
    echo "🔍 Loading handlers...<br>";
    require_once 'includes/reservation_handler.php';
    require_once 'includes/customer_handler.php';
    require_once 'includes/employee_handler.php';
    require_once 'includes/service_handler.php';
    echo "✅ All handlers loaded<br>";
    
    echo "🔍 Creating handler instances...<br>";
    $reservationHandler = new ReservationHandler();
    $customerHandler = new CustomerHandler();
    $employeeHandler = new EmployeeHandler();
    $serviceHandler = new ServiceHandler();
    echo "✅ All handler instances created<br>";
    
} catch (Exception $e) {
    echo "❌ Handler error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal handler error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Test 8: Session test
echo "<h2>8. Session Test</h2>";
try {
    echo "🔍 Starting session...<br>";
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    echo "✅ Session started<br>";
    echo "✅ Session ID: " . session_id() . "<br>";
} catch (Exception $e) {
    echo "❌ Session error: " . $e->getMessage() . "<br>";
}

// Test 9: Database test
echo "<h2>9. Database Test</h2>";
try {
    echo "🔍 Testing database...<br>";
    require_once 'includes/Database.php';
    
    $db = Database::getInstance();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    $result = $conn->query("SELECT COUNT(*) as count FROM employees");
    $row = $result->fetchArray(SQLITE3_ASSOC);
    echo "✅ Employee count: " . $row['count'] . "<br>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<h2>Debug Complete</h2>";
echo "<p>If all tests pass here but admin panel still fails, the issue is specific to admin/index.php</p>";
echo "<p><a href='admin/'>Try Admin Panel</a></p>";
?>
