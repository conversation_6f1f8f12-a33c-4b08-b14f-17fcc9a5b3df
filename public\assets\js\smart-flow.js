/**
 * Simplified Smart Email Flow Handler
 */
document.addEventListener('DOMContentLoaded', function() {
    // Show notification messages
    function showMessage(message, type = 'success') {
        const existingMessages = document.querySelectorAll('.temp-message');
        existingMessages.forEach(msg => msg.remove());

        const messageDiv = document.createElement('div');
        messageDiv.className = `${type}-message temp-message`;
        messageDiv.textContent = message;

        const header = document.querySelector('header');
        if (header && header.nextSibling) {
            header.parentNode.insertBefore(messageDiv, header.nextSibling);
        } else {
            document.querySelector('.container').insertBefore(messageDiv, document.querySelector('.container').firstChild);
        }

        setTimeout(() => messageDiv.remove(), 5000);
        messageDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }



    // DOM elements
    const elements = {
        emailCheckForm: document.getElementById('email-check-form'),
        registrationForm: document.getElementById('registration-form'),
        verificationForm: document.getElementById('verification-form'),
        emailEntryStep: document.getElementById('email-entry-step'),
        registrationStep: document.getElementById('registration-step'),
        verificationStep: document.getElementById('verification-step'),
        customerChoiceStep: document.getElementById('customer-choice-step'),
        reservationForm: document.getElementById('reservation-form')
    };

    // Event listeners
    if (elements.emailCheckForm) {
        elements.emailCheckForm.addEventListener('submit', function(e) {
            e.preventDefault();
            checkEmailExists();
        });
    }

    if (elements.verificationForm) {
        elements.verificationForm.addEventListener('submit', function(e) {
            // Determine the context based on the verification form container
            const verificationContainer = elements.verificationForm.closest('#verification-step');
            const isSmartFlow = verificationContainer && verificationContainer.classList.contains('smart-flow-verified');
            const isBookingContext = verificationContainer && verificationContainer.innerHTML.includes('security_verification_title');

            // Smart flow (existing customers) - use AJAX to show customer choice
            if (isSmartFlow) {
                e.preventDefault();
                handleVerification();
            }
            // New customers or main verification - let PHP handle the flow
            else {
                // Let the form submit normally to PHP
                return true;
            }
        });
    }

    if (elements.registrationForm) {
        elements.registrationForm.addEventListener('submit', function(e) {
            if (!handleRegistration()) {
                e.preventDefault();
            }
        });
    }

    // Check if email exists in database
    function checkEmailExists() {
        const emailInput = document.getElementById('check-email');
        const email = emailInput.value.trim();

        if (!email) {
            showMessage('Please enter a valid email address.', 'error');
            return;
        }

        if (!elements.emailCheckForm) {
            showMessage('Form not found. Please refresh the page.', 'error');
            return;
        }

        // Show loading state
        const submitBtn = elements.emailCheckForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
        submitBtn.disabled = true;
        elements.emailCheckForm.classList.add('email-checking');

        // Make API call to check email
        fetch('api/check_email.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email: email })
        })
        .then(response => response.json())
        .then(data => {
            // Reset button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            elements.emailCheckForm.classList.remove('email-checking');

            if (data.success) {
                if (data.exists) {
                    // Email exists - show verification step
                    showVerificationStep(email);
                } else {
                    // Email doesn't exist - show registration step
                    showRegistrationStep(email);
                }
            } else {
                showMessage(data.message || 'Error checking email. Please try again.', 'error');
            }
        })
        .catch(error => {
            // Reset button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            elements.emailCheckForm.classList.remove('email-checking');
            showMessage('Network error. Please check your connection and try again.', 'error');
        });
    }

    // Handle verification form submission
    function handleVerification() {
        // Get email from hidden input or session (no longer displayed)
        const emailInput = document.querySelector('input[name="email"]');
        const email = emailInput ? emailInput.value : '';
        const code = document.getElementById('verification-code').value.trim();

        if (!code) {
            showMessage('Please enter your verification code or access hash.', 'error');
            return;
        }

        if (!elements.verificationForm) {
            showMessage('Form not found. Please refresh the page.', 'error');
            return;
        }

        // Show loading state
        const submitBtn = elements.verificationForm.querySelector('button[name="verify_access"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verifying...';
        submitBtn.disabled = true;

        // Create form data for PHP submission
        const formData = new FormData();
        formData.append('verify_access', '1');
        formData.append('email', email);
        formData.append('verification_code', code);

        // Submit to PHP for verification
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(html => {
            // Check if verification was successful
            if (html.includes('smart-flow-verified') || (html.includes('Welcome Back') && !html.includes('Invalid') && !html.includes('error'))) {
                // Verification successful - show customer choice
                showCustomerChoiceStep();
            } else {
                // Reset button state
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                showMessage('Invalid verification code or access hash. Please try again.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Reset button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            showMessage('Network error. Please check your connection and try again.', 'error');
        });
    }

    // Handle registration form submission - let it submit normally to PHP
    function handleRegistration() {
        const name = document.getElementById('reg-name').value.trim();
        const mobile = document.getElementById('reg-mobile').value.trim();

        if (!name || !mobile) {
            showMessage('Please fill in all fields.', 'error');
            return false; // Prevent form submission
        }

        // Let the form submit normally to PHP
        return true;
    }

    // Show registration step for new customers
    function showRegistrationStep(email) {
        // Store email in session for the new customer form
        const formData = new FormData();
        formData.append('set_registration_email', email);

        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(() => {
            // Hide email entry step
            if (elements.emailEntryStep) elements.emailEntryStep.style.display = 'none';

            // Populate email field
            document.getElementById('reg-email').value = email;

            // Show registration step with animation
            if (elements.registrationStep) {
                elements.registrationStep.style.display = 'block';
                elements.registrationStep.classList.add('step-transition');
            }

            // Focus on name field
            setTimeout(() => {
                document.getElementById('reg-name').focus();
            }, 100);
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Error setting up registration. Please try again.', 'error');
        });
    }

    // Show verification step for existing customers
    function showVerificationStep(email) {
        // Set session email for PHP verification system
        const formData = new FormData();
        formData.append('set_verification_email', email);

        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(() => {
            // Hide email entry step
            if (elements.emailEntryStep) elements.emailEntryStep.style.display = 'none';

            // Populate hidden email field (no longer displayed)
            const emailInput = document.querySelector('input[name="email"]');
            if (emailInput) {
                emailInput.value = email;
            }

            // Show verification step with animation
            if (elements.verificationStep) {
                elements.verificationStep.style.display = 'block';
                elements.verificationStep.classList.add('step-transition');
            }

            // Focus on verification code field
            setTimeout(() => {
                document.getElementById('verification-code').focus();
            }, 100);
        });
    }

    // Go back to email entry
    window.goBackToEmailEntry = function() {
        // Hide all steps
        if (elements.registrationStep) elements.registrationStep.style.display = 'none';
        if (elements.verificationStep) elements.verificationStep.style.display = 'none';
        if (elements.customerChoiceStep) elements.customerChoiceStep.style.display = 'none';

        // Show email entry step
        if (elements.emailEntryStep) {
            elements.emailEntryStep.style.display = 'block';
            elements.emailEntryStep.classList.add('step-transition');
        }

        // Clear and focus email field
        const emailInput = document.getElementById('check-email');
        if (emailInput) {
            emailInput.value = '';
            emailInput.focus();
        }
    };

    // Send verification code using existing PHP system
    window.sendVerificationCode = function() {
        // Get email from hidden input or session
        const emailInput = document.querySelector('input[name="email"]');
        const email = emailInput ? emailInput.value : '';
        const btn = event.target;
        const originalText = btn.innerHTML;

        // Show loading state
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        btn.disabled = true;

        // Use existing PHP verification system
        const formData = new FormData();
        formData.append('request_verification_code', '1');
        formData.append('email', email);

        // Set session email for PHP
        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(html => {
            // Reset button state
            btn.innerHTML = originalText;
            btn.disabled = false;

            // Check if successful - check for both Greek and English success indicators
            if (html.includes('Verification code sent') ||
                html.includes('sent to your email') ||
                html.includes('κωδικός επαλήθευσης στάλθηκε') ||
                html.includes('στάλθηκε στο email')) {
                showMessage(window.customerTexts?.verification_code_sent || 'Verification code sent to your email!', 'success');
                // Focus on verification code field
                document.getElementById('verification-code').focus();
            } else {
                showMessage(window.customerTexts?.js_error_sending_code || 'Error sending verification code. Please try again.', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Reset button state
            btn.innerHTML = originalText;
            btn.disabled = false;
            showMessage('Network error. Please check your connection and try again.', 'error');
        });
    };

    // Show customer choice step (after successful verification)
    function showCustomerChoiceStep() {
        // Hide verification step
        if (elements.verificationStep) elements.verificationStep.style.display = 'none';

        // Show customer choice step with animation
        if (elements.customerChoiceStep) {
            elements.customerChoiceStep.style.display = 'block';
            elements.customerChoiceStep.classList.add('step-transition');
        }
    }

    // Start booking process
    window.startBooking = function() {
        // Set session flag for booking
        const formData = new FormData();
        formData.append('start_smart_booking', '1');

        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(() => {
            // Preserve language parameter when redirecting
            const currentLang = new URLSearchParams(window.location.search).get('lang');
            let redirectUrl = window.location.pathname + '?smart_booking=1';
            if (currentLang) {
                redirectUrl += '&lang=' + encodeURIComponent(currentLang);
            }
            window.location.href = redirectUrl;
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Error starting booking. Please try again.', 'error');
        });
    };

    // View reservations
    window.viewReservations = function() {
        // Set session flag for viewing reservations
        const formData = new FormData();
        formData.append('start_smart_view_reservations', '1');

        fetch(window.location.href, {
            method: 'POST',
            body: formData
        })
        .then(() => {
            // Preserve language parameter when redirecting
            const currentLang = new URLSearchParams(window.location.search).get('lang');
            let redirectUrl = window.location.pathname + '?smart_view=1';
            if (currentLang) {
                redirectUrl += '&lang=' + encodeURIComponent(currentLang);
            }
            window.location.href = redirectUrl;
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Error viewing reservations. Please try again.', 'error');
        });
    };

    // Handle successful verification (called from PHP redirect)
    if (window.location.search.includes('verification_success=1') || document.getElementById('smart-flow-verified')) {
        // Hide all steps except customer choice
        if (elements.emailEntryStep) elements.emailEntryStep.style.display = 'none';
        if (elements.registrationStep) elements.registrationStep.style.display = 'none';
        if (elements.verificationStep) elements.verificationStep.style.display = 'none';

        // Show customer choice step
        showCustomerChoiceStep();
    }

    // Handle successful registration (called from PHP redirect)
    if (window.location.search.includes('registration_success=1')) {
        // Hide all steps except booking form
        if (elements.emailEntryStep) elements.emailEntryStep.style.display = 'none';
        if (elements.registrationStep) elements.registrationStep.style.display = 'none';
        if (elements.verificationStep) elements.verificationStep.style.display = 'none';
        if (elements.customerChoiceStep) elements.customerChoiceStep.style.display = 'none';

        // Show booking form
        if (elements.reservationForm) elements.reservationForm.style.display = 'block';
        
        // Initialize booking calendar if needed
        if (typeof initializeBookingCalendar === 'function') {
            initializeBookingCalendar();
        }
    }
});

// Global confirmation system for front-end
function confirmAction(message, callback, options = {}) {
    const overlay = document.createElement('div');
    overlay.className = 'confirmation-overlay';
    overlay.id = 'custom-confirm-overlay';

    const title = options.title || 'Confirm Action';
    const confirmText = options.confirmText || 'Confirm';
    const cancelText = options.cancelText || 'Cancel';
    const icon = options.icon || 'fas fa-question-circle';
    const confirmClass = options.danger ? 'confirmation-btn-danger' : 'confirmation-btn-confirm';

    overlay.innerHTML = `
        <div class="confirmation-modal">
            <div class="confirmation-header">
                <i class="${icon} confirmation-icon"></i>
                <h2 class="confirmation-title">${title}</h2>
            </div>
            <div class="confirmation-content">
                <p class="confirmation-message">${message}</p>
                <div class="confirmation-actions">
                    <button type="button" class="confirmation-btn confirmation-btn-cancel" onclick="closeCustomConfirm()">
                        <i class="fas fa-times"></i>
                        ${cancelText}
                    </button>
                    <button type="button" class="confirmation-btn ${confirmClass}" onclick="executeCustomConfirm()">
                        <i class="fas fa-check"></i>
                        ${confirmText}
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(overlay);

    // Store callback
    window._customConfirmCallback = callback;
    window._customConfirmOverlay = overlay;

    // Show modal with animation
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);

    // Prevent background scrolling
    document.body.style.overflow = 'hidden';

    // Close modal when clicking outside
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            closeCustomConfirm();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && overlay.classList.contains('show')) {
            closeCustomConfirm();
        }
    });
}

// Close custom confirmation dialog
function closeCustomConfirm() {
    if (window._customConfirmOverlay) {
        window._customConfirmOverlay.classList.remove('show');
        setTimeout(() => {
            if (window._customConfirmOverlay && window._customConfirmOverlay.parentNode) {
                document.body.removeChild(window._customConfirmOverlay);
            }
            window._customConfirmOverlay = null;
            window._customConfirmCallback = null;
            document.body.style.overflow = '';
        }, 300);
    }
}

// Execute custom confirmed action
function executeCustomConfirm() {
    if (window._customConfirmCallback) {
        window._customConfirmCallback();
    }
    closeCustomConfirm();
}

// Make functions globally accessible
window.confirmAction = confirmAction;
window.closeCustomConfirm = closeCustomConfirm;
window.executeCustomConfirm = executeCustomConfirm;
