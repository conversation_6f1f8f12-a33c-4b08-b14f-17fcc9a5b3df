<?php
// Pagination settings
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$pageSize = isset($_GET['pageSize']) ? max(10, min(100, intval($_GET['pageSize']))) : 20;
$offset = ($page - 1) * $pageSize;

// Search functionality
$searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
$isSearching = !empty($searchTerm);

if ($isSearching) {
    // Get search results with pagination
    $totalServices = $serviceHandler->getSearchServiceCount($searchTerm);
    $totalPages = ceil($totalServices / $pageSize);
    $services = $serviceHandler->searchServices($searchTerm, $pageSize, $offset);
} else {
    // Get all services with pagination
    $totalServices = $serviceHandler->getServiceCount();
    $totalPages = ceil($totalServices / $pageSize);
    $services = $serviceHandler->getServicesPaginated($pageSize, $offset);
}
?>
<div>
    <h2>Services</h2>

    <div class="admin-header">
        <div class="header-left">
            <button class="btn btn-primary" onclick="openModal('Add Service', 'views/modals/add_service.php')">
                <i class="fas fa-plus"></i> Add Service
            </button>
        </div>

        <div class="header-center">
            <div class="view-toggle">
                <span class="view-toggle-label">View:</span>
                <div class="view-toggle-buttons">
                    <button class="view-toggle-btn active" onclick="switchServiceView('cards')" data-view="cards">
                        <i class="fas fa-th-large"></i> Cards
                    </button>
                    <button class="view-toggle-btn" onclick="switchServiceView('table')" data-view="table">
                        <i class="fas fa-table"></i> Table
                    </button>
                </div>
            </div>
        </div>

        <div class="header-right">
            <div class="search-container">
                <i class="search-icon fas fa-search"></i>
                <input type="text"
                    id="services-search"
                    class="search-input"
                    placeholder="Search ALL services by name, price, duration, or employees..."
                    value="<?= htmlspecialchars($searchTerm) ?>"
                    onkeypress="handleServicesSearchKeypress(event)">
                <button class="search-clear" onclick="clearServicesServerSearch()" title="Clear search" <?= $isSearching ? '' : 'style="opacity: 0;"' ?>>
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Search Results Info -->
    <?php if ($isSearching): ?>
        <div class="search-results-info" style="display: flex;">
            <span class="search-results-count">
                Found <?= $totalServices ?> service<?= $totalServices !== 1 ? 's' : '' ?> matching "<?= htmlspecialchars($searchTerm) ?>"
                <?php if ($totalPages > 1): ?>
                    (showing page <?= $page ?> of <?= $totalPages ?>)
                <?php endif; ?>
            </span>
            <button class="search-clear-all" onclick="clearServicesServerSearch()">Clear search</button>
        </div>
    <?php endif; ?>

    <!-- No Results Message -->
    <?php if ($isSearching && $totalServices === 0): ?>
        <div class="no-results" style="display: block;">
            <div class="no-results-icon">
                <i class="fas fa-search"></i>
            </div>
            <h3>No services found</h3>
            <p>No services match "<?= htmlspecialchars($searchTerm) ?>". Try different search terms.</p>
        </div>
    <?php endif; ?>

    <!-- Card View -->
    <div id="services-cards-view" class="card-grid">
        <?php foreach ($services as $s): ?>
            <div class="data-card" data-service="<?= htmlspecialchars($s['id']) ?>">
                <div class="card-header">
                    <div class="card-title"><?= htmlspecialchars($s['name']) ?></div>
                    <div class="card-subtitle"><?= htmlspecialchars($s['duration']) ?> min • €<?= number_format($s['price'], 2) ?></div>
                    <div class="card-id">ID: <?= htmlspecialchars($s['id']) ?></div>
                </div>

                <div class="card-body">
                    <?php if (!empty($s['description'])): ?>
                        <div class="card-field">
                            <i class="fas fa-info-circle"></i>
                            <span><?= htmlspecialchars($s['description']) ?></span>
                        </div>
                    <?php endif; ?>

                    <div class="card-field">
                        <i class="fas fa-clock"></i>
                        <span>Duration: <?= htmlspecialchars($s['duration']) ?> minutes</span>
                    </div>

                    <div class="card-field">
                        <i class="fas fa-euro-sign"></i>
                        <span>Price: €<?= number_format($s['price'], 2) ?></span>
                    </div>

                    <?php if (isset($s['employee_names']) && !empty($s['employee_names'])): ?>
                        <div class="card-field">
                            <i class="fas fa-users"></i>
                            <span>Employees: <?= htmlspecialchars($s['employee_names']) ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($s['booking_count'])): ?>
                        <div class="card-stats">
                            <div class="stat-item">
                                <div class="stat-value"><?= $s['booking_count'] ?></div>
                                <div class="stat-label">Total Bookings</div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="card-footer">
                    <div class="card-actions">
                        <button class="btn btn-sm btn-info" onclick="viewService('<?= htmlspecialchars($s['id'], ENT_QUOTES) ?>')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="editService('<?= htmlspecialchars($s['id'], ENT_QUOTES) ?>')" title="Edit Service">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteService('<?= htmlspecialchars($s['id'], ENT_QUOTES) ?>')" title="Delete Service">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Table View -->
    <div id="services-table-view" style="display: none;">
        <table class="table" data-sortable>
            <thead>
                <tr>
                    <th data-sort>ID</th>
                    <th data-sort>Name</th>
                    <th data-sort>Duration</th>
                    <th data-sort>Price</th>
                    <th>Description</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($services as $s): ?>
                    <tr>
                        <td><?= htmlspecialchars($s['id']) ?></td>
                        <td><?= htmlspecialchars($s['name']) ?></td>
                        <td><?= htmlspecialchars($s['duration']) ?> min</td>
                        <td>€<?= number_format($s['price'], 2) ?></td>
                        <td title="<?= htmlspecialchars($s['description']) ?>">
                            <?= strlen($s['description']) > 50 ? htmlspecialchars(substr($s['description'], 0, 50)) . '...' : htmlspecialchars($s['description']) ?>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewService('<?= htmlspecialchars($s['id'], ENT_QUOTES) ?>')" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="editService('<?= htmlspecialchars($s['id'], ENT_QUOTES) ?>')" title="Edit Service">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteService('<?= htmlspecialchars($s['id'], ENT_QUOTES) ?>')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination Controls -->
<div class="pagination-container">
    <div class="pagination-info">
        <span>
            Showing <?= min($offset + 1, $totalServices) ?>-<?= min($offset + $pageSize, $totalServices) ?> of <?= $totalServices ?>
            <?= $isSearching ? 'matching services' : 'services' ?>
            <?php if ($isSearching): ?>
                <small>(searched across all services)</small>
            <?php endif; ?>
        </span>
        <div class="page-size-selector">
            <label for="services-pageSize">Show:</label>
            <select id="services-pageSize" class="page-size-select" onchange="changeServicesPageSize(this.value)">
                <option value="10" <?= $pageSize == 10 ? 'selected' : '' ?>>10</option>
                <option value="20" <?= $pageSize == 20 ? 'selected' : '' ?>>20</option>
                <option value="50" <?= $pageSize == 50 ? 'selected' : '' ?>>50</option>
                <option value="100" <?= $pageSize == 100 ? 'selected' : '' ?>>100</option>
            </select>
        </div>
        <div class="performance-info">
            <span class="performance-badge">Fast Load</span>
        </div>
    </div>

    <div class="pagination-controls">
        <?php if ($page > 1): ?>
            <button class="pagination-btn" onclick="goToServicesPage(1)" title="First page">
                <i class="fas fa-angle-double-left"></i>
            </button>
            <button class="pagination-btn" onclick="goToServicesPage(<?= $page - 1 ?>)" title="Previous page">
                <i class="fas fa-angle-left"></i>
            </button>
        <?php else: ?>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-double-left"></i>
            </button>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-left"></i>
            </button>
        <?php endif; ?>

        <?php
        // Show page numbers
        $startPage = max(1, $page - 2);
        $endPage = min($totalPages, $page + 2);

        if ($startPage > 1): ?>
            <button class="pagination-btn" onclick="goToServicesPage(1)">1</button>
            <?php if ($startPage > 2): ?>
                <span class="pagination-ellipsis">...</span>
            <?php endif; ?>
        <?php endif; ?>

        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
            <button class="pagination-btn <?= $i == $page ? 'active' : '' ?>" onclick="goToServicesPage(<?= $i ?>)">
                <?= $i ?>
            </button>
        <?php endfor; ?>

        <?php if ($endPage < $totalPages): ?>
            <?php if ($endPage < $totalPages - 1): ?>
                <span class="pagination-ellipsis">...</span>
            <?php endif; ?>
            <button class="pagination-btn" onclick="goToServicesPage(<?= $totalPages ?>)"><?= $totalPages ?></button>
        <?php endif; ?>

        <?php if ($page < $totalPages): ?>
            <button class="pagination-btn" onclick="goToServicesPage(<?= $page + 1 ?>)" title="Next page">
                <i class="fas fa-angle-right"></i>
            </button>
            <button class="pagination-btn" onclick="goToServicesPage(<?= $totalPages ?>)" title="Last page">
                <i class="fas fa-angle-double-right"></i>
            </button>
        <?php else: ?>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-right"></i>
            </button>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-double-right"></i>
            </button>
        <?php endif; ?>
    </div>
</div>

<script>
    // View switching functionality for services
    function switchServiceView(viewType) {
        const cardsView = document.getElementById('services-cards-view');
        const tableView = document.getElementById('services-table-view');
        const toggleButtons = document.querySelectorAll('.view-toggle-btn');

        // Update button states
        toggleButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.view === viewType) {
                btn.classList.add('active');
            }
        });

        // Show/hide views
        if (viewType === 'cards') {
            cardsView.style.display = 'grid';
            tableView.style.display = 'none';
        } else {
            cardsView.style.display = 'none';
            tableView.style.display = 'block';
        }

        // Save preference
        localStorage.setItem('services-view', viewType);
    }

    // Server-side search functionality for services
    function handleServicesSearchKeypress(event) {
        if (event.key === 'Enter') {
            performServicesServerSearch();
        }
    }

    function performServicesServerSearch() {
        const searchTerm = document.getElementById('services-search').value.trim();
        const url = new URL(window.location);

        if (searchTerm) {
            url.searchParams.set('search', searchTerm);
        } else {
            url.searchParams.delete('search');
        }
        url.searchParams.set('page', 1); // Reset to first page

        window.location.href = url.toString();
    }

    function clearServicesServerSearch() {
        const url = new URL(window.location);
        url.searchParams.delete('search');
        url.searchParams.set('page', 1);
        window.location.href = url.toString();
    }

    // Pagination functions for services (preserve search terms)
    function goToServicesPage(page) {
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        // Preserve existing search and pageSize parameters
        window.location.href = url.toString();
    }

    function changeServicesPageSize(pageSize) {
        const url = new URL(window.location);
        url.searchParams.set('pageSize', pageSize);
        url.searchParams.set('page', 1); // Reset to first page
        // Preserve existing search parameter
        window.location.href = url.toString();
    }

    // Service management functions
    function viewService(serviceId) {
        openModal('Service Details', `views/modals/view_service.php?id=${encodeURIComponent(serviceId)}`);
    }

    function editService(serviceId) {
        openModal('Edit Service', `views/modals/edit_service.php?id=${encodeURIComponent(serviceId)}`);
    }

    function deleteService(serviceId) {
        if (confirm('Are you sure you want to delete this service?')) {
            // Implementation for service deletion
            console.log('Delete service:', serviceId);
        }
    }

    // Load saved view preference
    document.addEventListener('DOMContentLoaded', function() {
        const savedView = localStorage.getItem('services-view') || 'cards';
        switchServiceView(savedView);
    });
</script>