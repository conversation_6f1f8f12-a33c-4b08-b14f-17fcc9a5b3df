<?php
require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
require_once '../../../includes/customer_handler.php';
require_once '../../../includes/admin_functions.php';

$customerHandler = new CustomerHandler();
$customers = $customerHandler->getAllCustomers();
$services = getServices();
$employees = getEmployees();

// Get the pre-selected date from URL parameter
$preSelectedDate = $_GET['date'] ?? '';
$formattedDate = $preSelectedDate ? date('l, F j, Y', strtotime($preSelectedDate)) : '';
?>

<div class="add-reservation-modal">
    <?php if ($preSelectedDate): ?>
        <div class="modal-date-header">
            <i class="fas fa-calendar-day"></i>
            <span>Adding reservation for <strong><?= $formattedDate ?></strong></span>
        </div>
    <?php endif; ?>

    <form id="add-reservation-form" method="POST" action="actions/add_reservation.php">
        <div class="form-row">
            <div class="form-group">
                <label for="customer_id">
                    <i class="fas fa-user"></i>
                    Customer
                </label>
                <select name="customer_id" id="customer_id" required class="form-select">
                    <option value="">Choose a customer...</option>
                    <?php foreach ($customers as $customer): ?>
                        <option value="<?= htmlspecialchars($customer['id']) ?>">
                            <?= htmlspecialchars($customer['name']) ?> - <?= htmlspecialchars($customer['email']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="service">
                    <i class="fas fa-spa"></i>
                    Service
                </label>
                <select name="service" id="service" required class="form-select">
                    <option value="">Choose a service...</option>
                    <?php foreach ($services as $key => $service): ?>
                        <option value="<?= htmlspecialchars($key) ?>"
                            data-duration="<?= $service['duration'] ?>"
                            data-price="<?= $service['price'] ?>">
                            <?= htmlspecialchars($service['name']) ?>
                            <span class="service-details">(<?= $service['duration'] ?> mins - €<?= $service['price'] ?>)</span>
                        </option>
                    <?php endforeach; ?>
                </select>
                <div id="service-info" class="service-info-box" style="display: none;">
                    <div class="info-item">
                        <i class="fas fa-clock"></i>
                        <span id="service-duration">-</span> minutes
                    </div>
                    <div class="info-item">
                        <i class="fas fa-euro-sign"></i>
                        <span id="service-price">-</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="employee_id">
                    <i class="fas fa-user-tie"></i>
                    Employee
                </label>
                <select name="employee_id" id="employee_id" class="form-select">
                    <option value="">Auto-assign best available employee</option>
                    <?php foreach ($employees as $empId => $employee): ?>
                        <option value="<?= htmlspecialchars($empId) ?>">
                            <?= htmlspecialchars($employee['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <small class="form-help">Leave empty to automatically assign the best available employee</small>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group half-width">
                <label for="date">
                    <i class="fas fa-calendar-alt"></i>
                    Date
                </label>
                <input type="date" name="date" id="date" value="<?= htmlspecialchars($preSelectedDate) ?>" required class="form-input">
            </div>
            <div class="form-group half-width">
                <label for="time">
                    <i class="fas fa-clock"></i>
                    Time
                </label>
                <input type="time" name="time" id="time" required class="form-input">
            </div>
        </div>

        <div class="form-actions">
            <button type="button" class="btn btn-secondary" onclick="closeModal()">
                <i class="fas fa-times"></i>
                Cancel
            </button>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Add Reservation
            </button>
        </div>
    </form>
</div>


<script>
    document.getElementById('service').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const serviceInfo = document.getElementById('service-info');
        const durationSpan = document.getElementById('service-duration');
        const priceSpan = document.getElementById('service-price');

        if (selectedOption.value) {
            const duration = selectedOption.getAttribute('data-duration');
            const price = selectedOption.getAttribute('data-price');

            durationSpan.textContent = duration;
            priceSpan.textContent = price;
            serviceInfo.style.display = 'flex';
        } else {
            serviceInfo.style.display = 'none';
        }
    });

    // Set minimum date to today
    document.getElementById('date').min = new Date().toISOString().split('T')[0];
</script>