[2025-06-30 17:02:01] New customer added: CU-8F9897932A - g<PERSON><PERSON> (g<PERSON><PERSON><PERSON><PERSON>@gmail.com)
[2025-06-30 17:02:03] Admin attempting to save customer: g<PERSON><PERSON>, g<PERSON><PERSON><PERSON><PERSON>@gmail.com, 6947767541
[2025-06-30 17:02:03] Customer save success: {"success":true,"message":"Customer added successfully","customer_id":"CU-8F9897932A"}
[2025-06-30 17:02:34] New reservation created: RS-54DD3329FD for customer CU-8F9897932A on 2025-07-01 at 10:00
[2025-06-30 17:05:50] New customer created: CU-831F9B7023 - fdsfd (<EMAIL>)
[2025-06-30 17:06:47] All times requested for SV-4BD084AA4C on 2025-08-20 - 18 total slots (18 available, 0 unavailable)
[2025-06-30 17:06:59] New reservation created: RS-20F59A4528 for customer CU-831F9B7023 on 2025-08-20 at 10:15
[2025-06-30 17:07:00] Confirmation email sent successfully for reservation: RS-20F59A4528
[2025-06-30 17:07:01] Admin notification sent successfully for new reservation: RS-20F59A4528
