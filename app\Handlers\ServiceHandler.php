<?php

require_once __DIR__ . '/Database.php';

class ServiceHandler
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Get all services (tenant-aware)
     */
    public function getAllServices(): array
    {
        $services = [];
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT * FROM services WHERE tenant_id = :tenant_id ORDER BY name");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $services[] = [
                'id' => $row['id'],
                'name' => $row['name'],
                'duration' => (int)$row['duration'],
                'price' => (float)$row['price'],
                'description' => $row['description'] ?? ''
            ];
        }

        return $services;
    }

    /**
     * Get services with pagination (tenant-aware)
     */
    public function getServicesPaginated(int $limit = 20, int $offset = 0): array
    {
        $services = [];
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            $stmt = $conn->prepare("SELECT * FROM services WHERE tenant_id = :tenant_id ORDER BY name LIMIT :limit OFFSET :offset");
            $stmt->bindValue(':tenant_id', $tenantId);
            $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
            $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);
        } catch (Exception $e) {
            // Fallback for non-tenant context
            $stmt = $conn->prepare("SELECT * FROM services ORDER BY name LIMIT :limit OFFSET :offset");
            $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
            $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);
        }

        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $services[] = [
                'id' => $row['id'],
                'name' => $row['name'],
                'duration' => (int)$row['duration'],
                'price' => (float)$row['price'],
                'description' => $row['description'] ?? ''
            ];
        }

        return $services;
    }

    /**
     * Get total service count (tenant-aware)
     */
    public function getServiceCount(): int
    {
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM services WHERE tenant_id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        return (int)($row['count'] ?? 0);
    }

    /**
     * Search services with pagination (tenant-aware)
     */
    public function searchServices(string $searchTerm, int $limit = 20, int $offset = 0): array
    {
        $services = [];
        $conn = $this->db->getConnection();

        $searchTerm = '%' . strtolower(trim($searchTerm)) . '%';

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            $stmt = $conn->prepare("
                SELECT s.*, GROUP_CONCAT(e.name) as employee_names,
                       COUNT(DISTINCT r.id) as booking_count
                FROM services s
                LEFT JOIN employee_services es ON s.id = es.service_id
                LEFT JOIN employees e ON es.employee_id = e.id AND e.tenant_id = :tenant_id
                LEFT JOIN reservations r ON s.id = r.service
                WHERE s.tenant_id = :tenant_id
                   AND (LOWER(s.name) LIKE :search
                   OR LOWER(s.description) LIKE :search
                   OR CAST(s.price AS TEXT) LIKE :search
                   OR CAST(s.duration AS TEXT) LIKE :search
                   OR LOWER(e.name) LIKE :search)
                GROUP BY s.id
                ORDER BY s.name
                LIMIT :limit OFFSET :offset
            ");

            $stmt->bindValue(':tenant_id', $tenantId);
            $stmt->bindValue(':search', $searchTerm);
            $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
            $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);
        } catch (Exception $e) {
            // Fallback for non-tenant context
            $stmt = $conn->prepare("
                SELECT s.*, GROUP_CONCAT(e.name) as employee_names,
                       COUNT(DISTINCT r.id) as booking_count
                FROM services s
                LEFT JOIN employee_services es ON s.id = es.service_id
                LEFT JOIN employees e ON es.employee_id = e.id
                LEFT JOIN reservations r ON s.id = r.service
                WHERE LOWER(s.name) LIKE :search
                   OR LOWER(s.description) LIKE :search
                   OR CAST(s.price AS TEXT) LIKE :search
                   OR CAST(s.duration AS TEXT) LIKE :search
                   OR LOWER(e.name) LIKE :search
                GROUP BY s.id
                ORDER BY s.name
                LIMIT :limit OFFSET :offset
            ");

            $stmt->bindValue(':search', $searchTerm);
            $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
            $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);
        }

        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $services[] = [
                'id' => $row['id'],
                'name' => $row['name'],
                'duration' => (int)$row['duration'],
                'price' => (float)$row['price'],
                'description' => $row['description'] ?? '',
                'employee_names' => $row['employee_names'] ?? '',
                'booking_count' => (int)($row['booking_count'] ?? 0)
            ];
        }

        return $services;
    }

    /**
     * Get total count of search results (tenant-aware)
     */
    public function getSearchServiceCount(string $searchTerm): int
    {
        $conn = $this->db->getConnection();

        $searchTerm = '%' . strtolower(trim($searchTerm)) . '%';

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            $stmt = $conn->prepare("
                SELECT COUNT(DISTINCT s.id) as count
                FROM services s
                LEFT JOIN employee_services es ON s.id = es.service_id
                LEFT JOIN employees e ON es.employee_id = e.id AND e.tenant_id = :tenant_id
                WHERE s.tenant_id = :tenant_id
                   AND (LOWER(s.name) LIKE :search
                   OR LOWER(s.description) LIKE :search
                   OR CAST(s.price AS TEXT) LIKE :search
                   OR CAST(s.duration AS TEXT) LIKE :search
                   OR LOWER(e.name) LIKE :search)
            ");

            $stmt->bindValue(':tenant_id', $tenantId);
            $stmt->bindValue(':search', $searchTerm);
        } catch (Exception $e) {
            // Fallback for non-tenant context
            $stmt = $conn->prepare("
                SELECT COUNT(DISTINCT s.id) as count
                FROM services s
                LEFT JOIN employee_services es ON s.id = es.service_id
                LEFT JOIN employees e ON es.employee_id = e.id
                WHERE LOWER(s.name) LIKE :search
                   OR LOWER(s.description) LIKE :search
                   OR CAST(s.price AS TEXT) LIKE :search
                   OR CAST(s.duration AS TEXT) LIKE :search
                   OR LOWER(e.name) LIKE :search
            ");

            $stmt->bindValue(':search', $searchTerm);
        }

        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);

        return (int)($row['count'] ?? 0);
    }

    /**
     * Get service by ID
     */
    public function getServiceById(string $id): ?array
    {
        $conn = $this->db->getConnection();

        $stmt = $conn->prepare("SELECT * FROM services WHERE id = :id LIMIT 1");
        $stmt->bindValue(':id', $id);

        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);

        if ($row) {
            return [
                'id' => $row['id'],
                'name' => $row['name'],
                'duration' => (int)$row['duration'],
                'price' => (float)$row['price'],
                'description' => $row['description'] ?? ''
            ];
        }

        return null;
    }

    /**
     * Get employees for a service (tenant-aware)
     */
    public function getServiceEmployees(string $serviceId): array
    {
        $conn = $this->db->getConnection();
        $employees = [];

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("
            SELECT e.*
            FROM employees e
            JOIN employee_services es ON e.id = es.employee_id
            WHERE es.service_id = :service_id AND e.tenant_id = :tenant_id
            ORDER BY e.name
        ");

        $stmt->bindValue(':service_id', $serviceId);
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $employees[] = [
                'id' => $row['id'],
                'name' => $row['name'],
                'email' => $row['email'] ?? '',
                'phone' => $row['phone'] ?? '',
                'working_hours' => json_decode($row['working_hours'] ?? '{}', true),
                'status' => $row['status'] ?? 'active',
                'created_at' => $row['created_at']
            ];
        }

        return $employees;
    }

    /**
     * Get service statistics
     */
    public function getServiceStats(string $serviceId): array
    {
        $conn = $this->db->getConnection();

        // Get booking statistics
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as total_bookings,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_bookings,
                COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_bookings,
                COUNT(CASE WHEN date >= date('now') AND status = 'confirmed' THEN 1 END) as upcoming_bookings,
                MAX(date) as last_booking_date
            FROM reservations 
            WHERE service = :service_id
        ");

        $stmt->bindValue(':service_id', $serviceId);
        $result = $stmt->execute();
        $stats = $result->fetchArray(SQLITE3_ASSOC);

        return [
            'total_bookings' => (int)($stats['total_bookings'] ?? 0),
            'completed_bookings' => (int)($stats['completed_bookings'] ?? 0),
            'cancelled_bookings' => (int)($stats['cancelled_bookings'] ?? 0),
            'upcoming_bookings' => (int)($stats['upcoming_bookings'] ?? 0),
            'last_booking_date' => $stats['last_booking_date']
        ];
    }
}
