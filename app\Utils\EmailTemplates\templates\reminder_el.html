<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{site_name}} Email</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: white;
            text-align: center;
            padding: 30px 20px;
        }
        .header-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 700;
        }
        .header p {
            margin: 0;
            font-size: 18px;
            opacity: 0.9;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .paragraph {
            font-size: 16px;
            line-height: 1.8;
            color: #495057;
            margin-bottom: 25px;
        }
        .reservation-id {
            background: #fff;
            border: 2px dashed #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 25px 0;
            text-align: center;
            font-weight: 700;
            color: #ffc107;
            font-size: 16px;
            font-family: "Courier New", monospace;
            letter-spacing: 1px;
        }
        .verification-code {
            background: #fff;
            border: 3px dashed #28a745;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            font-size: 32px;
            font-weight: 700;
            color: #28a745;
            letter-spacing: 6px;
            font-family: "Courier New", monospace;
            text-align: center;
            box-shadow: 0 4px 12px rgba(40,167,69,0.15);
        }
        .access-code {
            background: #fff;
            border: 3px dashed #ffc107;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            font-size: 28px;
            font-weight: 700;
            color: #ffc107;
            letter-spacing: 4px;
            font-family: "Courier New", monospace;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,123,255,0.15);
        }
        .detail-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #ffc107;
        }
        .detail-section h3 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .detail-section-icon {
            font-size: 20px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
            font-size: 15px;
        }
        .detail-value {
            color: #212529;
            font-size: 15px;
            font-weight: 500;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        .footer-text {
            font-size: 14px;
            color: #6c757d;
        }
        
        /* Responsive */
        @media (max-width: 600px) {
            body { padding: 10px; }
            .email-container { border-radius: 8px; }
            .header { padding: 20px 15px; }
            .content { padding: 25px 20px; }
            .footer { padding: 15px 20px; }
            .detail-row { flex-direction: column; align-items: flex-start; gap: 5px; }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="header-icon">⏰</div>
            <h1>{{site_name}}</h1>
            <p>Υπενθύμιση Ραντεβού</p>
        </div>

        <div class="content">
            <div class="greeting">Γεια σας {{customer_name}},</div>

            <div class="paragraph">Αυτή είναι μια φιλική υπενθύμιση για το επερχόμενο ραντεβού σας.</div>

            <div class="reservation-id">Κωδικός: {{reservation_id}}</div>

            <div class="detail-section">
                <h3><span class="detail-section-icon">📋</span>Στοιχεία Ραντεβού</h3>
                <div class="detail-row">
                    <div class="detail-label">Υπηρεσία</div>
                    <div class="detail-value">{{service_name}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Ημερομηνία</div>
                    <div class="detail-value">{{date}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Ώρα</div>
                    <div class="detail-value">{{time}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Διάρκεια</div>
                    <div class="detail-value">{{duration}} λεπτά</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Τιμή</div>
                    <div class="detail-value">€{{price}}</div>
                </div>
            </div>

            <div class="paragraph">Παρακαλώ φτάστε 5 λεπτά νωρίτερα. Για αλλαγή ραντεβού, επικοινωνήστε μαζί μας.</div>
        </div>

        <div class="footer">
            <div class="footer-text">&copy; {{year}} {{site_name}}. Όλα τα δικαιώματα διατηρούνται.</div>
        </div>
    </div>
</body>
</html>