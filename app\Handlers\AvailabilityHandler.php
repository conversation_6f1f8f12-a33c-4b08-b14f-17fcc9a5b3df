<?php
require_once 'config.php';
require_once 'Database.php';
require_once 'functions.php';

class AvailabilityHandler
{
    private $db;
    private $settings;

    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->settings = getSettings();
    }

    /**
     * Check if a date is available for booking
     */
    public function isDateAvailable(string $date): bool
    {
        // Check if date is in the past
        if (strtotime($date) < strtotime(date('Y-m-d'))) {
            return false;
        }

        // Get day of week name (Monday, Tuesday, etc.)
        $dayName = date('l', strtotime($date));

        // Check if this day has business hours configured
        $businessHours = $this->settings['business_hours'] ?? [];
        $dayEnabled = isset($businessHours[$dayName]) && !empty($businessHours[$dayName]);
        if (!$dayEnabled) {
            return false;
        }

        // Check if this is a special day (holiday, etc.)
        $specialDays = $this->settings['special_days'] ?? [];
        if (isset($specialDays[$date]) && empty($specialDays[$date])) {
            return false; // This is a closed day
        }

        return true;
    }

    /**
     * Get available time slots for a date and service
     */
    public function getAvailableTimeSlots(string $date, string $serviceId): array
    {
        // Check if date is available first
        if (!$this->isDateAvailable($date)) {
            return [];
        }

        // Get service details
        $services = getServices();
        if (!isset($services[$serviceId])) {
            return [];
        }
        $service = $services[$serviceId];
        $serviceDuration = (int)$service['duration']; // in minutes

        // Get day of week name (Monday, Tuesday, etc.)
        $dayName = date('l', strtotime($date));

        // Get working hours for this day
        $workingHours = [];
        if (isset($this->settings['special_days'][$date]) && !empty($this->settings['special_days'][$date])) {
            // This is a special day with custom hours
            $workingHours = $this->settings['special_days'][$date];
        } else {
            // Regular day - use business_hours
            $workingHours = $this->settings['business_hours'][$dayName] ?? [];
        }

        if (empty($workingHours)) {
            return []; // No working hours for this day
        }

        // Get employees who can perform this service
        require_once __DIR__ . '/admin_functions.php';
        $availableEmployees = getEmployeesForService($serviceId);

        if (empty($availableEmployees)) {
            return []; // No employees can perform this service
        }

        // Collect all possible time slots from all employees
        $allPossibleSlots = [];

        foreach ($availableEmployees as $employeeId => $employee) {
            $employeeWorkingHours = $employee['working_hours'][$dayName] ?? [];

            if (empty($employeeWorkingHours)) {
                continue; // Employee doesn't work on this day
            }

            // Generate time slots for this employee
            foreach ($employeeWorkingHours as $period) {
                $start = strtotime($period['start']);
                $end = strtotime($period['end']);

                // Generate slots at 15-minute intervals
                for ($time = $start; $time <= $end - ($serviceDuration * 60); $time += 15 * 60) {
                    $slotTime = date('H:i', $time);

                    if (!isset($allPossibleSlots[$slotTime])) {
                        $endTime = $time + ($serviceDuration * 60);
                        // Format time based on language - get from request parameters
                        $currentLang = $_GET['lang'] ?? $_POST['lang'] ?? 'primary';
                        if ($currentLang === 'primary') {
                            // Greek format: 24-hour with πμ/μμ
                            $startFormatted = date('G:i', $time);
                            $endFormatted = date('G:i', $endTime);
                            $ampm = date('G', $time) < 12 ? ' πμ' : ' μμ';
                            $formatted_time = $startFormatted . ' - ' . $endFormatted . $ampm;
                        } else {
                            // English format: 12-hour with AM/PM
                            $formatted_time = date('g:i A', $time) . ' - ' . date('g:i A', $endTime);
                        }

                        $allPossibleSlots[$slotTime] = [
                            'time' => $slotTime,
                            'formatted_time' => $formatted_time,
                            'available_employees' => []
                        ];
                    }

                    // Add this employee to the available employees for this slot
                    $allPossibleSlots[$slotTime]['available_employees'][] = $employeeId;
                }
            }
        }

        // Get existing reservations for this date
        $existingReservations = $this->getExistingReservations($date);

        // Filter slots based on employee availability
        $availableSlots = [];
        foreach ($allPossibleSlots as $slot) {
            $slotStart = strtotime($date . ' ' . $slot['time']);
            $slotEnd = $slotStart + ($serviceDuration * 60);

            // Check which employees are available for this slot
            $availableEmployeesForSlot = $slot['available_employees'];

            foreach ($existingReservations as $reservation) {
                $reservationStart = strtotime($date . ' ' . $reservation['time']);
                $reservationService = $reservation['service'];
                $reservationServiceDuration = (int)($services[$reservationService]['duration'] ?? 60);
                $reservationEnd = $reservationStart + ($reservationServiceDuration * 60);
                $reservationEmployeeId = $reservation['employee_id'] ?? null;

                // Check if this slot overlaps with an existing reservation
                if (
                    ($slotStart >= $reservationStart && $slotStart < $reservationEnd) ||
                    ($slotEnd > $reservationStart && $slotEnd <= $reservationEnd) ||
                    ($slotStart <= $reservationStart && $slotEnd >= $reservationEnd)
                ) {
                    // Remove the busy employee from available employees
                    if ($reservationEmployeeId && in_array($reservationEmployeeId, $availableEmployeesForSlot)) {
                        $availableEmployeesForSlot = array_diff($availableEmployeesForSlot, [$reservationEmployeeId]);
                    }
                }
            }

            // If at least one employee is available, the slot is available
            if (!empty($availableEmployeesForSlot)) {
                $availableSlots[] = [
                    'time' => $slot['time'],
                    'formatted_time' => $slot['formatted_time'],
                    'available_employees' => array_values($availableEmployeesForSlot)
                ];
            }
        }

        // Sort slots by time
        usort($availableSlots, function ($a, $b) {
            return strcmp($a['time'], $b['time']);
        });

        return $availableSlots;
    }

    /**
     * Get the best available employee for a specific time slot and service
     * Uses "least bookings that day" logic for fair distribution
     */
    public function getBestAvailableEmployee(string $date, string $time, string $serviceId): ?string
    {
        $availableSlots = $this->getAvailableTimeSlots($date, $serviceId);

        foreach ($availableSlots as $slot) {
            if ($slot['time'] === $time && !empty($slot['available_employees'])) {
                // Get employee with least bookings for this date
                return $this->getEmployeeWithLeastBookings($date, $slot['available_employees']);
            }
        }

        return null;
    }

    /**
     * Get employee with least confirmed bookings for a specific date
     */
    private function getEmployeeWithLeastBookings(string $date, array $availableEmployees): string
    {
        $conn = $this->db->getConnection();
        $employeeBookings = [];

        // Count confirmed reservations for each available employee on this date
        foreach ($availableEmployees as $employeeId) {
            $stmt = $conn->prepare("
                SELECT COUNT(*) as booking_count
                FROM reservations
                WHERE employee_id = :employee_id
                  AND date = :date
                  AND status = 'confirmed'
            ");
            $stmt->bindValue(':employee_id', $employeeId);
            $stmt->bindValue(':date', $date);
            $result = $stmt->execute();
            $row = $result->fetchArray(SQLITE3_ASSOC);

            $employeeBookings[$employeeId] = (int)($row['booking_count'] ?? 0);
        }

        // Find employee(s) with minimum bookings
        $minBookings = min($employeeBookings);
        $employeesWithMinBookings = array_keys($employeeBookings, $minBookings);

        // If tie, return first employee (could be enhanced with round-robin)
        return $employeesWithMinBookings[0];
    }

    /**
     * Check if a specific employee is available for a time slot
     */
    public function isEmployeeAvailable(string $date, string $time, string $serviceId, string $employeeId): bool
    {
        $availableSlots = $this->getAvailableTimeSlots($date, $serviceId);

        foreach ($availableSlots as $slot) {
            if ($slot['time'] === $time) {
                return in_array($employeeId, $slot['available_employees']);
            }
        }

        return false;
    }

    /**
     * Get all time slots for a date and service with availability status
     */
    public function getAllTimeSlotsWithStatus(string $date, string $serviceId): array
    {
        // Get available slots using the new employee-aware system
        $availableSlots = $this->getAvailableTimeSlots($date, $serviceId);

        // Convert to the format expected by the API (with available/unavailable status)
        $allSlots = [];

        // First, collect all available slots
        foreach ($availableSlots as $slot) {
            $allSlots[] = [
                'time' => $slot['time'],
                'formatted_time' => $slot['formatted_time'],
                'available' => true,
                'available_employees' => $slot['available_employees'] ?? []
            ];
        }

        // Now we need to also include unavailable slots for a complete view
        // Get all possible time slots from business hours and mark unavailable ones

        // Check if date is available first
        if (!$this->isDateAvailable($date)) {
            return $allSlots; // Return only available slots if date is not available
        }

        // Get service details
        $services = getServices();
        if (!isset($services[$serviceId])) {
            return $allSlots;
        }

        $service = $services[$serviceId];
        $serviceDuration = (int)$service['duration']; // in minutes

        // Get day of week name (Monday, Tuesday, etc.)
        $dayName = date('l', strtotime($date));

        // Get employees who can perform this service
        require_once __DIR__ . '/admin_functions.php';
        $availableEmployees = getEmployeesForService($serviceId);

        if (empty($availableEmployees)) {
            return $allSlots; // Return only available slots if no employees
        }

        // Collect all possible time slots from all employees
        $allPossibleSlots = [];

        foreach ($availableEmployees as $employeeId => $employee) {
            $employeeWorkingHours = $employee['working_hours'][$dayName] ?? [];

            if (empty($employeeWorkingHours)) {
                continue; // Employee doesn't work on this day
            }

            // Generate time slots for this employee
            foreach ($employeeWorkingHours as $period) {
                $start = strtotime($period['start']);
                $end = strtotime($period['end']);

                // Generate slots at 15-minute intervals
                for ($time = $start; $time <= $end - ($serviceDuration * 60); $time += 15 * 60) {
                    $slotTime = date('H:i', $time);

                    if (!isset($allPossibleSlots[$slotTime])) {
                        $endTime = $time + ($serviceDuration * 60);
                        // Format time based on language - get from request parameters
                        $currentLang = $_GET['lang'] ?? $_POST['lang'] ?? 'primary';
                        if ($currentLang === 'primary') {
                            // Greek format: 24-hour with πμ/μμ
                            $startFormatted = date('G:i', $time);
                            $endFormatted = date('G:i', $endTime);
                            $ampm = date('G', $time) < 12 ? ' πμ' : ' μμ';
                            $formatted_time = $startFormatted . ' - ' . $endFormatted . $ampm;
                        } else {
                            // English format: 12-hour with AM/PM
                            $formatted_time = date('g:i A', $time) . ' - ' . date('g:i A', $endTime);
                        }

                        $allPossibleSlots[$slotTime] = [
                            'time' => $slotTime,
                            'formatted_time' => $formatted_time,
                            'available' => false, // Will be updated if available
                            'available_employees' => []
                        ];
                    }
                }
            }
        }

        // Mark available slots
        $availableSlotTimes = array_column($availableSlots, 'time');
        foreach ($allPossibleSlots as $slotTime => $slot) {
            if (in_array($slotTime, $availableSlotTimes)) {
                // Find the corresponding available slot data
                foreach ($availableSlots as $availableSlot) {
                    if ($availableSlot['time'] === $slotTime) {
                        $allPossibleSlots[$slotTime]['available'] = true;
                        $allPossibleSlots[$slotTime]['available_employees'] = $availableSlot['available_employees'];
                        break;
                    }
                }
            }
        }

        // Sort by time and return
        uasort($allPossibleSlots, function ($a, $b) {
            return strcmp($a['time'], $b['time']);
        });

        return array_values($allPossibleSlots);
    }

    /**
     * Get existing reservations for a date
     */
    private function getExistingReservations(string $date): array
    {
        $conn = $this->db->getConnection();
        $stmt = $conn->prepare("
            SELECT * FROM reservations 
            WHERE date = :date AND status = 'confirmed'
        ");
        $stmt->bindValue(':date', $date);
        $result = $stmt->execute();

        $reservations = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $reservations[] = $row;
        }

        return $reservations;
    }
}
