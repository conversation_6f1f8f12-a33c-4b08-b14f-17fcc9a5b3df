<?php

require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/reservation_handler.php';
require_once '../../includes/customer_handler.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$customerId = sanitize_input($_POST['customer_id'] ?? '');
$service = sanitize_input($_POST['service'] ?? '');
$date = sanitize_input($_POST['date'] ?? '');
$time = sanitize_input($_POST['time'] ?? '');
$employeeId = sanitize_input($_POST['employee_id'] ?? '');

if (!$customerId || !$service || !$date || !$time) {
    echo json_encode(['success' => false, 'message' => 'All required fields must be filled']);
    exit;
}

$reservationHandler = new ReservationHandler();
$customerHandler = new CustomerHandler();

// Check if customer exists
$customer = $customerHandler->getCustomerById($customerId);
if (!$customer) {
    echo json_encode(['success' => false, 'message' => 'Selected customer does not exist']);
    exit;
}

// Add reservation
if (!method_exists($reservationHandler, 'addReservation')) {
    echo json_encode(['success' => false, 'message' => 'Add function not implemented']);
    exit;
}

$result = $reservationHandler->addReservation(
    $customerId,
    $service,
    $date,
    $time,
    $employeeId
);

if (is_array($result) && ($result['success'] ?? false)) {
    // Optionally update customer stats
    if (method_exists($customerHandler, 'updateCustomerStats')) {
        $customerHandler->updateCustomerStats($customer['email'], $date);
    }
    echo json_encode(['success' => true, 'message' => 'Reservation added successfully', 'refresh' => true]);
} else {
    $msg = is_array($result) ? ($result['message'] ?? 'Failed to add reservation') : $result;
    echo json_encode(['success' => false, 'message' => $msg]);
}
