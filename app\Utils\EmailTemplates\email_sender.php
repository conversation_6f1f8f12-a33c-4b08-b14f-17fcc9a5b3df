<?php

/**
 * ========================================
 * MULTILINGUAL EMAIL TEMPLATE MANAGER
 * ========================================
 *
 * Handles all email sending with beautiful HTML templates
 * and automatic language detection for multilingual support.
 *
 * <AUTHOR> System
 * @version 2.0
 */

require_once __DIR__ . '/email_template_selector.php';

class EmailSender
{
    private $settings;
    private $currentTemplateLanguage = 'en';
    private $templateSelector;

    public function __construct()
    {
        $this->settings = require __DIR__ . '/../settings.php';
        $this->templateSelector = new EmailTemplateSelector();
    }

    /**
     * Send verification email
     */
    public function sendVerificationEmail(string $email, string $code): bool
    {
        $subject = 'Your Verification Code - ' . $this->settings['site_name'];

        // Try to get customer name from session or email
        $customerName = $this->getCustomerNameForVerification($email);

        $templateData = [
            'site_name' => $this->settings['site_name'],
            'customer_name' => $customerName,
            'verification_code' => $code,
            'year' => date('Y')
        ];

        // For verification emails, try to get customer ID from session or database
        $customerId = null;
        if (isset($_SESSION['temp_customer_id'])) {
            $customerId = $_SESSION['temp_customer_id'];
        }

        $htmlContent = $this->loadTemplate('verification', $templateData, $customerId);
        $textContent = $this->getVerificationTextContent($templateData);

        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }

    /**
     * Send confirmation email
     */
    public function sendConfirmationEmail(string $email, array $reservationData, array $customerData): bool
    {
        $services = getServices();
        $service = $services[$reservationData['service']] ?? null;

        if (!$service) return false;

        $subject = 'Reservation Confirmed - ' . $this->settings['site_name'];

        // Calculate end time
        $startTime = new DateTime($reservationData['time']);
        $endTime = clone $startTime;
        $endTime->add(new DateInterval('PT' . $service['duration'] . 'M'));

        $templateData = [
            'site_name' => $this->settings['site_name'],
            'customer_name' => $customerData['name'],
            'reservation_id' => $reservationData['id'],
            'service_name' => $service['name'],
            'service_duration' => $service['duration'],
            'service_price' => $service['price'],
            'date' => format_date($reservationData['date']),
            'time' => format_time_range($reservationData['time'], (int)$service['duration']),
            'end_time' => $endTime->format('H:i'),
            'year' => date('Y')
        ];

        $customerId = $customerData['id'] ?? null;
        $htmlContent = $this->loadTemplate('confirmation', $templateData, $customerId);
        $textContent = $this->getConfirmationTextContent($templateData);

        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }

    /**
     * Send cancellation email
     */
    public function sendCancellationEmail(string $email, array $reservationData, array $customerData): bool
    {
        $services = getServices();
        $service = $services[$reservationData['service']] ?? null;

        if (!$service) return false;

        $subject = 'Reservation Cancelled - ' . $this->settings['site_name'];

        $customerId = $customerData['id'] ?? null;

        $templateData = [
            'site_name' => $this->settings['site_name'],
            'customer_name' => $customerData['name'],
            'reservation_id' => $reservationData['id'],
            'service_name' => $service['name'],
            'date' => $this->formatDateForEmail($reservationData['date'], $customerId),
            'time' => format_time_range($reservationData['time'], (int)$service['duration']),
            'year' => date('Y')
        ];
        $htmlContent = $this->loadTemplate('cancellation', $templateData, $customerId);
        $textContent = $this->getCancellationTextContent($templateData);

        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }

    /**
     * Send reminder email
     */
    public function sendReminderEmail(string $email, array $reservationData, array $customerData): bool
    {
        $services = getServices();
        $service = $services[$reservationData['service']] ?? null;

        if (!$service) return false;

        $subject = 'Appointment Reminder - ' . $this->settings['site_name'];

        // Calculate time until appointment
        $appointmentDateTime = new DateTime($reservationData['date'] . ' ' . $reservationData['time']);
        $now = new DateTime();
        $interval = $now->diff($appointmentDateTime);

        $timeUntil = '';
        if ($interval->days > 0) {
            $timeUntil = $interval->days . ' day' . ($interval->days > 1 ? 's' : '');
        } else {
            $timeUntil = $interval->h . ' hour' . ($interval->h > 1 ? 's' : '');
        }

        $templateData = [
            'site_name' => $this->settings['site_name'],
            'customer_name' => $customerData['name'],
            'reservation_id' => $reservationData['id'],
            'service_name' => $service['name'],
            'service_duration' => $service['duration'],
            'date' => format_date($reservationData['date']),
            'time' => format_time_range($reservationData['time'], (int)$service['duration']),
            'time_until' => $timeUntil,
            'year' => date('Y')
        ];

        $customerId = $customerData['id'] ?? null;
        $htmlContent = $this->loadTemplate('reminder', $templateData, $customerId);
        $textContent = $this->getReminderTextContent($templateData);

        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }

    /**
     * Send welcome email
     */
    public function sendWelcomeEmail(string $email, array $customerData): bool
    {
        $subject = 'Welcome to ' . $this->settings['site_name'] . '!';

        $templateData = [
            'site_name' => $this->settings['site_name'],
            'customer_name' => $customerData['name'],
            'access_code' => $customerData['user_hash'],
            'year' => date('Y')
        ];

        $customerId = $customerData['id'] ?? null;
        $htmlContent = $this->loadTemplate('welcome', $templateData, $customerId);
        $textContent = $this->getWelcomeTextContent($templateData);

        return $this->sendEmail($email, $subject, $htmlContent, $textContent);
    }


    /**
     * Load HTML template with multilingual support and replace placeholders
     */
    private function loadTemplate(string $templateName, array $data, mixed $customerId = null): string
    {
        // Get customer language preference
        $customerLanguage = $this->templateSelector->getCustomerLanguage($customerId);

        // Set current template language for date formatting
        $this->currentTemplateLanguage = $customerLanguage;

        // Get localized template content
        $content = $this->templateSelector->getTemplateContent($templateName, $customerLanguage);

        if ($content === false) {
            throw new Exception("Email template not found: $templateName (language: $customerLanguage)");
        }

        return $this->processTemplateContent($content, $data);
    }

    /**
     * Load HTML template with specific language and replace placeholders
     */
    private function loadTemplateWithLanguage(string $templateName, array $data, string $language): string
    {
        // Get localized template content
        $content = $this->templateSelector->getTemplateContent($templateName, $language);

        if ($content === false) {
            throw new Exception("Email template not found: $templateName (language: $language)");
        }

        return $this->processTemplateContent($content, $data);
    }

    /**
     * Process template content and replace placeholders
     */
    private function processTemplateContent(string $content, array $data): string
    {

        // Handle cancelled reservations section
        if (isset($data['cancelled_reservations']) && is_array($data['cancelled_reservations']) && count($data['cancelled_reservations']) > 0) {
            $reservationsHtml = '';
            foreach ($data['cancelled_reservations'] as $reservation) {
                $reservationsHtml .= '<div class="reservation-item">';
                $reservationsHtml .= '<strong>' . htmlspecialchars($reservation['service']) . '</strong> - ';
                $reservationsHtml .= htmlspecialchars(format_date($reservation['date'])) . ' at ';
                $reservationsHtml .= htmlspecialchars(format_time_range($reservation['time'], (int)$reservation['duration']));
                $reservationsHtml .= '</div>';
            }

            $sectionContent = '<div class="cancelled-reservations">';
            $sectionContent .= '<h3>📅 Cancelled Reservations (' . count($data['cancelled_reservations']) . ')</h3>';
            $sectionContent .= $reservationsHtml;
            $sectionContent .= '</div>';

            $content = str_replace('{{#cancelled_reservations_section}}', $sectionContent, $content);
            $content = str_replace('{{/cancelled_reservations_section}}', '', $content);
        } else {
            // Remove the section if no cancelled reservations
            $content = preg_replace('/\{\{#cancelled_reservations_section\}\}.*?\{\{\/cancelled_reservations_section\}\}/s', '', $content);
        }

        // Replace simple placeholders
        foreach ($data as $key => $value) {
            if (!is_array($value)) {
                $content = str_replace('{{' . $key . '}}', htmlspecialchars((string)$value, ENT_QUOTES, 'UTF-8'), $content);
            }
        }

        return $content;
    }



    /**
     * Send multipart email (HTML + Text)
     */
    private function sendEmail(string $email, string $subject, string $htmlContent, string $textContent): bool
    {
        $boundary = 'boundary_' . md5(uniqid());

        // Email headers
        $headers = "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: multipart/alternative; boundary=\"$boundary\"\r\n";
        $headers .= "From: " . $this->settings['site_name'] . " <" . $this->settings['email_from'] . ">\r\n";
        $headers .= "Reply-To: " . $this->settings['email_from'] . "\r\n";
        $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

        // Multipart message
        $message = "--$boundary\r\n";
        $message .= "Content-Type: text/plain; charset=UTF-8\r\n";
        $message .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
        $message .= $textContent . "\r\n";
        $message .= "--$boundary\r\n";
        $message .= "Content-Type: text/html; charset=UTF-8\r\n";
        $message .= "Content-Transfer-Encoding: 7bit\r\n\r\n";
        $message .= $htmlContent . "\r\n";
        $message .= "--$boundary--";

        return mail($email, $subject, $message, $headers);
    }

    /**
     * Format date for email based on customer language preference
     */
    private function formatDateForEmail(string $date, mixed $customerId = null): string
    {
        try {
            $dateTime = new DateTime($date);

            // Determine language based on customer preference or template language
            $language = $this->getCustomerLanguage($customerId);

            if ($language === 'el') {
                // Greek formatting
                $formatter = new IntlDateFormatter(
                    'el_GR',
                    IntlDateFormatter::FULL,
                    IntlDateFormatter::NONE,
                    null,
                    null,
                    'EEEE, d MMMM yyyy'
                );
                return $formatter->format($dateTime);
            } else {
                // English formatting
                return $dateTime->format('l, F j, Y');
            }
        } catch (Exception) {
            return $date;
        }
    }

    /**
     * Get customer language preference
     */
    private function getCustomerLanguage(mixed $customerId = null): string
    {
        // For now, determine language based on template being loaded
        // This will be set by the loadTemplate method
        return $this->currentTemplateLanguage ?? 'en';
    }

    /**
     * Get customer name for verification email
     */
    private function getCustomerNameForVerification(string $email): string
    {
        // Try to get customer name from session first
        if (isset($_SESSION['customer']['name'])) {
            return $_SESSION['customer']['name'];
        }

        if (isset($_SESSION['temp_customer_name'])) {
            return $_SESSION['temp_customer_name'];
        }

        // Try to get from database if customer exists
        try {
            $pdo = new PDO("sqlite:" . __DIR__ . "/../../data/reservations.db");
            $stmt = $pdo->prepare("SELECT name FROM customers WHERE email = ?");
            $stmt->execute([$email]);
            $customer = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($customer) {
                return $customer['name'];
            }
        } catch (Exception $e) {
            // If database query fails, continue with fallback
        }

        // Fallback: extract name from email (before @)
        $emailParts = explode('@', $email);
        $username = $emailParts[0];

        // Clean up username to make it more name-like
        $name = str_replace(['.', '_', '-'], ' ', $username);
        $name = ucwords($name);

        return $name ?: 'Customer';
    }

    // Text content methods for fallback
    private function getVerificationTextContent(array $data): string
    {
        return "Your verification code is: {$data['verification_code']}\n\n" .
            "This code will expire in 15 minutes.\n" .
            "If you didn't request this code, please ignore this email.\n\n" .
            "Best regards,\n{$data['site_name']}";
    }

    private function getConfirmationTextContent(array $data): string
    {
        return "Dear {$data['customer_name']},\n\n" .
            "Your reservation has been confirmed!\n\n" .
            "RESERVATION DETAILS:\n" .
            "==================\n" .
            "ID: {$data['reservation_id']}\n" .
            "Service: {$data['service_name']}\n" .
            "Date: {$data['date']}\n" .
            "Time: {$data['time']}\n" .
            "Duration: {$data['service_duration']} minutes\n" .
            "Price: €{$data['service_price']}\n\n" .
            "Thank you for choosing {$data['site_name']}!\n\n" .
            "Best regards,\n{$data['site_name']}";
    }

    private function getCancellationTextContent(array $data): string
    {
        return "Dear {$data['customer_name']},\n\n" .
            "Your reservation has been cancelled as requested.\n\n" .
            "CANCELLED RESERVATION:\n" .
            "====================\n" .
            "ID: {$data['reservation_id']}\n" .
            "Service: {$data['service_name']}\n" .
            "Date: {$data['date']}\n" .
            "Time: {$data['time']}\n\n" .
            "We hope to see you again soon!\n\n" .
            "Best regards,\n{$data['site_name']}";
    }

    private function getReminderTextContent(array $data): string
    {
        return "Hello {$data['customer_name']},\n\n" .
            "This is a friendly reminder about your upcoming appointment.\n\n" .
            "APPOINTMENT DETAILS:\n" .
            "==================\n" .
            "Service: {$data['service_name']}\n" .
            "Date: {$data['date']}\n" .
            "Time: {$data['time']}\n" .
            "Duration: {$data['service_duration']} minutes\n" .
            "Reservation ID: {$data['reservation_id']}\n\n" .
            "We look forward to seeing you soon!\n\n" .
            "Best regards,\n{$data['site_name']}";
    }

    private function getWelcomeTextContent(array $data): string
    {
        return "Hello {$data['customer_name']},\n\n" .
            "Welcome to {$data['site_name']}!\n\n" .
            "Your personal access code: {$data['access_code']}\n\n" .
            "We look forward to serving you!\n\n" .
            "Best regards,\n{$data['site_name']}";
    }

    /**
     * Send admin notification for new reservation
     */
    public function sendAdminNewReservationNotification(array $reservationData, array $customerData): bool
    {
        $settings = require __DIR__ . '/../settings.php';
        $adminEmail = $settings['admin_email'] ?? '';

        if (empty($adminEmail)) {
            log_activity("Admin notification failed: No admin email configured");
            return false;
        }

        $subject = '[ADMIN] New Reservation - ' . $reservationData['service'];

        $templateData = [
            'site_name' => $this->settings['site_name'],
            'reservation_id' => $reservationData['id'],
            'customer_name' => $customerData['name'],
            'customer_email' => $customerData['email'],
            'customer_mobile' => $customerData['mobile'],
            'service_name' => $reservationData['service'],
            'date' => format_date($reservationData['date']),
            'time' => format_time_range($reservationData['time'], (int)$reservationData['duration']),
            'duration' => $reservationData['duration'],
            'price' => number_format($reservationData['price'], 2),
            'booked_at' => date('Y-m-d H:i:s'),
            'year' => date('Y')
        ];

        // Admin emails use primary language (Greek)
        $htmlContent = $this->loadTemplateWithLanguage('admin_new_reservation', $templateData, 'el');
        $textContent = $this->getAdminNewReservationTextContent($templateData);

        return $this->sendEmail($adminEmail, $subject, $htmlContent, $textContent);
    }

    /**
     * Send admin notification for reservation cancellation
     */
    public function sendAdminCancellationNotification(array $reservationData, array $customerData): bool
    {
        $settings = require __DIR__ . '/../settings.php';
        $adminEmail = $settings['admin_email'] ?? '';

        if (empty($adminEmail)) {
            log_activity("Admin notification failed: No admin email configured");
            return false;
        }

        $subject = '[ADMIN] Reservation Cancelled - ' . $reservationData['service'];

        $templateData = [
            'site_name' => $this->settings['site_name'],
            'reservation_id' => $reservationData['id'],
            'customer_name' => $customerData['name'],
            'customer_email' => $customerData['email'],
            'customer_mobile' => $customerData['mobile'],
            'service_name' => $reservationData['service'],
            'date' => format_date($reservationData['date']),
            'time' => format_time_range($reservationData['time'], (int)$reservationData['duration']),
            'duration' => $reservationData['duration'],
            'price' => number_format($reservationData['price'], 2),
            'cancelled_at' => date('Y-m-d H:i:s'),
            'year' => date('Y')
        ];

        // Admin emails use primary language (Greek)
        $htmlContent = $this->loadTemplateWithLanguage('admin_cancellation', $templateData, 'el');
        $textContent = $this->getAdminCancellationTextContent($templateData);

        return $this->sendEmail($adminEmail, $subject, $htmlContent, $textContent);
    }

    /**
     * Send admin notification for new customer
     */
    public function sendAdminNewCustomerNotification(array $customerData): bool
    {
        $settings = require __DIR__ . '/../settings.php';
        $adminEmail = $settings['admin_email'] ?? '';

        if (empty($adminEmail)) {
            log_activity("Admin notification failed: No admin email configured");
            return false;
        }

        $subject = '[ADMIN] New Customer Registration - ' . $customerData['name'];

        $templateData = [
            'site_name' => $this->settings['site_name'],
            'customer_id' => $customerData['id'],
            'customer_name' => $customerData['name'],
            'customer_email' => $customerData['email'],
            'customer_mobile' => $customerData['mobile'],
            'access_code' => $customerData['user_hash'],
            'registered_at' => date('Y-m-d H:i:s'),
            'year' => date('Y')
        ];

        // Admin emails use primary language (Greek)
        $htmlContent = $this->loadTemplateWithLanguage('admin_new_customer', $templateData, 'el');
        $textContent = $this->getAdminNewCustomerTextContent($templateData);

        return $this->sendEmail($adminEmail, $subject, $htmlContent, $textContent);
    }

    /**
     * Get text content for admin new reservation notification
     */
    private function getAdminNewReservationTextContent(array $data): string
    {
        return "NEW RESERVATION NOTIFICATION\n\n" .
            "A new reservation has been made on {$data['site_name']}:\n\n" .
            "Reservation ID: {$data['reservation_id']}\n" .
            "Customer: {$data['customer_name']}\n" .
            "Email: {$data['customer_email']}\n" .
            "Mobile: {$data['customer_mobile']}\n" .
            "Service: {$data['service_name']}\n" .
            "Date: {$data['date']}\n" .
            "Time: {$data['time']}\n" .
            "Duration: {$data['duration']} minutes\n" .
            "Price: €{$data['price']}\n" .
            "Booked at: {$data['booked_at']}\n\n" .
            "Please prepare for this appointment.\n\n" .
            "This is an automated notification from {$data['site_name']}.";
    }

    /**
     * Get text content for admin cancellation notification
     */
    private function getAdminCancellationTextContent(array $data): string
    {
        return "RESERVATION CANCELLATION NOTIFICATION\n\n" .
            "A reservation has been cancelled on {$data['site_name']}:\n\n" .
            "Reservation ID: {$data['reservation_id']}\n" .
            "Customer: {$data['customer_name']}\n" .
            "Email: {$data['customer_email']}\n" .
            "Mobile: {$data['customer_mobile']}\n" .
            "Service: {$data['service_name']}\n" .
            "Date: {$data['date']}\n" .
            "Time: {$data['time']}\n" .
            "Duration: {$data['duration']} minutes\n" .
            "Price: €{$data['price']}\n" .
            "Cancelled at: {$data['cancelled_at']}\n\n" .
            "This time slot is now available for other bookings.\n\n" .
            "This is an automated notification from {$data['site_name']}.";
    }

    /**
     * Get text content for admin new customer notification
     */
    private function getAdminNewCustomerTextContent(array $data): string
    {
        return "NEW CUSTOMER REGISTRATION NOTIFICATION\n\n" .
            "A new customer has registered on {$data['site_name']}:\n\n" .
            "Customer ID: {$data['customer_id']}\n" .
            "Name: {$data['customer_name']}\n" .
            "Email: {$data['customer_email']}\n" .
            "Mobile: {$data['customer_mobile']}\n" .
            "Access Hash: {$data['user_hash']}\n" .
            "Registered at: {$data['registered_at']}\n\n" .
            "Welcome email has been sent to the customer.\n\n" .
            "This is an automated notification from {$data['site_name']}.";
    }

    /**
     * Send admin notification for customer deletion
     */
    public function sendAdminCustomerDeletionNotification(array $customerData, array $cancelledReservations = [], bool $forceDelete = false): bool
    {
        $settings = require __DIR__ . '/../settings.php';
        $adminEmail = $settings['admin_email'] ?? '';

        if (empty($adminEmail)) {
            log_activity("Admin notification failed: No admin email configured");
            return false;
        }

        $subject = '[ADMIN] Customer Deleted - ' . $customerData['name'];

        $templateData = [
            'site_name' => $this->settings['site_name'],
            'customer_id' => $customerData['id'],
            'customer_name' => $customerData['name'],
            'customer_email' => $customerData['email'],
            'customer_mobile' => $customerData['mobile'],
            'total_reservations' => $customerData['total_reservations'],
            'last_visit' => $customerData['last_visit'] ?: 'Never',
            'member_since' => format_date($customerData['created_at']),
            'deleted_at' => date('Y-m-d H:i:s'),
            'force_delete' => $forceDelete ? 'Yes' : 'No',
            'cancelled_reservations_count' => count($cancelledReservations),
            'cancelled_reservations' => $cancelledReservations,
            'year' => date('Y')
        ];

        // Admin emails use primary language (Greek)
        $htmlContent = $this->loadTemplateWithLanguage('admin_customer_deletion', $templateData, 'el');
        $textContent = $this->getAdminCustomerDeletionTextContent($templateData);

        return $this->sendEmail($adminEmail, $subject, $htmlContent, $textContent);
    }

    /**
     * Get text content for admin customer deletion notification
     */
    private function getAdminCustomerDeletionTextContent(array $data): string
    {
        $text = "CUSTOMER DELETION NOTIFICATION\n\n" .
            "A customer has been deleted from {$data['site_name']}:\n\n" .
            "Customer ID: {$data['customer_id']}\n" .
            "Name: {$data['customer_name']}\n" .
            "Email: {$data['customer_email']}\n" .
            "Mobile: {$data['customer_mobile']}\n" .
            "Total Reservations: {$data['total_reservations']}\n" .
            "Last Visit: {$data['last_visit']}\n" .
            "Member Since: {$data['member_since']}\n" .
            "Force Delete: {$data['force_delete']}\n" .
            "Deleted at: {$data['deleted_at']}\n\n";

        if ($data['cancelled_reservations_count'] > 0) {
            $text .= "CANCELLED RESERVATIONS: {$data['cancelled_reservations_count']}\n";
            foreach ($data['cancelled_reservations'] as $reservation) {
                $text .= "- {$reservation['service']} on " . format_date($reservation['date']) . " at " . format_time_range($reservation['time'], (int)$reservation['duration']) . "\n";
            }
            $text .= "\n";
        }

        $text .= "This is an automated notification from {$data['site_name']}.";

        return $text;
    }
}
