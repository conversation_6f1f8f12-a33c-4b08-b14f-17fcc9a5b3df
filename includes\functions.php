<?php

/**
 * ========================================
 * CORE FUNCTIONS LIBRARY
 * ========================================
 *
 * This file contains essential utility functions for the booking system:
 *
 * SECTIONS:
 * 1. Data Management Functions (services, settings)
 * 2. Validation Functions (email, mobile, date)
 * 3. Utility Functions (sanitization, ID generation)
 * 4. Email Functions (wrappers for EmailSender class)
 * 5. Time and Date Functions (formatting, availability)
 * 6. Security Functions (verification, session management)
 *
 * <AUTHOR> System
 * @version 1.0
 */

/* ========================================
   DEPENDENCIES & INITIALIZATION
   ======================================== */

// Load application settings
$settings = require __DIR__ . '/settings.php';

// Include required dependencies
require_once __DIR__ . '/email_templates/email_sender.php';
require_once __DIR__ . '/TenantContext.php';
require_once __DIR__ . '/Database.php';

// Conditionally load CustomerHandler if needed
if (!class_exists('CustomerHandler') && file_exists(__DIR__ . '/customer_handler.php')) {
    require_once __DIR__ . '/customer_handler.php';
}

// ============================================================================
// 1. DATA MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Get all services from database (tenant-aware)
 */
function getServices(): array
{
    $services = [];
    $db = Database::getInstance();
    $conn = $db->getConnection();

    // Get current tenant ID
    require_once __DIR__ . '/TenantContext.php';
    try {
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT * FROM services WHERE tenant_id = :tenant_id ORDER BY name");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
    } catch (Exception $e) {
        // Fallback for non-tenant context
        $result = $conn->query("SELECT * FROM services ORDER BY name");
    }

    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $services[$row['id']] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'duration' => (int)$row['duration'],
            'price' => (float)$row['price'],
            'description' => $row['description'] ?? '',
            'allow_employee_selection' => (bool)($row['allow_employee_selection'] ?? 0)
        ];
    }

    return $services;
}

/**
 * Get application settings (tenant-aware)
 */
function getSettings(): array
{
    try {
        require_once __DIR__ . '/TenantSettingsManager.php';
        require_once __DIR__ . '/TenantContext.php';

        // Get all tenant settings
        $settings = TenantSettingsManager::getAllSettings();

        // If no settings exist, initialize defaults
        if (empty($settings)) {
            try {
                $tenantId = TenantContext::getTenant();
                if ($tenantId) {
                    TenantSettingsManager::initializeDefaultSettings($tenantId);
                    $settings = TenantSettingsManager::getAllSettings();
                }
            } catch (Exception $e) {
                error_log('getSettings - Tenant context error: ' . $e->getMessage());
                // Continue with empty settings, will use defaults below
            }
        }

        // Fallback to default settings if still empty
        if (empty($settings)) {
            $settings = [
                'site_name' => 'Booking System',
                'email_from' => '<EMAIL>',
                'admin_email' => '<EMAIL>',
                'business_hours' => [
                    'Monday' => [['start' => '09:00', 'end' => '17:00']],
                    'Tuesday' => [['start' => '09:00', 'end' => '17:00']],
                    'Wednesday' => [['start' => '09:00', 'end' => '17:00']],
                    'Thursday' => [['start' => '09:00', 'end' => '17:00']],
                    'Friday' => [['start' => '09:00', 'end' => '17:00']],
                    'Saturday' => [],
                    'Sunday' => []
                ],
                'special_days' => []
            ];
        }

        return $settings;
    } catch (Exception $e) {
        error_log('Error loading tenant settings: ' . $e->getMessage());

        // Fallback to file-based settings for backward compatibility
        $settingsFile = __DIR__ . '/settings.php';
        if (file_exists($settingsFile)) {
            $settings = require $settingsFile;
            return is_array($settings) ? $settings : [];
        }

        return [];
    }
}

// ============================================================================
// 2. VALIDATION FUNCTIONS
// ============================================================================

/**
 * Sanitize user input to prevent XSS attacks
 * @param string $input Raw user input
 * @return string Sanitized input safe for output
 */
function sanitize_input(string $input): string
{
    return htmlspecialchars(stripslashes(trim($input)), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate Greek mobile phone number format
 * @param string $mobile Mobile number to validate
 * @return bool True if valid Greek mobile number
 */
function validate_mobile(string $mobile): bool
{
    $mobile = preg_replace('/[^0-9]/', '', $mobile);
    return preg_match('/^(69|21|22|23|24|25|26|27|28)\d{8}$/', $mobile) === 1;
}

/**
 * Validate email address format
 * @param string $email Email address to validate
 * @return bool True if valid email format
 */
function validate_email(string $email): bool
{
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// ============================================================================
// 3. TIME AND DATE FUNCTIONS
// ============================================================================

/**
 * Format date for display
 */
function format_date(string $date): string
{
    try {
        $dateTime = new DateTime($date);

        // Check current language
        $currentLang = $_GET['lang'] ?? 'primary';

        if ($currentLang === 'primary') {
            // Greek formatting
            $formatter = new IntlDateFormatter(
                'el_GR',
                IntlDateFormatter::FULL,
                IntlDateFormatter::NONE,
                null,
                null,
                'EEEE, d MMMM yyyy'
            );
            return $formatter->format($dateTime);
        } else {
            // English formatting
            return $dateTime->format('l, F j, Y');
        }
    } catch (Exception) {
        return $date;
    }
}

/**
 * Format time for display
 */
function format_time(string $time): string
{
    try {
        $dateTime = DateTime::createFromFormat('H:i', $time);
        return $dateTime ? $dateTime->format('H:i') : $time;
    } catch (Exception) {
        return $time;
    }
}

/**
 * Format time range for display (start time - end time)
 */
function format_time_range(string $startTime, int $duration): string
{
    try {
        $startDateTime = DateTime::createFromFormat('H:i', $startTime);
        if (!$startDateTime) {
            return $startTime;
        }

        $endDateTime = clone $startDateTime;
        $endDateTime->add(new DateInterval('PT' . $duration . 'M'));

        return $startDateTime->format('H:i') . ' - ' . $endDateTime->format('H:i');
    } catch (Exception) {
        return $startTime;
    }
}

// Include the new ID generator
require_once __DIR__ . '/id_generator.php';

/**
 * Generate unique reservation ID
 */
function generate_reservation_id(): string
{
    return IdGenerator::generateReservationId();
}

/**
 * Generate unique customer ID
 */
function generate_customer_id(): string
{
    return IdGenerator::generateCustomerId();
}

/**
 * Generate unique employee ID
 */
function generate_employee_id(): string
{
    return IdGenerator::generateEmployeeId();
}

/**
 * Generate unique service ID
 */
function generate_service_id(): string
{
    return IdGenerator::generateServiceId();
}

/**
 * Log activity to database (tenant-aware)
 */
function log_activity(string $message): void
{
    try {
        // Get current tenant
        $tenantId = TenantContext::getTenant();
        if (!$tenantId) {
            // Fallback to file-based logging if no tenant context
            $logFile = DATA_DIR . 'activity.log';
            $timestamp = date('Y-m-d H:i:s');
            $logEntry = "[$timestamp] $message" . PHP_EOL;
            @file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
            return;
        }

        $db = Database::getInstance();
        $conn = $db->getConnection();

        $stmt = $conn->prepare("INSERT INTO activity_logs (tenant_id, message, created_at) VALUES (:tenant_id, :message, :created_at)");
        $stmt->bindValue(':tenant_id', $tenantId);
        $stmt->bindValue(':message', $message);
        $stmt->bindValue(':created_at', date('Y-m-d H:i:s'));
        $stmt->execute();
    } catch (Exception $e) {
        // Fallback to file-based logging on error
        $logFile = DATA_DIR . 'activity.log';
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[$timestamp] $message" . PHP_EOL;
        @file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}

/**
 * Generate verification code
 */
function generate_verification_code(): string
{
    return strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 6));
}

/**
 * Send verification email using EmailSender class
 */
function send_verification_email(string $email, string $code): bool
{
    $emailSender = new EmailSender();
    return $emailSender->sendVerificationEmail($email, $code);
}

/**
 * Send confirmation email using EmailSender class
 */
function send_confirmation_email(string $email, array $reservationData, array $customerData): bool
{
    $emailSender = new EmailSender();
    return $emailSender->sendConfirmationEmail($email, $reservationData, $customerData);
}

/**
 * Send cancellation email using EmailSender class
 *
 * @param string $email Customer email
 * @param array $reservationData Reservation details
 * @param array|string $customerDataOrReason Customer data array or cancellation reason string
 * @return bool Success status
 */
function send_cancellation_email(string $email, array $reservationData, $customerDataOrReason): bool
{
    $emailSender = new EmailSender();

    // Handle both old signature (reason string) and new signature (customer data array)
    if (is_array($customerDataOrReason)) {
        return $emailSender->sendCancellationEmail($email, $reservationData, $customerDataOrReason);
    } else {
        // Legacy support - try to get customer data from email
        $customerHandler = new CustomerHandler();
        $customer = $customerHandler->getCustomerByEmail($email);
        if ($customer) {
            return $emailSender->sendCancellationEmail($email, $reservationData, $customer);
        }
        return false;
    }
}

/**
 * Send reminder email using EmailSender class
 */
function send_reminder_email(string $email, array $reservationData, array $customerData): bool
{
    $emailSender = new EmailSender();
    return $emailSender->sendReminderEmail($email, $reservationData, $customerData);
}

/**
 * Send welcome email using EmailSender class
 */
function send_welcome_email(string $email, array $customerData): bool
{
    $emailSender = new EmailSender();
    return $emailSender->sendWelcomeEmail($email, $customerData);
}

/**
 * Generate user hash for security
 */
function get_user_hash(string $email): string
{
    return substr(hash('sha256', strtolower(trim($email)) . 'wellness_secret_2024'), 0, 16);
}

/**
 * Send admin notification for new reservation using EmailSender class
 */
function notify_admin_new_reservation(array $reservationData, array $customerData): bool
{
    $emailSender = new EmailSender();
    return $emailSender->sendAdminNewReservationNotification($reservationData, $customerData);
}

/**
 * Send admin notification for cancellation using EmailSender class
 */
function notify_admin_cancellation(array $reservationData, array $customerData): bool
{
    $emailSender = new EmailSender();
    return $emailSender->sendAdminCancellationNotification($reservationData, $customerData);
}

/**
 * Send admin notification for new customer using EmailSender class
 */
function notify_admin_new_customer(array $customerData): bool
{
    $emailSender = new EmailSender();
    return $emailSender->sendAdminNewCustomerNotification($customerData);
}

/**
 * Send admin notification for customer deletion using EmailSender class
 */
function notify_admin_customer_deletion(array $customerData, array $cancelledReservations = [], bool $forceDelete = false): bool
{
    $emailSender = new EmailSender();
    return $emailSender->sendAdminCustomerDeletionNotification($customerData, $cancelledReservations, $forceDelete);
}



/**
 * Check if verification code or user hash is valid
 */
function is_valid_verification_code(string $email, string $code): bool
{
    // First check if it's a user hash (longer than 6 characters)
    if (strlen($code) > 6) {
        $customerHandler = new CustomerHandler();
        $customer = $customerHandler->getCustomerByEmail($email);

        if ($customer && isset($customer['user_hash']) && $customer['user_hash'] === $code) {
            return true;
        }
    }

    // If not a valid user hash, check 6-digit verification code
    $db = Database::getInstance();
    $conn = $db->getConnection();

    $stmt = $conn->prepare("
        SELECT * FROM verification_codes 
        WHERE email = :email AND code = :code
        LIMIT 1
    ");

    $stmt->bindValue(':email', $email);
    $stmt->bindValue(':code', $code);

    $result = $stmt->execute();
    $row = $result->fetchArray(SQLITE3_ASSOC);

    if (!$row) {
        return false;
    }

    // Check if code is expired
    $timestamp = (int)$row['timestamp'];
    $timeElapsed = time() - $timestamp;

    // Allow immediate use (don't reject codes used within first few seconds)
    // Only reject if truly expired (older than expiry time)
    if ($timeElapsed > VERIFICATION_CODE_EXPIRY) {
        // Delete expired code
        $stmt = $conn->prepare("DELETE FROM verification_codes WHERE email = :email AND code = :code");
        $stmt->bindValue(':email', $email);
        $stmt->bindValue(':code', $code);
        $stmt->execute();
        return false;
    }

    // Code is valid and not expired

    return true;
}

/**
 * Check session timeout
 */
function check_session_timeout(): void
{
    if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
        // Session has expired
        session_unset();
        session_destroy();

        // Redirect to home page with expired message
        header('Location: index.php?expired=1');
        exit();
    }

    // Update last activity time
    $_SESSION['last_activity'] = time();
}

/**
 * Validate date format and ensure it's not in the past
 */
function validate_date(string $date): bool
{
    $dateTime = DateTime::createFromFormat('Y-m-d', $date);
    if (!$dateTime || $dateTime->format('Y-m-d') !== $date) {
        return false;
    }

    $today = new DateTime();
    $today->setTime(0, 0, 0);

    return $dateTime >= $today;
}

/**
 * Validate time format
 */
function validate_time(string $time): bool
{
    $timeObj = DateTime::createFromFormat('H:i', $time);
    return $timeObj && $timeObj->format('H:i') === $time;
}

/**
 * Store verification code in database
 */
function store_verification_code(string $email, string $code): bool
{
    $db = Database::getInstance();
    $conn = $db->getConnection();

    // Delete any existing codes for this email
    $stmt = $conn->prepare("DELETE FROM verification_codes WHERE email = :email");
    $stmt->bindValue(':email', $email);
    $stmt->execute();

    // Insert new code
    $stmt = $conn->prepare("
        INSERT INTO verification_codes (email, code, timestamp)
        VALUES (:email, :code, :timestamp)
    ");

    $stmt->bindValue(':email', $email);
    $stmt->bindValue(':code', $code);
    $stmt->bindValue(':timestamp', time());

    return $stmt->execute() !== false;
}



/**
 * Get available time slots for a specific date and service
 */
function getAvailableTimeSlots(string $date, string $serviceId): array
{
    $db = Database::getInstance();
    $conn = $db->getConnection();
    $settings = require __DIR__ . '/settings.php';

    // Get service duration
    $services = getServices();
    if (!isset($services[$serviceId])) {
        return [];
    }

    $serviceDuration = $services[$serviceId]['duration'];

    // Get business hours for the day
    $dayOfWeek = date('l', strtotime($date));
    $businessHours = $settings['business_hours'][$dayOfWeek] ?? [];

    if (empty($businessHours) || isset($businessHours[0]['start']) && $businessHours[0]['start'] === 'closed') {
        return []; // Closed on this day
    }

    // Get existing reservations for this date
    $stmt = $conn->prepare("
        SELECT time, duration 
        FROM reservations 
        WHERE date = :date AND status != 'cancelled'
        ORDER BY time
    ");

    $stmt->bindValue(':date', $date);
    $result = $stmt->execute();

    $bookedSlots = [];
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $startTime = strtotime($row['time']);
        $endTime = $startTime + ($row['duration'] * 60);

        $bookedSlots[] = [
            'start' => $startTime,
            'end' => $endTime
        ];
    }

    // Generate available time slots
    $availableSlots = [];

    // Handle multiple business hour periods for the day
    foreach ($businessHours as $period) {
        if (!isset($period['start']) || !isset($period['end'])) {
            continue;
        }

        $startTime = strtotime($period['start']);
        $endTime = strtotime($period['end']);

        // Generate slots at 15-minute intervals
        for ($time = $startTime; $time <= $endTime - ($serviceDuration * 60); $time += 900) {
            $slotStart = $time;
            $slotEnd = $time + ($serviceDuration * 60);

            // Check if slot overlaps with any booked slots
            $isAvailable = true;
            foreach ($bookedSlots as $bookedSlot) {
                if ($slotStart < $bookedSlot['end'] && $slotEnd > $bookedSlot['start']) {
                    $isAvailable = false;
                    break;
                }
            }

            if ($isAvailable) {
                $availableSlots[] = date('H:i', $time);
            }
        }
    }

    return $availableSlots;
}

// Legacy send_cancellation_email function removed - now using EmailSender class
