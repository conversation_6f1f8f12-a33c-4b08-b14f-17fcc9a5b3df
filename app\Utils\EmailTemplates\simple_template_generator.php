<?php

/**
 * Simplified Email Template Generator
 * Generates simple, clean email templates with just 3 fields per email type
 */
class SimpleEmailTemplateGenerator
{
    private $templatesDir;

    public function __construct()
    {
        $this->templatesDir = __DIR__ . '/templates';

        // Create templates directory if it doesn't exist
        if (!is_dir($this->templatesDir)) {
            mkdir($this->templatesDir, 0755, true);
        }
    }

    /**
     * Generate all simplified email templates
     */
    public function generateAllTemplates()
    {
        echo "🚀 Generating simplified email templates...\n\n";

        $languages = ['en', 'el'];
        $emailTypes = ['confirmation', 'cancellation', 'reminder', 'welcome', 'verification'];

        foreach ($languages as $lang) {
            echo "🌐 Language: " . strtoupper($lang) . "\n";

            foreach ($emailTypes as $type) {
                $this->generateSimpleTemplate($type, $lang);
            }

            echo "\n";
        }

        echo "✅ All simplified templates generated successfully!\n";
    }

    /**
     * Generate a simple template for a specific email type and language
     */
    private function generateSimpleTemplate($type, $language)
    {
        // Get email texts from unified manager
        require_once __DIR__ . '/../unified_text_manager.php';
        $emailTexts = UnifiedTextManager::getEmailTexts();

        $subject = $emailTexts[$language]["{$type}_subject"] ?? "Email Subject";
        $mainMessage = $emailTexts[$language]["{$type}_main_message"] ?? "Email content goes here.";
        $footer = $emailTexts[$language]["{$type}_footer"] ?? "Footer message.";

        // Convert newlines to HTML
        $mainMessage = nl2br($mainMessage);
        $footer = nl2br($footer);

        // Generate simple HTML template
        $html = $this->generateSimpleHTML($subject, $mainMessage, $footer);

        // Save template
        $filename = $language === 'en' ? "{$type}.html" : "{$type}_{$language}.html";
        $filepath = $this->templatesDir . '/' . $filename;

        file_put_contents($filepath, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate simple HTML structure
     */
    private function generateSimpleHTML($subject, $mainMessage, $footer)
    {
        return '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($subject) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .email-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 24px;
        }
        .content {
            font-size: 16px;
            line-height: 1.8;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 14px;
            color: #666;
            text-align: center;
        }
        .highlight {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1>{{site_name}}</h1>
        </div>
        
        <div class="content">
            ' . $mainMessage . '
        </div>
        
        <div class="footer">
            ' . $footer . '<br><br>
            &copy; {{year}} {{site_name}}. All rights reserved.
        </div>
    </div>
</body>
</html>';
    }
}
