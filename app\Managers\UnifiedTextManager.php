<?php

/**
 * Unified Text Manager
 * Handles both customer page texts and email template texts
 * with consistent storage and retrieval methods
 */
class UnifiedTextManager
{
    private static $customerTextsFile;
    private static $emailTextsFile;

    public static function init()
    {
        // Use the same file paths as TextManager for compatibility
        self::$customerTextsFile = DATA_DIR . '/custom_customer_texts.php';
        self::$emailTextsFile = DATA_DIR . '/custom_email_texts.php';

        // Ensure DATA_DIR exists
        if (!is_dir(DATA_DIR)) {
            mkdir(DATA_DIR, 0755, true);
        }
    }

    /**
     * Get all customer texts (original + custom, tenant-aware)
     */
    public static function getCustomerTexts()
    {
        // Load original texts
        $originalTexts = require __DIR__ . '/customer_texts.php';

        try {
            require_once __DIR__ . '/TenantSettingsManager.php';

            // Load custom texts from database (both languages)
            $customPrimaryTexts = TenantSettingsManager::getAllTexts('customer', 'primary');
            $customSecondaryTexts = TenantSettingsManager::getAllTexts('customer', 'secondary');

            // Merge custom texts with original texts
            $mergedTexts = $originalTexts;

            // Update with custom texts, preserving the primary/secondary structure
            foreach ($customPrimaryTexts as $key => $primaryValue) {
                $secondaryValue = $customSecondaryTexts[$key] ?? '';
                $mergedTexts[$key] = [
                    'primary' => $primaryValue,
                    'secondary' => $secondaryValue
                ];
            }

            // Also handle any secondary-only custom texts
            foreach ($customSecondaryTexts as $key => $secondaryValue) {
                if (!isset($customPrimaryTexts[$key])) {
                    $primaryValue = $originalTexts[$key]['primary'] ?? '';
                    $mergedTexts[$key] = [
                        'primary' => $primaryValue,
                        'secondary' => $secondaryValue
                    ];
                }
            }

            return $mergedTexts;
        } catch (Exception $e) {
            // Fallback to file-based loading
            self::init();
            $customTexts = [];
            if (file_exists(self::$customerTextsFile)) {
                $customTexts = require self::$customerTextsFile;
            }
            return array_merge($originalTexts, $customTexts);
        }
    }

    /**
     * Get all email texts (original + custom, tenant-aware)
     */
    public static function getEmailTexts()
    {
        // Load original email texts
        $originalTexts = self::getOriginalEmailTexts();

        try {
            require_once __DIR__ . '/TenantSettingsManager.php';

            // Load custom texts from database
            $customTextsEn = TenantSettingsManager::getAllTexts('email', 'en');
            $customTextsEl = TenantSettingsManager::getAllTexts('email', 'el');

            return [
                'en' => array_merge($originalTexts['en'], $customTextsEn),
                'el' => array_merge($originalTexts['el'], $customTextsEl)
            ];
        } catch (Exception $e) {
            // Fallback to file-based loading
            self::init();
            $customTexts = ['en' => [], 'el' => []];
            if (file_exists(self::$emailTextsFile)) {
                $customTexts = require self::$emailTextsFile;
            }

            return [
                'en' => array_merge($originalTexts['en'], $customTexts['en'] ?? []),
                'el' => array_merge($originalTexts['el'], $customTexts['el'] ?? [])
            ];
        }
    }

    /**
     * Save customer texts (tenant-aware)
     */
    public static function saveCustomerTexts($texts)
    {
        try {
            require_once __DIR__ . '/TenantSettingsManager.php';

            // Separate texts by language for database storage
            $primaryTexts = [];
            $secondaryTexts = [];

            foreach ($texts as $key => $value) {
                if (is_array($value)) {
                    // New format with primary/secondary
                    $primaryTexts[$key] = $value['primary'] ?? '';
                    $secondaryTexts[$key] = $value['secondary'] ?? '';
                } else {
                    // Old format - treat as primary language
                    $primaryTexts[$key] = $value;
                    $secondaryTexts[$key] = '';
                }
            }

            // Save both languages to database
            $success = true;
            if (!empty($primaryTexts)) {
                $success &= TenantSettingsManager::setMultipleTexts('customer', 'primary', $primaryTexts);
            }
            if (!empty($secondaryTexts)) {
                $success &= TenantSettingsManager::setMultipleTexts('customer', 'secondary', $secondaryTexts);
            }

            return $success;
        } catch (Exception $e) {
            // Fallback to file-based saving
            try {
                self::init();

                $content = "<?php\n\n";
                $content .= "// Custom customer texts\n";
                $content .= "// Generated on: " . date('Y-m-d H:i:s') . "\n\n";
                $content .= "return " . var_export($texts, true) . ";\n";

                $result = file_put_contents(self::$customerTextsFile, $content, LOCK_EX);
                return $result !== false;
            } catch (Exception $e2) {
                error_log("Customer texts save error: " . $e2->getMessage());
                return false;
            }
        }
    }

    /**
     * Save email texts and regenerate templates (tenant-aware)
     */
    public static function saveEmailTexts($texts)
    {
        try {
            require_once __DIR__ . '/TenantSettingsManager.php';

            // Save to database for each language
            $success = true;
            foreach ($texts as $language => $languageTexts) {
                $success &= TenantSettingsManager::setMultipleTexts('email', $language, $languageTexts);
            }

            if ($success) {
                // Auto-regenerate email templates with new texts
                self::regenerateEmailTemplates();
                return true;
            }

            return false;
        } catch (Exception $e) {
            // Fallback to file-based saving
            try {
                self::init();

                $content = "<?php\n\n";
                $content .= "// Custom email texts\n";
                $content .= "// Generated on: " . date('Y-m-d H:i:s') . "\n\n";
                $content .= "return " . var_export($texts, true) . ";\n";

                $result = file_put_contents(self::$emailTextsFile, $content, LOCK_EX);

                if ($result !== false) {
                    // Auto-regenerate email templates with new texts
                    self::regenerateEmailTemplates();
                    return true;
                }

                return false;
            } catch (Exception $e2) {
                error_log("Email texts save error: " . $e2->getMessage());
                return false;
            }
        }
    }

    /**
     * Regenerate email templates from HTML content
     */
    private static function regenerateEmailTemplates()
    {
        try {
            // Include the HTML email template generator
            require_once __DIR__ . '/email_templates/html_template_generator.php';

            // Create generator instance
            $generator = new HtmlTemplateGenerator();

            // Capture output to prevent it from being displayed
            ob_start();

            // Generate all HTML templates
            $generator->generateAllTemplates();

            $output = ob_get_clean();

            // Log the regeneration for debugging
            error_log("HTML email templates regenerated: " . $output);

            return true;
        } catch (Exception $e) {
            error_log("Error regenerating HTML email templates: " . $e->getMessage());
            return false;
        }
    }



    /**
     * Manually regenerate email templates (for admin use)
     */
    public static function regenerateEmailTemplatesManually()
    {
        return self::regenerateEmailTemplates();
    }

    /**
     * Get original default customer texts
     */
    public static function getOriginalCustomerTexts()
    {
        return require __DIR__ . '/customer_texts.php';
    }

    /**
     * Get original default email texts
     */
    public static function getOriginalEmailTexts()
    {
        return [
            'en' => [
                // Common
                'site_name_placeholder' => '{{site_name}}',
                'customer_name_placeholder' => '{{customer_name}}',
                'year_placeholder' => '{{year}}',
                'footer_text' => '&copy; {{year}} {{site_name}}. All rights reserved.',

                // Confirmation Email (Single HTML Content)
                'confirmation_html' => '<div class="email-container">
        <div class="header">
            <div class="header-icon">✅</div>
            <h1>{{site_name}}</h1>
            <p>Reservation Confirmed</p>
        </div>

        <div class="content">
            <div class="greeting">Hello {{customer_name}},</div>

            <div class="paragraph">Your reservation has been confirmed! We look forward to seeing you.</div>

            <div class="reservation-id">ID: {{reservation_id}}</div>

            <div class="detail-section">
                <h3><span class="detail-section-icon">📋</span>Reservation Details</h3>
                <div class="detail-row">
                    <div class="detail-label">Service</div>
                    <div class="detail-value">{{service_name}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Date</div>
                    <div class="detail-value">{{date}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Time</div>
                    <div class="detail-value">{{time}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Price</div>
                    <div class="detail-value">€{{service_price}}</div>
                </div>
            </div>

            <div class="paragraph">Thank you for choosing {{site_name}}!</div>
        </div>

        <div class="footer">
            <div class="footer-text">&copy; {{year}} {{site_name}}. All rights reserved.</div>
        </div>
    </div>',

                // Cancellation Email (Single HTML Content)
                'cancellation_html' => '<div class="email-container">
        <div class="header">
            <div class="header-icon">❌</div>
            <h1>{{site_name}}</h1>
            <p>Reservation Cancelled</p>
        </div>

        <div class="content">
            <div class="greeting">Hello {{customer_name}},</div>

            <div class="paragraph">Your reservation has been cancelled as requested.</div>

            <div class="reservation-id">ID: {{reservation_id}}</div>

            <div class="detail-section">
                <h3><span class="detail-section-icon">📋</span>Cancelled Reservation Details</h3>
                <div class="detail-row">
                    <div class="detail-label">Service</div>
                    <div class="detail-value">{{service_name}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Date</div>
                    <div class="detail-value">{{date}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Time</div>
                    <div class="detail-value">{{time}}</div>
                </div>
            </div>

            <div class="paragraph">If you need to make a new reservation, please visit our booking system.</div>
        </div>

        <div class="footer">
            <div class="footer-text">&copy; {{year}} {{site_name}}. All rights reserved.</div>
        </div>
    </div>',

                // Reminder Email (Single HTML Content)
                'reminder_html' => '<div class="email-container">
        <div class="header">
            <div class="header-icon">⏰</div>
            <h1>{{site_name}}</h1>
            <p>Appointment Reminder</p>
        </div>

        <div class="content">
            <div class="greeting">Hello {{customer_name}},</div>

            <div class="paragraph">This is a friendly reminder about your upcoming appointment.</div>

            <div class="reservation-id">ID: {{reservation_id}}</div>

            <div class="detail-section">
                <h3><span class="detail-section-icon">📋</span>Appointment Details</h3>
                <div class="detail-row">
                    <div class="detail-label">Service</div>
                    <div class="detail-value">{{service_name}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Date</div>
                    <div class="detail-value">{{date}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Time</div>
                    <div class="detail-value">{{time}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Duration</div>
                    <div class="detail-value">{{duration}} minutes</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Price</div>
                    <div class="detail-value">€{{price}}</div>
                </div>
            </div>

            <div class="paragraph">Please arrive 5 minutes early. If you need to reschedule, please contact us.</div>
        </div>

        <div class="footer">
            <div class="footer-text">&copy; {{year}} {{site_name}}. All rights reserved.</div>
        </div>
    </div>',

                // Welcome Email (Single HTML Content)
                'welcome_html' => '<div class="email-container">
        <div class="header">
            <div class="header-icon">👋</div>
            <h1>{{site_name}}</h1>
            <p>Welcome!</p>
        </div>

        <div class="content">
            <div class="greeting">Hello {{customer_name}},</div>

            <div class="paragraph">Welcome to {{site_name}}! Your account has been created successfully.</div>

            <div class="detail-section">
                <h3><span class="detail-section-icon">🔑</span> Your Access Information</h3>
                <div class="access-code">{{access_code}}</div>
            </div>

            <div class="paragraph">Please save this access hash - you\'ll need it to make future bookings.</div>
        </div>

        <div class="footer">
            <div class="footer-text">&copy; {{year}} {{site_name}}. All rights reserved.</div>
        </div>
    </div>',

                // Verification Email (Single HTML Content)
                'verification_html' => '<div class="email-container">
        <div class="header">
            <div class="header-icon">🔐</div>
            <h1>{{site_name}}</h1>
            <p>Verify Your Email</p>
        </div>

        <div class="content">
            <div class="greeting">Hello {{customer_name}},</div>

            <div class="paragraph">Please verify your email address to complete your registration.</div>

            <div class="verification-code">{{verification_code}}</div>

            <div class="paragraph">This code will expire in 15 minutes.</div>
        </div>

        <div class="footer">
            <div class="footer-text">&copy; {{year}} {{site_name}}. All rights reserved.</div>
        </div>
    </div>',



                // Admin notifications
                'admin_new_customer_title' => 'New Customer Registration',
                'admin_new_customer_header' => 'New Customer',
                'admin_new_customer_message' => 'A new customer has registered on your booking system.',
                'admin_customer_section' => '👤 Customer Information',
                'admin_customer_details_section' => '👤 Customer Details',
                'admin_new_reservation_title' => 'New Reservation',
                'admin_new_reservation_header' => 'New Booking',
                'admin_new_reservation_message' => 'A new reservation has been made.',
                'admin_reservation_section' => '📅 Reservation Details',
                'admin_cancellation_title' => 'Reservation Cancelled',
                'admin_cancellation_header' => 'Cancellation Notice',
                'admin_cancellation_message' => 'A reservation has been cancelled.',
                'admin_cancelled_section' => '🚫 Cancelled Reservation',
                'admin_customer_deletion_title' => 'Customer Deleted',
                'admin_customer_deletion_header' => 'Customer Account Deleted',
                'admin_deleted_customer_section' => '🗑️ Deleted Customer Information'
            ],
            'el' => [
                // Common
                'site_name_placeholder' => '{{site_name}}',
                'customer_name_placeholder' => '{{customer_name}}',
                'year_placeholder' => '{{year}}',
                'footer_text' => '&copy; {{year}} {{site_name}}. Όλα τα δικαιώματα διατηρούνται.',

                // Confirmation Email (Single HTML Content)
                'confirmation_html' => '<div class="email-container">
        <div class="header">
            <div class="header-icon">✅</div>
            <h1>{{site_name}}</h1>
            <p>Επιβεβαίωση Κράτησης</p>
        </div>

        <div class="content">
            <div class="greeting">Γεια σας {{customer_name}},</div>

            <div class="paragraph">Η κράτησή σας επιβεβαιώθηκε! Ανυπομονούμε να σας δούμε.</div>

            <div class="reservation-id">Κωδικός: {{reservation_id}}</div>

            <div class="detail-section">
                <h3><span class="detail-section-icon">📋</span>Στοιχεία Κράτησης</h3>
                <div class="detail-row">
                    <div class="detail-label">Υπηρεσία</div>
                    <div class="detail-value">{{service_name}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Ημερομηνία</div>
                    <div class="detail-value">{{date}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Ώρα</div>
                    <div class="detail-value">{{time}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Τιμή</div>
                    <div class="detail-value">€{{service_price}}</div>
                </div>
            </div>

            <div class="paragraph">Σας ευχαριστούμε που επιλέξατε το {{site_name}}!</div>
        </div>

        <div class="footer">
            <div class="footer-text">&copy; {{year}} {{site_name}}. Όλα τα δικαιώματα διατηρούνται.</div>
        </div>
    </div>',

                // Cancellation Email (Single HTML Content)
                'cancellation_html' => '<div class="email-container">
        <div class="header">
            <div class="header-icon">❌</div>
            <h1>{{site_name}}</h1>
            <p>Ακύρωση Κράτησης</p>
        </div>

        <div class="content">
            <div class="greeting">Γεια σας {{customer_name}},</div>

            <div class="paragraph">Η κράτησή σας ακυρώθηκε όπως ζητήσατε.</div>

            <div class="reservation-id">Κωδικός: {{reservation_id}}</div>

            <div class="detail-section">
                <h3><span class="detail-section-icon">📋</span>Στοιχεία Ακυρωμένης Κράτησης</h3>
                <div class="detail-row">
                    <div class="detail-label">Υπηρεσία</div>
                    <div class="detail-value">{{service_name}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Ημερομηνία</div>
                    <div class="detail-value">{{date}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Ώρα</div>
                    <div class="detail-value">{{time}}</div>
                </div>
            </div>

            <div class="paragraph">Αν χρειάζεστε νέα κράτηση, παρακαλώ επισκεφθείτε το σύστημα κρατήσεων.</div>
        </div>

        <div class="footer">
            <div class="footer-text">&copy; {{year}} {{site_name}}. Όλα τα δικαιώματα διατηρούνται.</div>
        </div>
    </div>',

                // Reminder Email (Single HTML Content)
                'reminder_html' => '<div class="email-container">
        <div class="header">
            <div class="header-icon">⏰</div>
            <h1>{{site_name}}</h1>
            <p>Υπενθύμιση Ραντεβού</p>
        </div>

        <div class="content">
            <div class="greeting">Γεια σας {{customer_name}},</div>

            <div class="paragraph">Αυτή είναι μια φιλική υπενθύμιση για το επερχόμενο ραντεβού σας.</div>

            <div class="reservation-id">Κωδικός: {{reservation_id}}</div>

            <div class="detail-section">
                <h3><span class="detail-section-icon">📋</span>Στοιχεία Ραντεβού</h3>
                <div class="detail-row">
                    <div class="detail-label">Υπηρεσία</div>
                    <div class="detail-value">{{service_name}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Ημερομηνία</div>
                    <div class="detail-value">{{date}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Ώρα</div>
                    <div class="detail-value">{{time}}</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Διάρκεια</div>
                    <div class="detail-value">{{duration}} λεπτά</div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Τιμή</div>
                    <div class="detail-value">€{{price}}</div>
                </div>
            </div>

            <div class="paragraph">Παρακαλώ φτάστε 5 λεπτά νωρίτερα. Για αλλαγή ραντεβού, επικοινωνήστε μαζί μας.</div>
        </div>

        <div class="footer">
            <div class="footer-text">&copy; {{year}} {{site_name}}. Όλα τα δικαιώματα διατηρούνται.</div>
        </div>
    </div>',

                // Welcome Email (Single HTML Content)
                'welcome_html' => '<div class="email-container">
        <div class="header">
            <div class="header-icon">👋</div>
            <h1>{{site_name}}</h1>
            <p>Καλώς ήρθατε!</p>
        </div>

        <div class="content">
            <div class="greeting">Γεια σας {{customer_name}},</div>

            <div class="paragraph">Καλώς ήρθατε στο {{site_name}}! Ο λογαριασμός σας δημιουργήθηκε επιτυχώς.</div>

            <div class="detail-section">
                <h3><span class="detail-section-icon">🔑</span> Στοιχεία Πρόσβασης</h3>
                <div class="access-code">{{access_code}}</div>
            </div>

            <div class="paragraph">Παρακαλώ αποθηκεύστε αυτόν τον κωδικό - θα τον χρειαστείτε για μελλοντικές κρατήσεις.</div>
        </div>

        <div class="footer">
            <div class="footer-text">&copy; {{year}} {{site_name}}. Όλα τα δικαιώματα διατηρούνται.</div>
        </div>
    </div>',

                // Verification Email (Single HTML Content)
                'verification_html' => '<div class="email-container">
        <div class="header">
            <div class="header-icon">🔐</div>
            <h1>{{site_name}}</h1>
            <p>Επαληθεύστε το Email σας</p>
        </div>

        <div class="content">
            <div class="greeting">Γεια σας {{customer_name}},</div>

            <div class="paragraph">Παρακαλώ επαληθεύστε τη διεύθυνση email σας για να ολοκληρώσετε την εγγραφή.</div>

            <div class="verification-code">{{verification_code}}</div>

            <div class="paragraph">Αυτός ο κωδικός θα λήξει σε 15 λεπτά.</div>
        </div>

        <div class="footer">
            <div class="footer-text">&copy; {{year}} {{site_name}}. Όλα τα δικαιώματα διατηρούνται.</div>
        </div>
    </div>',



                // Admin notifications
                'admin_new_customer_title' => 'Νέος Πελάτης',
                'admin_new_customer_header' => 'Νέος Πελάτης',
                'admin_new_customer_message' => 'Ένας νέος πελάτης εγγράφηκε στο σύστημα κρατήσεων.',
                'admin_customer_section' => '👤 Στοιχεία Πελάτη',
                'admin_customer_details_section' => '👤 Λεπτομέρειες Πελάτη',
                'admin_new_reservation_title' => 'Νέα Κράτηση',
                'admin_new_reservation_header' => 'Νέα Κράτηση',
                'admin_new_reservation_message' => 'Μια νέα κράτηση έγινε.',
                'admin_reservation_section' => '📅 Στοιχεία Κράτησης',
                'admin_cancellation_title' => 'Ακύρωση Κράτησης',
                'admin_cancellation_header' => 'Ειδοποίηση Ακύρωσης',
                'admin_cancellation_message' => 'Μια κράτηση ακυρώθηκε.',
                'admin_cancelled_section' => '🚫 Ακυρωμένη Κράτηση',
                'admin_customer_deletion_title' => 'Διαγραφή Πελάτη',
                'admin_customer_deletion_header' => 'Λογαριασμός Πελάτη Διαγράφηκε',
                'admin_deleted_customer_section' => '🗑️ Στοιχεία Διαγραμμένου Πελάτη'
            ]
        ];
    }

    /**
     * Reset customer texts to defaults
     */
    public static function resetCustomerTexts($keys = null)
    {
        $originalTexts = self::getOriginalCustomerTexts();

        if ($keys === null) {
            // Reset all
            return self::saveCustomerTexts($originalTexts);
        } else {
            // Reset specific keys
            $currentTexts = self::getCustomerTexts();
            foreach ($keys as $key) {
                if (isset($originalTexts[$key])) {
                    $currentTexts[$key] = $originalTexts[$key];
                }
            }
            return self::saveCustomerTexts($currentTexts);
        }
    }

    /**
     * Reset email texts to defaults
     */
    public static function resetEmailTexts($keys = null)
    {
        $originalTexts = self::getOriginalEmailTexts();

        if ($keys === null) {
            // Reset all
            return self::saveEmailTexts($originalTexts);
        } else {
            // Reset specific keys
            $currentTexts = self::getEmailTexts();
            foreach ($keys as $key) {
                if (isset($originalTexts['en'][$key])) {
                    $currentTexts['en'][$key] = $originalTexts['en'][$key];
                }
                if (isset($originalTexts['el'][$key])) {
                    $currentTexts['el'][$key] = $originalTexts['el'][$key];
                }
            }
            return self::saveEmailTexts($currentTexts);
        }
    }
}

// Initialize on load
UnifiedTextManager::init();
