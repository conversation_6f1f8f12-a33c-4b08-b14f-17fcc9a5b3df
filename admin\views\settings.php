<?php

require_once __DIR__ . '/../../includes/tenant_init.php';
require_once __DIR__ . '/../../includes/TenantSettingsManager.php';

$settingsFile = __DIR__ . '/../../includes/settings.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $siteName = trim($_POST['site_name'] ?? '');
    $emailFrom = trim($_POST['email_from'] ?? '');
    $adminEmail = trim($_POST['admin_email'] ?? '');

    // Handle business hours (up to 2 intervals per day) - only if form has business hour fields
    $businessHours = null; // null means don't update
    $hasBusinessHourFields = false;
    $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    // Check if any business hour field is present
    foreach ($days as $day) {
        for ($i = 1; $i <= 2; $i++) {
            if (isset($_POST["bh_{$day}_start{$i}"]) || isset($_POST["bh_{$day}_end{$i}"])) {
                $hasBusinessHourFields = true;
                break 2;
            }
        }
    }

    if ($hasBusinessHourFields) {
        $businessHours = [];
        foreach ($days as $day) {
            $intervals = [];
            for ($i = 1; $i <= 2; $i++) {
                $start = trim($_POST["bh_{$day}_start{$i}"] ?? '');
                $end = trim($_POST["bh_{$day}_end{$i}"] ?? '');
                if ($start && $end) {
                    $intervals[] = ['start' => $start, 'end' => $end];
                }
            }
            $businessHours[$day] = $intervals;
        }
    }

    // Handle special days from textarea - only if field is present
    $specialDays = null; // null means don't update
    if (isset($_POST['special_days_text'])) {
        $specialDays = [];
        $specialDaysText = trim($_POST['special_days_text']);
        if ($specialDaysText) {
            $entries = explode('|', $specialDaysText);
            foreach ($entries as $entry) {
                $entry = trim($entry);
                if (!$entry) continue;
                $parts = preg_split('/\s+/', $entry);
                $date = array_shift($parts);
                $intervals = [];
                foreach ($parts as $interval) {
                    if (preg_match('/^([0-2][0-9]:[0-5][0-9])-([0-2][0-9]:[0-5][0-9])$/', $interval, $m)) {
                        $intervals[] = ['start' => $m[1], 'end' => $m[2]];
                    }
                }
                $specialDays[$date] = $intervals; // empty array = closed
            }
        }
    }

    if ($siteName && $emailFrom && $adminEmail) {
        try {
            // Save settings using tenant-aware manager
            $success = true;
            $success &= TenantSettingsManager::setSetting('site_name', $siteName);
            $success &= TenantSettingsManager::setSetting('email_from', $emailFrom);
            $success &= TenantSettingsManager::setSetting('admin_email', $adminEmail);

            // Only save business hours and special days if they were provided in the form
            if ($businessHours !== null) {
                $success &= TenantSettingsManager::setSetting('business_hours', $businessHours);
            }
            if ($specialDays !== null) {
                $success &= TenantSettingsManager::setSetting('special_days', $specialDays);
            }

            if ($success) {
                header("Location: ?action=settings&saved=1");
                exit;
            } else {
                $error = "Failed to save settings. Please try again.";
            }
        } catch (Exception $e) {
            $error = "Error saving settings: " . $e->getMessage();
            error_log("Settings save error: " . $e->getMessage());
        }
    } else {
        $error = "Please fill in all required fields (Site Name, Email From, Admin Email).";
    }
}

// Load settings using tenant-aware manager
try {
    $settings = TenantSettingsManager::getAllSettings();

    // Fallback to file-based settings if tenant settings are empty
    if (empty($settings) && file_exists($settingsFile)) {
        $settings = require $settingsFile;
    }
} catch (Exception $e) {
    error_log("Settings load error: " . $e->getMessage());
    $settings = [];

    // Try file-based fallback
    if (file_exists($settingsFile)) {
        try {
            $settings = require $settingsFile;
        } catch (Exception $e2) {
            error_log("Settings file load error: " . $e2->getMessage());
            $settings = [];
        }
    }
}

$businessHours = $settings['business_hours'] ?? [];
$specialDays = $settings['special_days'] ?? [];
$days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

// Prepare $specialDaysText for display
$specialDaysText = '';
foreach ($specialDays as $date => $intervals) {
    $line = $date;
    foreach ($intervals as $interval) {
        $line .= ' ' . $interval['start'] . '-' . $interval['end'];
    }
    $specialDaysText .= $line . ' | ';
}
$specialDaysText = trim($specialDaysText, ' |');
?>


<div class="settings-container">
    <div class="settings-header">
        <h2>System Settings</h2>
    </div>

    <?php if (isset($_GET['saved'])): ?>
        <div class="alert alert-success">Settings saved successfully!</div>
    <?php endif; ?>

    <form method="post" action="?action=settings" class="settings-form">
        <!-- Basic Information Section -->
        <div class="form-section basic-info">
            <h3>Basic Information</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="site_name">Site Name</label>
                    <input type="text" id="site_name" name="site_name" value="<?= htmlspecialchars($settings['site_name']) ?>" class="form-control" required>
                    <div class="form-help">The name of your wellness center</div>
                </div>
                <div class="form-group">
                    <label for="email_from">Email From Address</label>
                    <input type="email" id="email_from" name="email_from" value="<?= htmlspecialchars($settings['email_from']) ?>" class="form-control" required>
                    <div class="form-help">Email address used for sending notifications</div>
                </div>
                <div class="form-group">
                    <label for="admin_email">Admin Email</label>
                    <input type="email" id="admin_email" name="admin_email" value="<?= htmlspecialchars($settings['admin_email']) ?>" class="form-control" required>
                    <div class="form-help">Administrator email for important notifications</div>
                </div>
            </div>
        </div>

        <!-- Business Hours Section -->
        <div class="form-section business-hours">
            <h3>Business Hours</h3>
            <p style="color: #6c757d; margin-bottom: 20px;">Set your operating hours for each day. You can define up to 2 time intervals per day (e.g., morning and afternoon sessions).</p>

            <table class="business-hours-table">
                <thead>
                    <tr>
                        <th>Day</th>
                        <th>First Interval</th>
                        <th>Second Interval</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($days as $day): ?>
                        <tr>
                            <td class="day-name"><?= $day ?></td>
                            <td>
                                <div class="time-inputs">
                                    <input type="time" name="bh_<?= $day ?>_start1" value="<?= htmlspecialchars($businessHours[$day][0]['start'] ?? '') ?>" placeholder="09:00">
                                    <span class="time-separator">to</span>
                                    <input type="time" name="bh_<?= $day ?>_end1" value="<?= htmlspecialchars($businessHours[$day][0]['end'] ?? '') ?>" placeholder="17:00">
                                </div>
                            </td>
                            <td>
                                <div class="time-inputs">
                                    <input type="time" name="bh_<?= $day ?>_start2" value="<?= htmlspecialchars($businessHours[$day][1]['start'] ?? '') ?>" placeholder="19:00">
                                    <span class="time-separator">to</span>
                                    <input type="time" name="bh_<?= $day ?>_end2" value="<?= htmlspecialchars($businessHours[$day][1]['end'] ?? '') ?>" placeholder="21:00">
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Special Days Section -->
        <div class="form-section special-days">
            <h3>Special Days & Holidays</h3>

            <div class="special-days-info">
                <h4>How to format special days</h4>
                <p style="margin-bottom: 15px;">Define holidays, special hours, or closed days using the format below. Separate multiple entries with <strong>|</strong> (pipe symbol).</p>

                <div style="margin-bottom: 15px;">
                    <strong>Examples:</strong>
                </div>

                <div class="example-code">
                    <!-- Closed days -->
                    2024-12-25 | 2024-01-01 |

                    <!-- Special hours -->
                    2024-12-24 09:00-14:00 | 2024-12-31 10:00-16:00 |

                    <!-- Multiple intervals in one day -->
                    2024-07-04 09:00-12:00 18:00-20:00
                </div>

                <div style="font-size: 14px; color: #495057;">
                    <strong>Format rules:</strong><br>
                    • <code>YYYY-MM-DD</code> for dates<br>
                    • <code>HH:MM-HH:MM</code> for time intervals<br>
                    • Leave time empty for closed days<br>
                    • Use spaces between multiple intervals<br>
                    • Use <code>|</code> to separate different days
                </div>
            </div>

            <div class="form-group">
                <label for="special_days_text">Special Days Configuration</label>
                <textarea
                    name="special_days_text"
                    id="special_days_text"
                    rows="6"
                    class="form-control special-days-textarea"
                    placeholder="2024-12-25 | 2024-12-24 09:00-14:00 | 2024-07-04 09:00-12:00 18:00-20:00"><?= htmlspecialchars($specialDaysText) ?></textarea>
                <div class="form-help">Define holidays, special hours, or closed days. Leave empty if no special days are configured.</div>
            </div>
        </div>

        <!-- Save Button -->
        <div style="text-align: center; padding: 30px;">
            <button type="submit" class="save-button">
                Save Settings
            </button>
        </div>
    </form>
</div>

<script>
    // Add some interactive features
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-save indication
        const form = document.querySelector('.settings-form');
        const inputs = form.querySelectorAll('input, textarea');

        inputs.forEach(input => {
            input.addEventListener('change', function() {
                this.style.borderColor = '#ffc107';
                setTimeout(() => {
                    this.style.borderColor = '';
                }, 1000);
            });
        });

        // Validate time inputs
        const timeInputs = document.querySelectorAll('input[type="time"]');
        timeInputs.forEach(input => {
            input.addEventListener('change', function() {
                const row = this.closest('tr');
                const startInputs = row.querySelectorAll('input[name*="_start"]');
                const endInputs = row.querySelectorAll('input[name*="_end"]');

                // Simple validation to ensure end time is after start time
                for (let i = 0; i < startInputs.length; i++) {
                    if (startInputs[i].value && endInputs[i].value) {
                        if (startInputs[i].value >= endInputs[i].value) {
                            endInputs[i].style.borderColor = '#dc3545';
                            setTimeout(() => {
                                endInputs[i].style.borderColor = '';
                            }, 3000);
                        }
                    }
                }
            });
        });

        // Special days textarea helper
        const specialDaysTextarea = document.getElementById('special_days_text');
        if (specialDaysTextarea) {
            specialDaysTextarea.addEventListener('input', function() {
                // Count entries
                const entries = this.value.split('|').filter(entry => entry.trim().length > 0);
                const counter = document.getElementById('entry-counter');
                if (!counter) {
                    const counterDiv = document.createElement('div');
                    counterDiv.id = 'entry-counter';
                    counterDiv.className = 'form-help';
                    counterDiv.style.marginTop = '10px';
                    this.parentNode.appendChild(counterDiv);
                }
                document.getElementById('entry-counter').textContent = `${entries.length} special day(s) configured`;
            });

            // Trigger initial count
            specialDaysTextarea.dispatchEvent(new Event('input'));
        }
    });
</script>