<?php

require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/reservation_handler.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$reservationId = sanitize_input($data['reservation_id'] ?? '');

if (!$reservationId) {
    echo json_encode(['success' => false, 'message' => 'Reservation ID is required']);
    exit;
}

$reservationHandler = new ReservationHandler();
if (!method_exists($reservationHandler, 'completeReservation')) {
    echo json_encode(['success' => false, 'message' => 'Complete function not implemented']);
    exit;
}

// You may need to implement completeReservation in ReservationHandler if missing
$result = $reservationHandler->completeReservation($reservationId);

if ($result['success']) {
    echo json_encode(['success' => true, 'message' => $result['message']]);
} else {
    echo json_encode(['success' => false, 'message' => $result['message']]);
}
