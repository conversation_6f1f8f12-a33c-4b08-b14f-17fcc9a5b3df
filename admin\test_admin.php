<?php
/**
 * Test Admin Panel Loading
 * This script tests the exact same loading sequence as admin/index.php
 */

echo "<h1>Admin Panel Loading Test</h1>";
echo "<p>Current URL: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . $_SERVER['REQUEST_URI'] . "</p>";

// Step 1: Tenant system loading (same as admin/index.php)
echo "<h2>1. Tenant System Loading</h2>";
try {
    echo "🔍 Loading tenant_init...<br>";
    require_once '../includes/tenant_init.php';
    echo "✅ Tenant system loaded successfully<br>";
} catch (Exception $e) {
    echo "❌ Tenant system error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Step 2: Session start (same as admin/index.php)
echo "<h2>2. Session Start</h2>";
try {
    echo "🔍 Starting session...<br>";
    session_start();
    echo "✅ Session started<br>";
    echo "✅ Session ID: " . session_id() . "<br>";
    echo "✅ Admin logged in: " . (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true ? 'YES' : 'NO') . "<br>";
} catch (Exception $e) {
    echo "❌ Session error: " . $e->getMessage() . "<br>";
}

// Step 3: Config loading (same as admin/index.php)
echo "<h2>3. Config Loading</h2>";
try {
    echo "🔍 Loading config...<br>";
    require_once '../includes/config.php';
    echo "✅ Config loaded<br>";
} catch (Exception $e) {
    echo "❌ Config error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Step 4: Functions loading (same as admin/index.php)
echo "<h2>4. Functions Loading</h2>";
try {
    echo "🔍 Loading functions...<br>";
    require_once '../includes/functions.php';
    echo "✅ Functions loaded<br>";
} catch (Exception $e) {
    echo "❌ Functions error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Step 5: Settings loading (same as admin/index.php)
echo "<h2>5. Settings Loading</h2>";
try {
    echo "🔍 Loading settings...<br>";
    $settings = getSettings();
    echo "✅ Settings loaded<br>";
    echo "📋 Site Name: " . htmlspecialchars($settings['site_name'] ?? 'NOT SET') . "<br>";
    echo "📋 Admin Email: " . htmlspecialchars($settings['admin_email'] ?? 'NOT SET') . "<br>";
} catch (Exception $e) {
    echo "❌ Settings error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal settings error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Step 6: Handler includes (same as admin/index.php)
echo "<h2>6. Handler Includes</h2>";
try {
    echo "🔍 Loading handlers...<br>";
    require_once '../includes/reservation_handler.php';
    require_once '../includes/customer_handler.php';
    require_once '../includes/admin_functions.php';
    require_once '../includes/employee_handler.php';
    require_once '../includes/service_handler.php';
    echo "✅ All handlers included<br>";
} catch (Exception $e) {
    echo "❌ Handler include error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Step 7: Handler instantiation (same as admin/index.php)
echo "<h2>7. Handler Instantiation</h2>";
try {
    echo "🔍 Creating handler instances...<br>";
    $reservationHandler = new ReservationHandler();
    echo "✅ ReservationHandler created<br>";
    
    $customerHandler = new CustomerHandler();
    echo "✅ CustomerHandler created<br>";
    
    $employeeHandler = new EmployeeHandler();
    echo "✅ EmployeeHandler created<br>";
    
    $serviceHandler = new ServiceHandler();
    echo "✅ ServiceHandler created<br>";
    
} catch (Exception $e) {
    echo "❌ Handler instantiation error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal handler error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Step 8: Basic functionality test
echo "<h2>8. Basic Functionality Test</h2>";
try {
    echo "🔍 Testing basic functions...<br>";
    
    if (function_exists('sanitize_input')) {
        echo "✅ sanitize_input function exists<br>";
    } else {
        echo "❌ sanitize_input function missing<br>";
    }
    
    if (function_exists('log_activity')) {
        echo "✅ log_activity function exists<br>";
    } else {
        echo "❌ log_activity function missing<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Function test error: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Complete</h2>";
echo "<p>If all tests pass here, the admin panel should work.</p>";
echo "<p><strong>Login Status:</strong> " . (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true ? 'LOGGED IN' : 'NOT LOGGED IN') . "</p>";

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo "<p><a href='login.php'>Go to Login</a></p>";
} else {
    echo "<p><a href='index.php'>Go to Admin Panel</a></p>";
}
?>
