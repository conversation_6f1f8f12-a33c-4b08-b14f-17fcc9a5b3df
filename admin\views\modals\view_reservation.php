<?php
require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
require_once '../../../includes/reservation_handler.php';
require_once '../../../includes/customer_handler.php';

$reservationId = $_GET['id'] ?? '';
$reservationHandler = new ReservationHandler();
$reservation = $reservationHandler->getReservationById($reservationId);

if (!$reservation) {
    echo '<div class="alert alert-error">Reservation not found.</div>';
    exit;
}

$services = getServices();
$customerHandler = new CustomerHandler();
$customer = $customerHandler->getCustomerById($reservation['customer_id']);

// Get service details
$service = null;
$serviceName = 'Unknown Service';
if (isset($reservation['service']) && !empty($reservation['service'])) {
    if (isset($services[$reservation['service']])) {
        $service = $services[$reservation['service']];
        $serviceName = $service['name'];
    } else {
        $serviceName = $reservation['service'];
    }
}

// Calculate time details
$startTime = $reservation['time'];
$endTime = date('H:i', strtotime($startTime . ' + ' . $reservation['duration'] . ' minutes'));
$timeRange = format_time_range($startTime, $reservation['duration']);

// Status information
$statusConfig = [
    'confirmed' => ['icon' => 'fa-check-circle', 'color' => 'success', 'label' => 'Confirmed'],
    'completed' => ['icon' => 'fa-check-double', 'color' => 'info', 'label' => 'Completed'],
    'cancelled' => ['icon' => 'fa-times-circle', 'color' => 'danger', 'label' => 'Cancelled'],
    'pending' => ['icon' => 'fa-clock', 'color' => 'warning', 'label' => 'Pending']
];

$currentStatus = $statusConfig[$reservation['status']] ?? $statusConfig['pending'];

// Calculate days until/since appointment
$appointmentDate = new DateTime($reservation['date']);
$today = new DateTime();
$daysDiff = $today->diff($appointmentDate);
$isUpcoming = $appointmentDate >= $today;

// Generate customer initials for avatar
$customerInitials = '';
if ($customer) {
    $nameParts = explode(' ', $customer['name']);
    foreach ($nameParts as $part) {
        if (!empty($part)) {
            $customerInitials .= strtoupper($part[0]);
        }
    }
    $customerInitials = substr($customerInitials, 0, 2);
}
?>
<div class="reservation-details-enhanced">
    <!-- Reservation Header -->
    <div class="reservation-header">
        <div class="reservation-status-badge">
            <i class="fas <?= $currentStatus['icon'] ?>"></i>
            <span class="status-<?= $currentStatus['color'] ?>"><?= $currentStatus['label'] ?></span>
        </div>
        <div class="reservation-id">
            <span class="label">Reservation ID:</span>
            <span class="value"><?= htmlspecialchars($reservation['id']) ?></span>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="reservation-content-grid">
        <!-- Customer Card -->
        <div class="detail-card customer-card">
            <div class="card-header">
                <h4><i class="fas fa-user"></i> Customer Information</h4>
            </div>
            <div class="card-body">
                <div class="customer-profile">
                    <div class="customer-avatar">
                        <?php if (!empty($customer['avatar_url'])): ?>
                            <img src="<?= htmlspecialchars($customer['avatar_url']) ?>" alt="<?= htmlspecialchars($customer['name']) ?>">
                        <?php else: ?>
                            <div class="avatar-initials"><?= $customerInitials ?></div>
                        <?php endif; ?>
                    </div>
                    <div class="customer-info">
                        <h5 class="customer-name"><?= htmlspecialchars($customer['name'] ?? 'Unknown Customer') ?></h5>
                        <div class="customer-contact">
                            <?php if (!empty($customer['email'])): ?>
                                <div class="contact-item">
                                    <i class="fas fa-envelope"></i>
                                    <a href="mailto:<?= htmlspecialchars($customer['email']) ?>"><?= htmlspecialchars($customer['email']) ?></a>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($customer['mobile'])): ?>
                                <div class="contact-item">
                                    <i class="fas fa-phone"></i>
                                    <a href="tel:<?= htmlspecialchars($customer['mobile']) ?>"><?= htmlspecialchars($customer['mobile']) ?></a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="customer-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewCustomer('<?= htmlspecialchars($customer['id']) ?>')">
                        <i class="fas fa-eye"></i> View Profile
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="window.open('mailto:<?= htmlspecialchars($customer['email']) ?>')">
                        <i class="fas fa-envelope"></i> Email
                    </button>
                </div>
            </div>
        </div>

        <!-- Service Card -->
        <div class="detail-card service-card">
            <div class="card-header">
                <h4><i class="fas fa-concierge-bell"></i> Service Details</h4>
            </div>
            <div class="card-body">
                <div class="service-info">
                    <h5 class="service-name"><?= htmlspecialchars($serviceName) ?></h5>
                    <?php if ($service && !empty($service['description'])): ?>
                        <p class="service-description"><?= htmlspecialchars($service['description']) ?></p>
                    <?php endif; ?>
                </div>
                <div class="service-details">
                    <div class="detail-item">
                        <div class="detail-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="detail-content">
                            <span class="detail-label">Duration</span>
                            <span class="detail-value"><?= htmlspecialchars($reservation['duration']) ?> minutes</span>
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-icon">
                            <i class="fas fa-euro-sign"></i>
                        </div>
                        <div class="detail-content">
                            <span class="detail-label">Price</span>
                            <span class="detail-value">€<?= number_format($reservation['price'], 2) ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Appointment Details -->
    <div class="appointment-section">
        <div class="card-header">
            <h4><i class="fas fa-calendar-alt"></i> Appointment Details</h4>
        </div>
        <div class="card-body">
            <div class="appointment-timeline">
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="timeline-content">
                        <h6>Date</h6>
                        <p class="appointment-date">
                            <?= date('l, F j, Y', strtotime($reservation['date'])) ?>
                            <?php if ($isUpcoming): ?>
                                <span class="days-badge upcoming">
                                    <?= $daysDiff->days == 0 ? 'Today' : 'In ' . $daysDiff->days . ' day' . ($daysDiff->days > 1 ? 's' : '') ?>
                                </span>
                            <?php else: ?>
                                <span class="days-badge past">
                                    <?= $daysDiff->days == 0 ? 'Today' : $daysDiff->days . ' day' . ($daysDiff->days > 1 ? 's' : '') . ' ago' ?>
                                </span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="timeline-content">
                        <h6>Time</h6>
                        <p class="appointment-time">
                            <span class="time-range"><?= $timeRange ?></span>
                            <span class="time-details"><?= $startTime ?> - <?= $endTime ?></span>
                        </p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="timeline-content">
                        <h6>Status</h6>
                        <p class="appointment-status">
                            <span class="status-badge status-<?= $currentStatus['color'] ?>">
                                <i class="fas <?= $currentStatus['icon'] ?>"></i>
                                <?= $currentStatus['label'] ?>
                            </span>
                        </p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="timeline-content">
                        <h6>Booked</h6>
                        <p class="booking-date">
                            <?= date('F j, Y \a\t H:i', strtotime($reservation['created_at'])) ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="reservation-actions">
        <?php if ($reservation['status'] !== 'completed' && $reservation['status'] !== 'cancelled'): ?>
            <button class="btn btn-primary" onclick="editReservation('<?= htmlspecialchars($reservation['id']) ?>')">
                <i class="fas fa-edit"></i> Edit Reservation
            </button>
        <?php endif; ?>

        <?php if ($reservation['status'] === 'confirmed'): ?>
            <button class="btn btn-success" onclick="completeReservation('<?= htmlspecialchars($reservation['id']) ?>')">
                <i class="fas fa-check"></i> Mark Complete
            </button>
        <?php endif; ?>

        <?php if ($reservation['status'] !== 'cancelled' && $reservation['status'] !== 'completed'): ?>
            <button class="btn btn-danger" onclick="cancelReservation('<?= htmlspecialchars($reservation['id']) ?>')">
                <i class="fas fa-times"></i> Cancel
            </button>
        <?php endif; ?>

        <button class="btn btn-info" onclick="window.print()">
            <i class="fas fa-print"></i> Print
        </button>

        <button class="btn btn-secondary" onclick="viewCustomer('<?= htmlspecialchars($customer['id']) ?>')">
            <i class="fas fa-user"></i> Customer Profile
        </button>
    </div>
</div>