<?php
// Pagination settings
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$pageSize = isset($_GET['pageSize']) ? max(10, min(100, intval($_GET['pageSize']))) : 20;
$offset = ($page - 1) * $pageSize;

// Search functionality
$searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
$isSearching = !empty($searchTerm);

if ($isSearching) {
    // Get search results with pagination
    $totalCustomers = $customerHandler->getSearchCustomerCount($searchTerm);
    $totalPages = ceil($totalCustomers / $pageSize);
    $customers = $customerHandler->searchCustomers($searchTerm, $pageSize, $offset);
} else {
    // Get all customers with pagination
    $totalCustomers = $customerHandler->getCustomerCount();
    $totalPages = ceil($totalCustomers / $pageSize);
    $customers = $customerHandler->getCustomersPaginated($pageSize, $offset);
}
?>
<div>
    <h2>Customers</h2>

    <div class="admin-header">
        <div class="header-left">
            <button class="btn btn-primary" onclick="openModal('Add Customer', 'views/modals/add_customer.php')">
                <i class="fas fa-plus"></i> Add Customer
            </button>
        </div>

        <div class="header-center">
            <div class="view-toggle">
                <span class="view-toggle-label">View:</span>
                <div class="view-toggle-buttons">
                    <button class="view-toggle-btn active" onclick="switchCustomerView('cards')" data-view="cards">
                        <i class="fas fa-th-large"></i> Cards
                    </button>
                    <button class="view-toggle-btn" onclick="switchCustomerView('table')" data-view="table">
                        <i class="fas fa-table"></i> Table
                    </button>
                </div>
            </div>
        </div>

        <div class="header-right">
            <div class="search-container">
                <i class="search-icon fas fa-search"></i>
                <input type="text"
                    id="customers-search"
                    class="search-input"
                    placeholder="Search ALL customers by name, email, or phone..."
                    value="<?= htmlspecialchars($searchTerm) ?>"
                    onkeypress="handleSearchKeypress(event)">
                <button class="search-clear" onclick="clearServerSearch()" title="Clear search" <?= $isSearching ? '' : 'style="opacity: 0;"' ?>>
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Search Results Info -->
    <?php if ($isSearching): ?>
        <div class="search-results-info" style="display: flex;">
            <span class="search-results-count">
                Found <?= $totalCustomers ?> customer<?= $totalCustomers !== 1 ? 's' : '' ?> matching "<?= htmlspecialchars($searchTerm) ?>"
                <?php if ($totalPages > 1): ?>
                    (showing page <?= $page ?> of <?= $totalPages ?>)
                <?php endif; ?>
            </span>
            <button class="search-clear-all" onclick="clearServerSearch()">Clear search</button>
        </div>
    <?php endif; ?>

    <!-- No Results Message -->
    <?php if ($isSearching && $totalCustomers === 0): ?>
        <div class="no-results" style="display: block;">
            <div class="no-results-icon">
                <i class="fas fa-search"></i>
            </div>
            <h3>No customers found</h3>
            <p>No customers match "<?= htmlspecialchars($searchTerm) ?>". Try different search terms.</p>
        </div>
    <?php endif; ?>

    <!-- Card View -->
    <div id="customers-cards-view" class="card-grid">
        <?php foreach ($customers as $c): ?>
            <?php
            // Get reservation count for this customer
            $reservationCount = 0;
            $hasFutureReservations = false;
            try {
                if (isset($reservationHandler)) {
                    $customerReservations = $reservationHandler->getReservationsForCustomer($c['id']);
                    $reservationCount = count($customerReservations);

                    // Check for future reservations
                    $today = date('Y-m-d');
                    foreach ($customerReservations as $res) {
                        if ($res['date'] >= $today && $res['status'] === 'confirmed') {
                            $hasFutureReservations = true;
                            break;
                        }
                    }
                }
            } catch (Exception $e) {
                // Handle error silently
            }

            // Generate initials for avatar
            $initials = '';
            $nameParts = explode(' ', $c['name']);
            foreach ($nameParts as $part) {
                if (!empty($part)) {
                    $initials .= strtoupper($part[0]);
                }
            }
            $initials = substr($initials, 0, 2);
            ?>
            <div class="data-card customer-card">
                <div class="card-header">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div class="customer-avatar" style="width: 40px; height: 40px; border-radius: 50%; background: var(--primary); color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 14px;">
                            <?= $initials ?>
                        </div>
                        <div>
                            <h3 class="card-title"><?= htmlspecialchars($c['name']) ?></h3>
                            <p class="card-subtitle"><?= htmlspecialchars($c['email']) ?></p>
                        </div>
                    </div>
                    <div class="card-id">#<?= htmlspecialchars($c['id']) ?></div>
                </div>

                <div class="card-body">
                    <?php if (!empty($c['mobile'])): ?>
                        <div class="card-field">
                            <i class="card-field-icon fas fa-phone"></i>
                            <span class="card-field-label">Phone:</span>
                            <span class="card-field-value"><?= htmlspecialchars($c['mobile']) ?></span>
                        </div>
                    <?php endif; ?>

                    <div class="card-field">
                        <i class="card-field-icon fas fa-language"></i>
                        <span class="card-field-label">Language:</span>
                        <span class="card-field-value">
                            <?php
                            $lang = $c['preferred_language'] ?? 'el';
                            $flag = $lang === 'el' ? '🇬🇷' : '🇬🇧';
                            $langName = $lang === 'el' ? 'Greek' : 'English';
                            echo $flag . ' ' . $langName;
                            ?>
                        </span>
                    </div>

                    <div class="card-field">
                        <i class="card-field-icon fas fa-calendar-plus"></i>
                        <span class="card-field-label">Joined:</span>
                        <span class="card-field-value">
                            <?= isset($c['created_at']) ? htmlspecialchars(date('M j, Y', strtotime($c['created_at']))) : 'N/A' ?>
                        </span>
                    </div>

                    <div class="card-stats">
                        <div class="card-stat">
                            <span class="card-stat-value"><?= $reservationCount ?></span>
                            <span class="card-stat-label">Bookings</span>
                        </div>
                        <?php if ($hasFutureReservations): ?>
                            <div class="card-stat">
                                <span class="card-stat-value" style="color: var(--warning);">
                                    <i class="fas fa-clock"></i>
                                </span>
                                <span class="card-stat-label">Upcoming</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="card-actions">
                        <button class="btn btn-sm btn-info" onclick="viewCustomer('<?= htmlspecialchars($c['id'], ENT_QUOTES) ?>')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="editCustomer('<?= htmlspecialchars($c['id'], ENT_QUOTES) ?>')" title="Edit Customer">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="openModal('Add Reservation', 'views/modals/add_reservation.php?customer_id=<?= htmlspecialchars($c['id'], ENT_QUOTES) ?>')" title="New Booking">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteCustomer('<?= htmlspecialchars($c['id'], ENT_QUOTES) ?>')" title="Delete Customer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Table View (Hidden by default) -->
    <div id="customers-table-view" style="display: none;">
        <table class="table" data-sortable>
            <thead>
                <tr>
                    <th data-sort>ID</th>
                    <th data-sort>Name</th>
                    <th data-sort>Email</th>
                    <th data-sort>Mobile</th>
                    <th data-sort>Language</th>
                    <th data-sort>Created</th>
                    <th data-sort>Reservations</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($customers as $c): ?>
                    <?php
                    // Get reservation count for this customer
                    $reservationCount = 0;
                    $hasFutureReservations = false;
                    try {
                        if (isset($reservationHandler)) {
                            $customerReservations = $reservationHandler->getReservationsForCustomer($c['id']);
                            $reservationCount = count($customerReservations);

                            // Check for future reservations
                            $today = date('Y-m-d');
                            foreach ($customerReservations as $reservation) {
                                if ($reservation['date'] >= $today && $reservation['status'] === 'confirmed') {
                                    $hasFutureReservations = true;
                                    break;
                                }
                            }
                        }
                    } catch (Exception $e) {
                        // Handle error silently for display
                    }
                    ?>
                    <tr>
                        <td><?= htmlspecialchars($c['id']) ?></td>
                        <td><?= htmlspecialchars($c['name']) ?></td>
                        <td><?= htmlspecialchars($c['email']) ?></td>
                        <td><?= htmlspecialchars($c['mobile']) ?></td>
                        <td>
                            <?php
                            $lang = $c['preferred_language'] ?? 'el';
                            $flag = $lang === 'el' ? '🇬🇷' : '🇬🇧';
                            $langName = $lang === 'el' ? 'Greek' : 'English';
                            echo $flag . ' ' . $langName;
                            ?>
                        </td>
                        <td><?= isset($c['created_at']) ? htmlspecialchars(date('Y-m-d H:i', strtotime($c['created_at']))) : 'N/A' ?></td>
                        <td>
                            <span class="badge <?= $reservationCount > 0 ? 'badge-info' : 'badge-secondary' ?>">
                                <?= $reservationCount ?>
                            </span>
                            <?php if ($hasFutureReservations): ?>
                                <span class="badge badge-warning" title="Has upcoming reservations">
                                    <i class="fas fa-clock"></i>
                                </span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewCustomer('<?= htmlspecialchars($c['id'], ENT_QUOTES) ?>')" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="editCustomer('<?= htmlspecialchars($c['id'], ENT_QUOTES) ?>')" title="Edit Customer">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteCustomer('<?= htmlspecialchars($c['id'], ENT_QUOTES) ?>')" title="Delete Customer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination Controls -->
<div class="pagination-container">
    <div class="pagination-info">
        <span>
            Showing <?= min($offset + 1, $totalCustomers) ?>-<?= min($offset + $pageSize, $totalCustomers) ?> of <?= $totalCustomers ?>
            <?= $isSearching ? 'matching customers' : 'customers' ?>
            <?php if ($isSearching): ?>
                <small>(searched across all customers)</small>
            <?php endif; ?>
        </span>
        <div class="page-size-selector">
            <label for="pageSize">Show:</label>
            <select id="pageSize" class="page-size-select" onchange="changePageSize(this.value)">
                <option value="10" <?= $pageSize == 10 ? 'selected' : '' ?>>10</option>
                <option value="20" <?= $pageSize == 20 ? 'selected' : '' ?>>20</option>
                <option value="50" <?= $pageSize == 50 ? 'selected' : '' ?>>50</option>
                <option value="100" <?= $pageSize == 100 ? 'selected' : '' ?>>100</option>
            </select>
        </div>
        <div class="performance-info">
            <span class="performance-badge">Fast Load</span>
        </div>
    </div>

    <div class="pagination-controls">
        <?php if ($page > 1): ?>
            <button class="pagination-btn" onclick="goToPage(1)" title="First page">
                <i class="fas fa-angle-double-left"></i>
            </button>
            <button class="pagination-btn" onclick="goToPage(<?= $page - 1 ?>)" title="Previous page">
                <i class="fas fa-angle-left"></i>
            </button>
        <?php else: ?>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-double-left"></i>
            </button>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-left"></i>
            </button>
        <?php endif; ?>

        <?php
        // Show page numbers
        $startPage = max(1, $page - 2);
        $endPage = min($totalPages, $page + 2);

        if ($startPage > 1): ?>
            <button class="pagination-btn" onclick="goToPage(1)">1</button>
            <?php if ($startPage > 2): ?>
                <span class="pagination-ellipsis">...</span>
            <?php endif; ?>
        <?php endif; ?>

        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
            <button class="pagination-btn <?= $i == $page ? 'active' : '' ?>" onclick="goToPage(<?= $i ?>)">
                <?= $i ?>
            </button>
        <?php endfor; ?>

        <?php if ($endPage < $totalPages): ?>
            <?php if ($endPage < $totalPages - 1): ?>
                <span class="pagination-ellipsis">...</span>
            <?php endif; ?>
            <button class="pagination-btn" onclick="goToPage(<?= $totalPages ?>)"><?= $totalPages ?></button>
        <?php endif; ?>

        <?php if ($page < $totalPages): ?>
            <button class="pagination-btn" onclick="goToPage(<?= $page + 1 ?>)" title="Next page">
                <i class="fas fa-angle-right"></i>
            </button>
            <button class="pagination-btn" onclick="goToPage(<?= $totalPages ?>)" title="Last page">
                <i class="fas fa-angle-double-right"></i>
            </button>
        <?php else: ?>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-right"></i>
            </button>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-double-right"></i>
            </button>
        <?php endif; ?>
    </div>
</div>

<style>
    .customers-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
        gap: 15px;
    }

    .header-left {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .header-controls {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .view-toggle-label {
        font-size: 14px;
        color: var(--text-secondary);
        margin-right: 8px;
    }

    @media (max-width: 768px) {
        .customers-header {
            flex-direction: column;
            align-items: stretch;
        }

        .header-controls {
            justify-content: center;
        }
    }
</style>

<script>
    // View switching functionality for customers
    function switchCustomerView(viewType) {
        const cardsView = document.getElementById('customers-cards-view');
        const tableView = document.getElementById('customers-table-view');
        const toggleButtons = document.querySelectorAll('.view-toggle-btn');

        // Update button states
        toggleButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.view === viewType) {
                btn.classList.add('active');
            }
        });

        // Show/hide views
        if (viewType === 'cards') {
            cardsView.style.display = 'grid';
            tableView.style.display = 'none';
        } else {
            cardsView.style.display = 'none';
            tableView.style.display = 'block';
        }

        // Save preference
        localStorage.setItem('customers-view', viewType);
    }

    // Server-side search functionality
    function handleSearchKeypress(event) {
        if (event.key === 'Enter') {
            performServerSearch();
        }
    }

    function performServerSearch() {
        const searchTerm = document.getElementById('customers-search').value.trim();
        const url = new URL(window.location);

        if (searchTerm) {
            url.searchParams.set('search', searchTerm);
        } else {
            url.searchParams.delete('search');
        }
        url.searchParams.set('page', 1); // Reset to first page

        window.location.href = url.toString();
    }

    function clearServerSearch() {
        const url = new URL(window.location);
        url.searchParams.delete('search');
        url.searchParams.set('page', 1);
        window.location.href = url.toString();
    }

    // Legacy client-side search (for backward compatibility)
    function searchCustomers() {
        // For now, redirect to server-side search
        performServerSearch();
    }

    function clearSearch(type) {
        clearServerSearch();
    }

    function applyCustomerFilters() {
        const searchTerm = document.getElementById('customers-search').value.toLowerCase().trim();

        let visibleCount = 0;

        // Filter table rows
        const rows = document.querySelectorAll('#customers-table-view tbody tr');
        rows.forEach(row => {
            let showRow = true;

            if (searchTerm) {
                const searchableText = Array.from(row.cells).map(cell => cell.textContent.toLowerCase()).join(' ');
                if (!searchableText.includes(searchTerm)) {
                    showRow = false;
                }
            }

            row.style.display = showRow ? '' : 'none';
            if (showRow) visibleCount++;
        });

        // Filter cards
        const cards = document.querySelectorAll('#customers-cards-view .data-card');
        let visibleCardCount = 0;

        cards.forEach(card => {
            let showCard = true;

            if (searchTerm) {
                const customerName = card.querySelector('.card-title')?.textContent.toLowerCase() || '';
                const customerEmail = card.querySelector('.card-subtitle')?.textContent.toLowerCase() || '';
                const cardText = card.textContent.toLowerCase();

                if (!customerName.includes(searchTerm) &&
                    !customerEmail.includes(searchTerm) &&
                    !cardText.includes(searchTerm)) {
                    showCard = false;
                }
            }

            card.style.display = showCard ? 'block' : 'none';
            if (showCard) visibleCardCount++;
        });

        // Update search results info
        updateCustomerSearchResults(searchTerm, Math.max(visibleCount, visibleCardCount), cards.length);
    }

    function updateCustomerSearchResults(searchTerm, visibleCount, totalCount) {
        const resultsInfo = document.getElementById('customers-search-results-info');
        const noResults = document.getElementById('customers-no-results');
        const resultsCount = resultsInfo?.querySelector('.search-results-count');

        if (searchTerm) {
            if (visibleCount === 0) {
                if (resultsInfo) resultsInfo.style.display = 'none';
                if (noResults) noResults.style.display = 'block';
            } else {
                if (resultsInfo) resultsInfo.style.display = 'flex';
                if (noResults) noResults.style.display = 'none';
                if (resultsCount) resultsCount.textContent = `Showing ${visibleCount} of ${totalCount} customers`;
            }
        } else {
            if (resultsInfo) resultsInfo.style.display = 'none';
            if (noResults) noResults.style.display = 'none';
        }
    }

    // Pagination functions (preserve search terms)
    function goToPage(page) {
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        // Preserve existing search and pageSize parameters
        window.location.href = url.toString();
    }

    function changePageSize(pageSize) {
        const url = new URL(window.location);
        url.searchParams.set('pageSize', pageSize);
        url.searchParams.set('page', 1); // Reset to first page
        // Preserve existing search parameter
        window.location.href = url.toString();
    }

    // Load saved view preference
    document.addEventListener('DOMContentLoaded', function() {
        const savedView = localStorage.getItem('customers-view') || 'cards';
        switchCustomerView(savedView);
    });
</script>