<?php
/**
 * Simple Settings Test - Isolate the exact issue
 */

echo "<h1>Simple Settings Test</h1>";

// Test 1: Basic includes
echo "<h2>1. Basic Includes</h2>";
try {
    echo "🔍 Loading config...<br>";
    require_once 'includes/config.php';
    echo "✅ Config loaded<br>";
    
    echo "🔍 Loading tenant_init...<br>";
    require_once 'includes/tenant_init.php';
    echo "✅ Tenant init loaded<br>";
    
    echo "🔍 Loading functions...<br>";
    require_once 'includes/functions.php';
    echo "✅ Functions loaded<br>";
    
} catch (Exception $e) {
    echo "❌ Include error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
    exit;
} catch (Error $e) {
    echo "❌ Fatal error in includes: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
    exit;
}

// Test 2: Tenant Context
echo "<h2>2. Tenant Context</h2>";
try {
    require_once 'includes/TenantContext.php';
    
    $tenantId = TenantContext::getTenant();
    echo "✅ Tenant ID: " . $tenantId . "<br>";
    
} catch (Exception $e) {
    echo "❌ Tenant context error: " . $e->getMessage() . "<br>";
}

// Test 3: Database
echo "<h2>3. Database</h2>";
try {
    require_once 'includes/Database.php';
    
    $db = Database::getInstance();
    $conn = $db->getConnection();
    echo "✅ Database connected<br>";
    
    // Check if tenant_settings table exists
    $result = $conn->query("SELECT name FROM sqlite_master WHERE type='table' AND name='tenant_settings'");
    $row = $result->fetchArray();
    echo "✅ tenant_settings table exists: " . ($row ? 'YES' : 'NO') . "<br>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 4: TenantSettingsManager
echo "<h2>4. TenantSettingsManager</h2>";
try {
    echo "🔍 Loading TenantSettingsManager...<br>";
    require_once 'includes/TenantSettingsManager.php';
    echo "✅ TenantSettingsManager loaded<br>";
    
    echo "🔍 Testing getAllSettings...<br>";
    $settings = TenantSettingsManager::getAllSettings();
    echo "✅ getAllSettings returned: " . json_encode($settings) . "<br>";
    
} catch (Exception $e) {
    echo "❌ TenantSettingsManager error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal error in TenantSettingsManager: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Test 5: getSettings function
echo "<h2>5. getSettings Function</h2>";
try {
    echo "🔍 Calling getSettings...<br>";
    $settings = getSettings();
    echo "✅ getSettings returned: " . json_encode($settings) . "<br>";
    
} catch (Exception $e) {
    echo "❌ getSettings error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal error in getSettings: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

echo "<h2>Test Complete</h2>";
?>
