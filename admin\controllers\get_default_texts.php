<?php
require_once '../../includes/config.php';
require_once '../../includes/unified_text_manager.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $key = $input['key'] ?? '';
    $type = $input['type'] ?? 'customer'; // 'customer' or 'email'

    if (empty($key)) {
        echo json_encode(['success' => false, 'message' => 'No key provided']);
        exit;
    }

    if ($type === 'customer') {
        // Get default customer text
        $defaultTexts = UnifiedTextManager::getOriginalCustomerTexts();

        if (isset($defaultTexts[$key])) {
            $textData = $defaultTexts[$key];

            // Handle old format (string) and new format (array)
            if (is_string($textData)) {
                $result = ['primary' => $textData, 'secondary' => ''];
            } else {
                $result = [
                    'primary' => $textData['primary'] ?? '',
                    'secondary' => $textData['secondary'] ?? ''
                ];
            }

            echo json_encode([
                'success' => true,
                'key' => $key,
                'type' => 'customer',
                'texts' => $result
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Text key not found']);
        }
    } else if ($type === 'email') {
        // Get default email text
        $originalEmailTexts = UnifiedTextManager::getOriginalEmailTexts();

        if (isset($originalEmailTexts['en'][$key])) {
            $result = [
                'greek' => $originalEmailTexts['el'][$key] ?? '',
                'english' => $originalEmailTexts['en'][$key] ?? ''
            ];

            echo json_encode([
                'success' => true,
                'key' => $key,
                'type' => 'email',
                'texts' => $result
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Email text key not found']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid type']);
    }
} catch (Exception $e) {
    error_log('Get default texts error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server error occurred']);
}
