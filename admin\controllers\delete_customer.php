<?php

require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/admin_functions.php';

header('Content-Type: application/json');

// Add debugging
error_log("Delete customer request received");

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$customerId = sanitize_input($data['customer_id'] ?? '');
$forceDelete = $data['force_delete'] ?? false;

error_log("Customer ID: " . $customerId . ", Force Delete: " . ($forceDelete ? 'true' : 'false'));

if (!$customerId) {
    echo json_encode(['success' => false, 'message' => 'Customer ID is required']);
    exit;
}

if (!function_exists('deleteCustomer')) {
    echo json_encode(['success' => false, 'message' => 'Delete function not implemented']);
    exit;
}

$result = deleteCustomer($customerId, $forceDelete);

error_log("Delete result: " . json_encode($result));

if ($result['success']) {
    echo json_encode(['success' => true, 'message' => $result['message']]);
} else {
    echo json_encode([
        'success' => false,
        'message' => $result['message'],
        'has_future_reservations' => $result['has_future_reservations'] ?? false
    ]);
}
