<?php

/**
 * Tenant Initialization
 *
 * This file should be included at the beginning of every page
 * to initialize the tenant context for multi-tenant operations.
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/TenantContext.php';

// Initialize tenant context from request
try {
    if (!TenantContext::initializeFromRequest()) {
        // Handle case where no tenant could be determined
        if (php_sapi_name() !== 'cli') {
            // Only redirect if not running from command line
            $host = $_SERVER['HTTP_HOST'] ?? '';

            // If on main domain, redirect to www or show landing page
            if ($host === 'bookingspa.gr') {
                header('Location: https://www.bookingspa.gr');
                exit;
            }

            // If subdomain not found, show error page
            if (strpos($host, '.bookingspa.gr') !== false) {
                http_response_code(404);
                echo "Business not found. Please check the URL.";
                exit;
            }
        }
    }
} catch (Exception $e) {
    error_log('Tenant initialization error: ' . $e->getMessage());

    if (php_sapi_name() !== 'cli') {
        http_response_code(500);
        echo "Service temporarily unavailable. Please try again later.";
        exit;
    }
}

/**
 * Helper function to get current tenant info
 */
function getCurrentTenant(): ?array
{
    return TenantContext::getTenantData();
}

/**
 * Helper function to check if tenant is active
 */
function isTenantActive(): bool
{
    return TenantContext::isValidTenant();
}

/**
 * Helper function to get tenant setting
 */
function getTenantSetting(string $key, $default = null)
{
    return TenantContext::getSetting($key, $default);
}

/**
 * Helper function to get tenant business name
 */
function getBusinessName(): string
{
    $tenant = getCurrentTenant();
    return $tenant['business_name'] ?? 'Booking System';
}

/**
 * Helper function to get tenant subdomain
 */
function getTenantSubdomain(): string
{
    $tenant = getCurrentTenant();
    return $tenant['subdomain'] ?? 'default';
}
