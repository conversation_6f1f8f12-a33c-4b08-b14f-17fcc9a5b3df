<?php
// Pagination settings
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$pageSize = isset($_GET['pageSize']) ? max(10, min(100, intval($_GET['pageSize']))) : 20;
$offset = ($page - 1) * $pageSize;

// Search functionality
$searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
$isSearching = !empty($searchTerm);

if ($isSearching) {
    // Get search results with pagination
    $totalEmployees = $employeeHandler->getSearchEmployeeCount($searchTerm);
    $totalPages = ceil($totalEmployees / $pageSize);
    $employees = $employeeHandler->searchEmployees($searchTerm, $pageSize, $offset);
} else {
    // Get all employees with pagination
    $totalEmployees = $employeeHandler->getEmployeeCount();
    $totalPages = ceil($totalEmployees / $pageSize);
    $employees = $employeeHandler->getEmployeesPaginated($pageSize, $offset);
}

$services = getServices();
?>
<div>
    <h2>Employees</h2>

    <div class="admin-header">
        <div class="header-left">
            <button class="btn btn-primary" onclick="quickAddEmployee()">
                <i class="fas fa-plus"></i> Add Employee
            </button>
        </div>

        <div class="header-center">
            <div class="view-toggle">
                <span class="view-toggle-label">View:</span>
                <div class="view-toggle-buttons">
                    <button class="view-toggle-btn active" onclick="switchEmployeeView('cards')" data-view="cards">
                        <i class="fas fa-th-large"></i> Cards
                    </button>
                    <button class="view-toggle-btn" onclick="switchEmployeeView('table')" data-view="table">
                        <i class="fas fa-table"></i> Table
                    </button>
                </div>
            </div>
        </div>

        <div class="header-right">
            <div class="search-container">
                <i class="search-icon fas fa-search"></i>
                <input type="text"
                    id="employees-search"
                    class="search-input"
                    placeholder="Search ALL employees by name, email, phone, or services..."
                    value="<?= htmlspecialchars($searchTerm) ?>"
                    onkeypress="handleEmployeesSearchKeypress(event)">
                <button class="search-clear" onclick="clearEmployeesServerSearch()" title="Clear search" <?= $isSearching ? '' : 'style="opacity: 0;"' ?>>
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Search Results Info -->
    <?php if ($isSearching): ?>
        <div class="search-results-info" style="display: flex;">
            <span class="search-results-count">
                Found <?= $totalEmployees ?> employee<?= $totalEmployees !== 1 ? 's' : '' ?> matching "<?= htmlspecialchars($searchTerm) ?>"
                <?php if ($totalPages > 1): ?>
                    (showing page <?= $page ?> of <?= $totalPages ?>)
                <?php endif; ?>
            </span>
            <button class="search-clear-all" onclick="clearEmployeesServerSearch()">Clear search</button>
        </div>
    <?php endif; ?>

    <!-- No Results Message -->
    <?php if ($isSearching && $totalEmployees === 0): ?>
        <div class="no-results" style="display: block;">
            <div class="no-results-icon">
                <i class="fas fa-search"></i>
            </div>
            <h3>No employees found</h3>
            <p>No employees match "<?= htmlspecialchars($searchTerm) ?>". Try different search terms.</p>
        </div>
    <?php endif; ?>

    <!-- Card View -->
    <div id="employees-cards-view" class="card-grid">
        <?php foreach ($employees as $e): ?>
            <?php
            // Generate initials for avatar
            $initials = '';
            $nameParts = explode(' ', $e['name']);
            foreach ($nameParts as $part) {
                if (!empty($part)) {
                    $initials .= strtoupper($part[0]);
                }
            }
            $initials = substr($initials, 0, 2);

            // Get services for this employee
            $employeeServices = getEmployeeServices($e['id']);
            $serviceNames = array_map(function ($service) {
                return $service['name'];
            }, $employeeServices);
            $servicesList = implode(', ', $serviceNames);

            // Get today's reservations count
            $todayReservations = 0;
            $monthlyReservations = 0;
            try {
                if (isset($reservationHandler)) {
                    $today = date('Y-m-d');
                    $startOfMonth = date('Y-m-01');
                    $endOfMonth = date('Y-m-t');

                    $allReservations = $reservationHandler->getAllReservations();
                    foreach ($allReservations as $res) {
                        if ($res['employee_id'] == $e['id']) {
                            if ($res['date'] === $today) {
                                $todayReservations++;
                            }
                            if ($res['date'] >= $startOfMonth && $res['date'] <= $endOfMonth) {
                                $monthlyReservations++;
                            }
                        }
                    }
                }
            } catch (Exception $ex) {
                // Handle error silently
            }
            ?>
            <div class="data-card employee-card">
                <div class="card-header">
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <div class="employee-avatar" style="width: 40px; height: 40px; border-radius: 50%; background: var(--warning); color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 14px;">
                            <?= $initials ?>
                        </div>
                        <div>
                            <h3 class="card-title"><?= htmlspecialchars($e['name']) ?></h3>
                            <p class="card-subtitle"><?= htmlspecialchars($e['position'] ?? 'Employee') ?></p>
                        </div>
                    </div>
                    <div class="card-id">#<?= htmlspecialchars($e['id']) ?></div>
                </div>

                <div class="card-body">
                    <?php if (!empty($e['email'])): ?>
                        <div class="card-field">
                            <i class="card-field-icon fas fa-envelope"></i>
                            <span class="card-field-label">Email:</span>
                            <span class="card-field-value"><?= htmlspecialchars($e['email']) ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($e['phone'])): ?>
                        <div class="card-field">
                            <i class="card-field-icon fas fa-phone"></i>
                            <span class="card-field-label">Phone:</span>
                            <span class="card-field-value"><?= htmlspecialchars($e['phone']) ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($servicesList)): ?>
                        <div class="card-field">
                            <i class="card-field-icon fas fa-concierge-bell"></i>
                            <span class="card-field-label">Services:</span>
                            <span class="card-field-value" title="<?= htmlspecialchars($servicesList) ?>">
                                <?= strlen($servicesList) > 40 ? htmlspecialchars(substr($servicesList, 0, 40)) . '...' : htmlspecialchars($servicesList) ?>
                            </span>
                        </div>
                    <?php endif; ?>

                    <div class="card-field">
                        <i class="card-field-icon fas fa-calendar-plus"></i>
                        <span class="card-field-label">Hired:</span>
                        <span class="card-field-value">
                            <?= isset($e['hire_date']) ? htmlspecialchars(date('M j, Y', strtotime($e['hire_date']))) : 'N/A' ?>
                        </span>
                    </div>

                    <div class="card-stats">
                        <div class="card-stat">
                            <span class="card-stat-value"><?= $todayReservations ?></span>
                            <span class="card-stat-label">Today</span>
                        </div>
                        <div class="card-stat">
                            <span class="card-stat-value"><?= $monthlyReservations ?></span>
                            <span class="card-stat-label">This Month</span>
                        </div>
                    </div>

                    <?php if (!empty($e['specialties'])): ?>
                        <div class="card-badges">
                            <?php
                            $specialties = explode(',', $e['specialties']);
                            foreach ($specialties as $specialty):
                                $specialty = trim($specialty);
                                if (!empty($specialty)):
                            ?>
                                    <span class="card-badge primary"><?= htmlspecialchars($specialty) ?></span>
                            <?php
                                endif;
                            endforeach;
                            ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="card-footer">
                    <div class="card-actions">
                        <button class="btn btn-sm btn-info" onclick="viewEmployee('<?= htmlspecialchars($e['id'], ENT_QUOTES) ?>')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="editEmployee('<?= htmlspecialchars($e['id'], ENT_QUOTES) ?>')" title="Edit Employee">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-success" onclick="openModal('Employee Schedule', 'views/modals/employee_schedule.php?id=<?= htmlspecialchars($e['id'], ENT_QUOTES) ?>')" title="View Schedule">
                            <i class="fas fa-calendar"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="deleteEmployee('<?= htmlspecialchars($e['id'], ENT_QUOTES) ?>')" title="Delete Employee">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Table View (Hidden by default) -->
    <div id="employees-table-view" style="display: none;">
        <table class="table" data-sortable>
            <thead>
                <tr>
                    <th data-sort>ID</th>
                    <th data-sort>Name</th>
                    <th data-sort>Email</th>
                    <th data-sort>Phone</th>
                    <th data-sort>Services</th>
                    <th data-sort>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($employees as $key => $e): ?>
                    <?php
                    // Get services for this employee
                    $employeeServices = getEmployeeServices($e['id']);
                    $serviceNames = array_map(function ($service) {
                        return $service['name'];
                    }, $employeeServices);
                    $servicesList = implode(', ', $serviceNames);
                    ?>
                    <tr>
                        <td><?= htmlspecialchars($e['id']) ?></td>
                        <td><?= htmlspecialchars($e['name']) ?></td>
                        <td><?= htmlspecialchars($e['email']) ?></td>
                        <td><?= htmlspecialchars($e['phone']) ?></td>
                        <td title="<?= htmlspecialchars($servicesList) ?>">
                            <?= strlen($servicesList) > 30 ? htmlspecialchars(substr($servicesList, 0, 30)) . '...' : htmlspecialchars($servicesList) ?>
                        </td>
                        <td>
                            <span class="status-badge <?= $e['status'] ?>">
                                <?= ucfirst($e['status']) ?>
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewEmployee('<?= htmlspecialchars($e['id'], ENT_QUOTES) ?>')" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-secondary" onclick="editEmployee('<?= htmlspecialchars($e['id'], ENT_QUOTES) ?>')" title="Edit Employee">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="deleteEmployee('<?= htmlspecialchars($e['id'], ENT_QUOTES) ?>')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination Controls -->
<div class="pagination-container">
    <div class="pagination-info">
        <span>
            Showing <?= min($offset + 1, $totalEmployees) ?>-<?= min($offset + $pageSize, $totalEmployees) ?> of <?= $totalEmployees ?>
            <?= $isSearching ? 'matching employees' : 'employees' ?>
            <?php if ($isSearching): ?>
                <small>(searched across all employees)</small>
            <?php endif; ?>
        </span>
        <div class="page-size-selector">
            <label for="employees-pageSize">Show:</label>
            <select id="employees-pageSize" class="page-size-select" onchange="changeEmployeesPageSize(this.value)">
                <option value="10" <?= $pageSize == 10 ? 'selected' : '' ?>>10</option>
                <option value="20" <?= $pageSize == 20 ? 'selected' : '' ?>>20</option>
                <option value="50" <?= $pageSize == 50 ? 'selected' : '' ?>>50</option>
                <option value="100" <?= $pageSize == 100 ? 'selected' : '' ?>>100</option>
            </select>
        </div>
        <div class="performance-info">
            <span class="performance-badge">Fast Load</span>
        </div>
    </div>

    <div class="pagination-controls">
        <?php if ($page > 1): ?>
            <button class="pagination-btn" onclick="goToEmployeesPage(1)" title="First page">
                <i class="fas fa-angle-double-left"></i>
            </button>
            <button class="pagination-btn" onclick="goToEmployeesPage(<?= $page - 1 ?>)" title="Previous page">
                <i class="fas fa-angle-left"></i>
            </button>
        <?php else: ?>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-double-left"></i>
            </button>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-left"></i>
            </button>
        <?php endif; ?>

        <?php
        // Show page numbers
        $startPage = max(1, $page - 2);
        $endPage = min($totalPages, $page + 2);

        if ($startPage > 1): ?>
            <button class="pagination-btn" onclick="goToEmployeesPage(1)">1</button>
            <?php if ($startPage > 2): ?>
                <span class="pagination-ellipsis">...</span>
            <?php endif; ?>
        <?php endif; ?>

        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
            <button class="pagination-btn <?= $i == $page ? 'active' : '' ?>" onclick="goToEmployeesPage(<?= $i ?>)">
                <?= $i ?>
            </button>
        <?php endfor; ?>

        <?php if ($endPage < $totalPages): ?>
            <?php if ($endPage < $totalPages - 1): ?>
                <span class="pagination-ellipsis">...</span>
            <?php endif; ?>
            <button class="pagination-btn" onclick="goToEmployeesPage(<?= $totalPages ?>)"><?= $totalPages ?></button>
        <?php endif; ?>

        <?php if ($page < $totalPages): ?>
            <button class="pagination-btn" onclick="goToEmployeesPage(<?= $page + 1 ?>)" title="Next page">
                <i class="fas fa-angle-right"></i>
            </button>
            <button class="pagination-btn" onclick="goToEmployeesPage(<?= $totalPages ?>)" title="Last page">
                <i class="fas fa-angle-double-right"></i>
            </button>
        <?php else: ?>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-right"></i>
            </button>
            <button class="pagination-btn" disabled>
                <i class="fas fa-angle-double-right"></i>
            </button>
        <?php endif; ?>
    </div>
</div>

<style>
    .employees-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
        gap: 15px;
    }

    .header-left {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .header-controls {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .view-toggle-label {
        font-size: 14px;
        color: var(--text-secondary);
        margin-right: 8px;
    }

    @media (max-width: 768px) {
        .employees-header {
            flex-direction: column;
            align-items: stretch;
        }

        .header-controls {
            justify-content: center;
        }
    }
</style>

<script>
    // View switching functionality for employees
    function switchEmployeeView(viewType) {
        const cardsView = document.getElementById('employees-cards-view');
        const tableView = document.getElementById('employees-table-view');
        const toggleButtons = document.querySelectorAll('.view-toggle-btn');

        // Update button states
        toggleButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.view === viewType) {
                btn.classList.add('active');
            }
        });

        // Show/hide views
        if (viewType === 'cards') {
            cardsView.style.display = 'grid';
            tableView.style.display = 'none';
        } else {
            cardsView.style.display = 'none';
            tableView.style.display = 'block';
        }

        // Save preference
        localStorage.setItem('employees-view', viewType);
    }

    // Server-side search functionality for employees
    function handleEmployeesSearchKeypress(event) {
        if (event.key === 'Enter') {
            performEmployeesServerSearch();
        }
    }

    function performEmployeesServerSearch() {
        const searchTerm = document.getElementById('employees-search').value.trim();
        const url = new URL(window.location);

        if (searchTerm) {
            url.searchParams.set('search', searchTerm);
        } else {
            url.searchParams.delete('search');
        }
        url.searchParams.set('page', 1); // Reset to first page

        window.location.href = url.toString();
    }

    function clearEmployeesServerSearch() {
        const url = new URL(window.location);
        url.searchParams.delete('search');
        url.searchParams.set('page', 1);
        window.location.href = url.toString();
    }

    // Legacy functions (for backward compatibility)
    function searchEmployees() {
        performEmployeesServerSearch();
    }

    function clearSearch(type) {
        if (type === 'employees') {
            clearEmployeesServerSearch();
        }
    }

    function applyEmployeeFilters() {
        const searchTerm = document.getElementById('employees-search').value.toLowerCase().trim();

        let visibleCount = 0;

        // Filter table rows
        const rows = document.querySelectorAll('#employees-table-view tbody tr');
        rows.forEach(row => {
            let showRow = true;

            if (searchTerm) {
                const searchableText = Array.from(row.cells).map(cell => cell.textContent.toLowerCase()).join(' ');
                if (!searchableText.includes(searchTerm)) {
                    showRow = false;
                }
            }

            row.style.display = showRow ? '' : 'none';
            if (showRow) visibleCount++;
        });

        // Filter cards
        const cards = document.querySelectorAll('#employees-cards-view .data-card');
        let visibleCardCount = 0;

        cards.forEach(card => {
            let showCard = true;

            if (searchTerm) {
                const employeeName = card.querySelector('.card-title')?.textContent.toLowerCase() || '';
                const employeePosition = card.querySelector('.card-subtitle')?.textContent.toLowerCase() || '';
                const cardText = card.textContent.toLowerCase();

                if (!employeeName.includes(searchTerm) &&
                    !employeePosition.includes(searchTerm) &&
                    !cardText.includes(searchTerm)) {
                    showCard = false;
                }
            }

            card.style.display = showCard ? 'block' : 'none';
            if (showCard) visibleCardCount++;
        });

        // Update search results info
        updateEmployeeSearchResults(searchTerm, Math.max(visibleCount, visibleCardCount), cards.length);
    }

    function updateEmployeeSearchResults(searchTerm, visibleCount, totalCount) {
        const resultsInfo = document.getElementById('employees-search-results-info');
        const noResults = document.getElementById('employees-no-results');
        const resultsCount = resultsInfo?.querySelector('.search-results-count');

        if (searchTerm) {
            if (visibleCount === 0) {
                if (resultsInfo) resultsInfo.style.display = 'none';
                if (noResults) noResults.style.display = 'block';
            } else {
                if (resultsInfo) resultsInfo.style.display = 'flex';
                if (noResults) noResults.style.display = 'none';
                if (resultsCount) resultsCount.textContent = `Showing ${visibleCount} of ${totalCount} employees`;
            }
        } else {
            if (resultsInfo) resultsInfo.style.display = 'none';
            if (noResults) noResults.style.display = 'none';
        }
    }

    // Pagination functions for employees
    function goToEmployeesPage(page) {
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        window.location.href = url.toString();
    }

    function changeEmployeesPageSize(pageSize) {
        const url = new URL(window.location);
        url.searchParams.set('pageSize', pageSize);
        url.searchParams.set('page', 1); // Reset to first page
        window.location.href = url.toString();
    }

    // Load saved view preference
    document.addEventListener('DOMContentLoaded', function() {
        const savedView = localStorage.getItem('employees-view') || 'cards';
        switchEmployeeView(savedView);
    });
</script>