<?php
require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
require_once '../../../includes/admin_functions.php';

$employeeId = $_GET['id'] ?? '';
$employee = null;
$employeeServices = [];

// Debug logging
error_log("Edit Employee Modal - Employee ID: " . $employeeId);

if ($employeeId) {
    $employee = getEmployeeById($employeeId);
    if (!$employee) {
        error_log("Employee not found for edit: " . $employeeId);
        // Try to debug what employees exist
        $allEmployees = getEmployees();
        error_log("Available employees for edit: " . json_encode(array_keys($allEmployees)));
    } else {
        $employeeServices = getEmployeeServices($employeeId);
    }
}

$services = getServices();
?>

<form method="post" action="actions/save_employee.php" data-validate>
    <input type="hidden" name="employee_id" value="<?= htmlspecialchars($employeeId) ?>">

    <div class="form-row">
        <div class="form-group">
            <label>Employee Name *</label>
            <input type="text" name="employee_name" class="form-control" value="<?= htmlspecialchars($employee['name'] ?? '') ?>" required>
        </div>
        <div class="form-group">
            <label>Email</label>
            <input type="email" name="employee_email" class="form-control" value="<?= htmlspecialchars($employee['email'] ?? '') ?>">
        </div>
    </div>

    <div class="form-group">
        <label>Phone</label>
        <input type="text" name="employee_phone" class="form-control" value="<?= htmlspecialchars($employee['phone'] ?? '') ?>">
    </div>

    <div class="form-group">
        <label>Services *</label>
        <div class="services-selection">
            <?php foreach ($services as $serviceId => $service): ?>
                <div class="checkbox-item">
                    <input type="checkbox" name="service_ids[]" value="<?= htmlspecialchars($serviceId) ?>"
                        id="service_<?= htmlspecialchars($serviceId) ?>"
                        <?= isset($employeeServices[$serviceId]) ? 'checked' : '' ?>>
                    <label for="service_<?= htmlspecialchars($serviceId) ?>">
                        <?= htmlspecialchars($service['name']) ?> (<?= $service['duration'] ?>min - €<?= $service['price'] ?>)
                    </label>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <div class="form-group">
        <label>Working Hours</label>
        <div class="working-hours-section">
            <?php
            $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
            $workingHours = $employee['working_hours'] ?? [];

            foreach ($days as $day):
                $dayHours = $workingHours[$day] ?? [];
                $isWorkingDay = !empty($dayHours);
            ?>
                <div class="day-hours">
                    <h5><?= $day ?></h5>
                    <div class="day-periods" id="periods_<?= strtolower($day) ?>">
                        <div class="period-row">
                            <input type="checkbox" name="working_days[]" value="<?= $day ?>"
                                id="day_<?= strtolower($day) ?>" <?= $isWorkingDay ? 'checked' : '' ?>>
                            <label for="day_<?= strtolower($day) ?>">Working day</label>
                        </div>

                        <?php if ($isWorkingDay): ?>
                            <?php foreach ($dayHours as $index => $period): ?>
                                <div class="period-row">
                                    <input type="time" name="<?= $day ?>[<?= $index ?>][start]"
                                        class="form-control time-input" value="<?= $period['start'] ?>">
                                    <span>to</span>
                                    <input type="time" name="<?= $day ?>[<?= $index ?>][end]"
                                        class="form-control time-input" value="<?= $period['end'] ?>">
                                    <?php if ($index > 0): ?>
                                        <button type="button" class="btn btn-sm btn-danger remove-period" title="Remove period">−</button>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="period-row" style="display: none;">
                                <input type="time" name="<?= $day ?>[0][start]" class="form-control time-input" value="09:00">
                                <span>to</span>
                                <input type="time" name="<?= $day ?>[0][end]" class="form-control time-input" value="17:00">
                            </div>
                        <?php endif; ?>

                        <button type="button" class="btn btn-sm btn-success add-period" data-day="<?= $day ?>" title="Add period">+</button>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <button type="submit" class="btn btn-primary" data-original-text="Save">
        <i class="fas fa-save"></i> Save Employee
    </button>
</form>

<style>
    .services-selection {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #ddd;
        padding: 10px;
        border-radius: 4px;
    }

    .checkbox-item {
        margin-bottom: 8px;
    }

    .checkbox-item input[type="checkbox"] {
        margin-right: 8px;
    }

    .working-hours-section {
        border: 1px solid #ddd;
        padding: 15px;
        border-radius: 4px;
        max-height: 400px;
        overflow-y: auto;
    }

    .day-hours {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .day-hours h5 {
        margin-bottom: 10px;
        color: #333;
    }

    .period-row {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;
    }

    .period-row:first-child {
        margin-bottom: 15px;
    }

    .time-input {
        width: 100px;
    }

    .add-period,
    .remove-period {
        margin-left: 10px;
        min-width: 30px;
        font-weight: bold;
        font-size: 16px;
        line-height: 1;
        padding: 4px 8px;
    }

    .add-period {
        background-color: #28a745;
        border-color: #28a745;
    }

    .add-period:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }

    .remove-period {
        background-color: #dc3545;
        border-color: #dc3545;
    }

    .remove-period:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }
</style>

<script>
    // Working Hours Management - Robust Implementation
    function initializeWorkingHours() {
        console.log('Initializing working hours functionality...');

        // Handle working day checkboxes with event delegation
        document.addEventListener('change', function(e) {
            if (e.target.matches('input[name="working_days[]"]')) {
                const checkbox = e.target;
                const day = checkbox.value;
                const periodsDiv = document.getElementById('periods_' + day.toLowerCase());

                if (periodsDiv) {
                    updateDayState(periodsDiv, checkbox);
                }
            }
        });

        // Handle add period buttons with event delegation
        document.addEventListener('click', function(e) {
            if (e.target.matches('.add-period')) {
                e.preventDefault();
                const button = e.target;
                const day = button.dataset.day;
                const periodsDiv = document.getElementById('periods_' + day.toLowerCase());
                const checkbox = document.getElementById('day_' + day.toLowerCase());

                console.log('Add period clicked for day:', day);

                // Only add period if day is checked as working day
                if (!checkbox || !checkbox.checked) {
                    console.log('Day not checked, skipping add period');
                    return;
                }

                addPeriod(periodsDiv, day, button);
            }
        });

        // Handle remove period buttons with event delegation
        document.addEventListener('click', function(e) {
            if (e.target.matches('.remove-period')) {
                e.preventDefault();
                console.log('Remove period clicked');
                const periodRow = e.target.closest('.period-row');
                if (periodRow) {
                    periodRow.remove();
                }
            }
        });

        // Initialize existing checkboxes
        document.querySelectorAll('input[name="working_days[]"]').forEach(function(checkbox) {
            const day = checkbox.value;
            const periodsDiv = document.getElementById('periods_' + day.toLowerCase());
            if (periodsDiv) {
                updateDayState(periodsDiv, checkbox);
            }
        });
    }

    function updateDayState(periodsDiv, checkbox) {
        const timeInputs = periodsDiv.querySelectorAll('.time-input');
        const periodRows = periodsDiv.querySelectorAll('.period-row:not(:first-child)');
        const addButton = periodsDiv.querySelector('.add-period');

        console.log('Updating day state for', checkbox.value, 'checked:', checkbox.checked);

        periodRows.forEach(function(row) {
            row.style.display = checkbox.checked ? 'flex' : 'none';
        });

        timeInputs.forEach(function(input) {
            input.disabled = !checkbox.checked;
        });

        if (addButton) {
            addButton.style.display = checkbox.checked ? 'inline-block' : 'none';
        }
    }

    function addPeriod(periodsDiv, day, addButton) {
        // Count existing time period rows (excluding the checkbox row)
        const existingTimeRows = periodsDiv.querySelectorAll('.period-row:not(:first-child)');
        const newIndex = existingTimeRows.length;

        console.log('Adding period with index:', newIndex);

        const newPeriodRow = document.createElement('div');
        newPeriodRow.className = 'period-row';
        newPeriodRow.style.display = 'flex';
        newPeriodRow.innerHTML = `
        <input type="time" name="${day}[${newIndex}][start]" class="form-control time-input" value="09:00">
        <span>to</span>
        <input type="time" name="${day}[${newIndex}][end]" class="form-control time-input" value="17:00">
        <button type="button" class="btn btn-sm btn-danger remove-period" title="Remove period">−</button>
    `;

        // Insert before the add button
        periodsDiv.insertBefore(newPeriodRow, addButton);
        console.log('Period added successfully');
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeWorkingHours);
    } else {
        initializeWorkingHours();
    }

    // Initialize when modal content is loaded (for custom admin modal system)
    document.addEventListener('modalContentLoaded', function(e) {
        console.log('Modal content loaded, reinitializing working hours...');
        setTimeout(initializeWorkingHours, 100);
    });

    // Also try to initialize immediately when this script runs
    console.log('Edit employee modal script loaded, initializing...');
    setTimeout(initializeWorkingHours, 50);
</script>