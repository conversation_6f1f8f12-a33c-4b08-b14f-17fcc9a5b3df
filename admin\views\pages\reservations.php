<?php

// List all reservations, optionally filter by date or week
$date = $_GET['date'] ?? null;
$view = $_GET['view'] ?? null;

// Pagination settings
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$pageSize = isset($_GET['pageSize']) ? max(10, min(100, intval($_GET['pageSize']))) : 20;
$offset = ($page - 1) * $pageSize;

// Search functionality
$searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
$isSearching = !empty($searchTerm);

if ($view === 'week') {
    $start = date('Y-m-d');
    $end = date('Y-m-d', strtotime('+6 days'));
    $reservations = $reservationHandler->getReservationsByDateRange($start, $end);
    $totalReservations = count($reservations);
    $totalPages = 1; // Week view doesn't need pagination
} elseif ($date) {
    $reservations = $reservationHandler->getReservationsByDate($date);
    $totalReservations = count($reservations);
    $totalPages = 1; // Single day view doesn't need pagination
} elseif ($isSearching) {
    // Search across all reservations
    $totalReservations = $reservationHandler->getSearchReservationCount($searchTerm);
    $totalPages = ceil($totalReservations / $pageSize);
    $reservations = $reservationHandler->searchReservations($searchTerm, $pageSize, $offset);
} else {
    // Get total count for pagination
    $totalReservations = $reservationHandler->getReservationCount();
    $totalPages = ceil($totalReservations / $pageSize);

    // Get paginated reservations
    $reservations = $reservationHandler->getReservationsPaginated($pageSize, $offset);
}

$services = getServices();

// Get all customers and create a lookup array
$customers = [];
$allCustomers = $customerHandler->getAllCustomers();
foreach ($allCustomers as $customer) {
    $customers[$customer['id']] = $customer;
}

// Get all employees and create a lookup array
require_once '../includes/admin_functions.php';
$employees = [];
$allEmployees = getEmployees();
foreach ($allEmployees as $employee) {
    $employees[$employee['id']] = $employee;
}
?>
<div>
    <h2>Reservations</h2>
    <div class="admin-header">
        <div class="header-left">
            <button class="btn btn-primary" onclick="openModal('Add Reservation', 'views/modals/add_reservation.php')">
                <i class="fas fa-plus"></i> Add Reservation
            </button>
            <div class="filters">
                <select id="employee-filter" class="form-select" onchange="filterReservations()">
                    <option value="">All Employees</option>
                    <?php foreach ($employees as $empId => $employee): ?>
                        <option value="<?= htmlspecialchars($empId) ?>">
                            <?= htmlspecialchars($employee['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>

                <select id="status-filter" class="form-select" onchange="filterReservations()">
                    <option value="">All Statuses</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
        </div>

        <div class="header-center">
            <div class="view-toggle">
                <span class="view-toggle-label">View:</span>
                <div class="view-toggle-buttons">
                    <button class="view-toggle-btn active" onclick="switchView('cards')" data-view="cards">
                        <i class="fas fa-th-large"></i> Cards
                    </button>
                    <button class="view-toggle-btn" onclick="switchView('table')" data-view="table">
                        <i class="fas fa-table"></i> Table
                    </button>
                </div>
            </div>
        </div>

        <div class="header-right">
            <div class="search-container">
                <i class="search-icon fas fa-search"></i>
                <input type="text"
                    id="reservations-search"
                    class="search-input"
                    placeholder="Search ALL reservations by customer, service, date, or status..."
                    value="<?= htmlspecialchars($searchTerm) ?>"
                    onkeypress="handleReservationsSearchKeypress(event)">
                <button class="search-clear" onclick="clearReservationsServerSearch()" title="Clear search" <?= $isSearching ? '' : 'style="opacity: 0;"' ?>>
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Search Results Info -->
    <?php if ($isSearching): ?>
        <div class="search-results-info" style="display: flex;">
            <span class="search-results-count">
                Found <?= $totalReservations ?> reservation<?= $totalReservations !== 1 ? 's' : '' ?> matching "<?= htmlspecialchars($searchTerm) ?>"
                <?php if ($totalPages > 1): ?>
                    (showing page <?= $page ?> of <?= $totalPages ?>)
                <?php endif; ?>
            </span>
            <button class="search-clear-all" onclick="clearReservationsServerSearch()">Clear search</button>
        </div>
    <?php endif; ?>

    <!-- No Results Message -->
    <?php if ($isSearching && $totalReservations === 0): ?>
        <div class="no-results" style="display: block;">
            <div class="no-results-icon">
                <i class="fas fa-search"></i>
            </div>
            <h3>No reservations found</h3>
            <p>No reservations match "<?= htmlspecialchars($searchTerm) ?>". Try different search terms.</p>
        </div>
    <?php endif; ?>

    <!-- Card View -->
    <div id="cards-view" class="card-grid">
        <?php foreach ($reservations as $r): ?>
            <?php
            // Get customer name from the lookup array
            $customerName = '';
            if (isset($r['customer_id']) && isset($customers[$r['customer_id']])) {
                $customerName = $customers[$r['customer_id']]['name'];
            } else {
                $customerName = 'Unknown Customer';
            }

            // Get employee name from the lookup array
            $employeeName = '';
            $isAutoAssigned = false;
            if (isset($r['employee_id']) && isset($employees[$r['employee_id']])) {
                $employeeName = $employees[$r['employee_id']]['name'];
                // Check if this service has auto-assignment enabled (allow_employee_selection = false)
                $serviceDetails = $services[$r['service']] ?? null;
                $isAutoAssigned = $serviceDetails && !($serviceDetails['allow_employee_selection'] ?? false);
            } else {
                $employeeName = 'Unassigned';
            }
            ?>
            <div class="data-card reservation-card <?= $r['status'] ?>"
                data-employee="<?= htmlspecialchars($r['employee_id'] ?? '') ?>"
                data-status="<?= htmlspecialchars($r['status']) ?>">

                <div class="card-header">
                    <div>
                        <h3 class="card-title"><?= htmlspecialchars($customerName) ?></h3>
                        <p class="card-subtitle"><?= htmlspecialchars($services[$r['service']]['name'] ?? $r['service']) ?></p>
                    </div>
                    <div class="card-id">#<?= htmlspecialchars($r['id']) ?></div>
                </div>

                <div class="card-status">
                    <span class="status-indicator <?= $r['status'] ?>">
                        <?php if ($r['status'] === 'confirmed'): ?>
                            <i class="fas fa-check-circle"></i>
                        <?php elseif ($r['status'] === 'completed'): ?>
                            <i class="fas fa-flag-checkered"></i>
                        <?php elseif ($r['status'] === 'cancelled'): ?>
                            <i class="fas fa-times-circle"></i>
                        <?php else: ?>
                            <i class="fas fa-clock"></i>
                        <?php endif; ?>
                        <?= ucfirst($r['status']) ?>
                    </span>
                </div>

                <div class="card-body">
                    <div class="card-field">
                        <i class="card-field-icon fas fa-calendar"></i>
                        <span class="card-field-label">Date:</span>
                        <span class="card-field-value"><?= htmlspecialchars(date('M j, Y', strtotime($r['date']))) ?></span>
                    </div>

                    <div class="card-field">
                        <i class="card-field-icon fas fa-clock"></i>
                        <span class="card-field-label">Time:</span>
                        <span class="card-field-value"><?= htmlspecialchars(format_time_range($r['time'], (int)$r['duration'])) ?></span>
                    </div>

                    <div class="card-field">
                        <i class="card-field-icon fas fa-user-tie"></i>
                        <span class="card-field-label">Employee:</span>
                        <span class="card-field-value">
                            <?php if ($isAutoAssigned): ?>
                                <i class="fas fa-magic auto-assign-icon" title="Auto-assigned"></i>
                            <?php endif; ?>
                            <?= htmlspecialchars($employeeName) ?>
                        </span>
                    </div>

                    <div class="card-badges">
                        <span class="card-badge info">
                            <i class="fas fa-euro-sign"></i>
                            €<?= number_format($services[$r['service']]['price'] ?? 0, 2) ?>
                        </span>
                        <span class="card-badge primary">
                            <i class="fas fa-clock"></i>
                            <?= $services[$r['service']]['duration'] ?? 60 ?> min
                        </span>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="card-actions">
                        <button class="btn btn-sm btn-info" onclick="viewReservation('<?= $r['id'] ?>')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <?php if ($r['status'] === 'confirmed'): ?>
                            <button class="btn btn-sm btn-secondary" onclick="editReservation('<?= $r['id'] ?>')" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-success" onclick="completeReservation('<?= $r['id'] ?>')" title="Complete">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="cancelReservation('<?= $r['id'] ?>')" title="Cancel">
                                <i class="fas fa-times"></i>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Table View (Hidden by default) -->
    <div id="table-view" style="display: none;">
        <table class="table" data-sortable>
            <thead>
                <tr>
                    <th data-sort>ID</th>
                    <th data-sort>Date</th>
                    <th data-sort>Time</th>
                    <th data-sort>Customer</th>
                    <th data-sort>Service</th>
                    <th data-sort>Employee</th>
                    <th data-sort>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($reservations as $r): ?>
                    <?php
                    // Get customer name from the lookup array
                    $customerName = '';
                    if (isset($r['customer_id']) && isset($customers[$r['customer_id']])) {
                        $customerName = $customers[$r['customer_id']]['name'];
                    } elseif (isset($r['name']) && !empty($r['name'])) {
                        // Fallback to name field if it exists in reservation
                        $customerName = $r['name'];
                    } elseif (isset($r['email']) && !empty($r['email'])) {
                        // Last fallback to email if name is not available
                        $customerName = $r['email'];
                    } else {
                        $customerName = 'Unknown Customer';
                    }

                    // Get employee name and check auto-assignment
                    $employeeName = 'Not Assigned';
                    $isAutoAssigned = false;
                    if (!empty($r['employee_id']) && isset($employees[$r['employee_id']])) {
                        $employeeName = $employees[$r['employee_id']]['name'];
                        // Check if this service has auto-assignment enabled
                        $serviceDetails = $services[$r['service']] ?? null;
                        $isAutoAssigned = $serviceDetails && !($serviceDetails['allow_employee_selection'] ?? false);
                    }
                    ?>
                    <tr>
                        <td><?= htmlspecialchars($r['id']) ?></td>
                        <td><?= htmlspecialchars($r['date']) ?></td>
                        <td><?= htmlspecialchars(format_time_range($r['time'], (int)$r['duration'])) ?></td>
                        <td><?= htmlspecialchars($customerName) ?></td>
                        <td><?= htmlspecialchars($services[$r['service']]['name'] ?? $r['service']) ?></td>
                        <td>
                            <?php if ($isAutoAssigned): ?>
                                <i class="fas fa-magic auto-assign-icon" title="Auto-assigned"></i>
                            <?php endif; ?>
                            <?= htmlspecialchars($employeeName) ?>
                        </td>
                        <td>
                            <span class="status-badge <?= $r['status'] ?>">
                                <?= ucfirst($r['status']) ?>
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-info" onclick="viewReservation('<?= $r['id'] ?>')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <?php if ($r['status'] === 'confirmed'): ?>
                                <button class="btn btn-sm btn-secondary" onclick="editReservation('<?= $r['id'] ?>')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="cancelReservation('<?= $r['id'] ?>')">
                                    <i class="fas fa-times"></i>
                                </button>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination Controls (only show for main reservations view, not date/week filters) -->
<?php if (!$date && $view !== 'week'): ?>
    <div class="pagination-container">
        <div class="pagination-info">
            <span>Showing <?= min($offset + 1, $totalReservations) ?>-<?= min($offset + $pageSize, $totalReservations) ?> of <?= $totalReservations ?> reservations</span>
            <div class="page-size-selector">
                <label for="reservations-pageSize">Show:</label>
                <select id="reservations-pageSize" class="page-size-select" onchange="changeReservationsPageSize(this.value)">
                    <option value="10" <?= $pageSize == 10 ? 'selected' : '' ?>>10</option>
                    <option value="20" <?= $pageSize == 20 ? 'selected' : '' ?>>20</option>
                    <option value="50" <?= $pageSize == 50 ? 'selected' : '' ?>>50</option>
                    <option value="100" <?= $pageSize == 100 ? 'selected' : '' ?>>100</option>
                </select>
            </div>
            <div class="performance-info">
                <span class="performance-badge">Fast Load</span>
            </div>
        </div>

        <div class="pagination-controls">
            <?php if ($page > 1): ?>
                <button class="pagination-btn" onclick="goToReservationsPage(1)" title="First page">
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button class="pagination-btn" onclick="goToReservationsPage(<?= $page - 1 ?>)" title="Previous page">
                    <i class="fas fa-angle-left"></i>
                </button>
            <?php else: ?>
                <button class="pagination-btn" disabled>
                    <i class="fas fa-angle-double-left"></i>
                </button>
                <button class="pagination-btn" disabled>
                    <i class="fas fa-angle-left"></i>
                </button>
            <?php endif; ?>

            <?php
            // Show page numbers
            $startPage = max(1, $page - 2);
            $endPage = min($totalPages, $page + 2);

            if ($startPage > 1): ?>
                <button class="pagination-btn" onclick="goToReservationsPage(1)">1</button>
                <?php if ($startPage > 2): ?>
                    <span class="pagination-ellipsis">...</span>
                <?php endif; ?>
            <?php endif; ?>

            <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                <button class="pagination-btn <?= $i == $page ? 'active' : '' ?>" onclick="goToReservationsPage(<?= $i ?>)">
                    <?= $i ?>
                </button>
            <?php endfor; ?>

            <?php if ($endPage < $totalPages): ?>
                <?php if ($endPage < $totalPages - 1): ?>
                    <span class="pagination-ellipsis">...</span>
                <?php endif; ?>
                <button class="pagination-btn" onclick="goToReservationsPage(<?= $totalPages ?>)"><?= $totalPages ?></button>
            <?php endif; ?>

            <?php if ($page < $totalPages): ?>
                <button class="pagination-btn" onclick="goToReservationsPage(<?= $page + 1 ?>)" title="Next page">
                    <i class="fas fa-angle-right"></i>
                </button>
                <button class="pagination-btn" onclick="goToReservationsPage(<?= $totalPages ?>)" title="Last page">
                    <i class="fas fa-angle-double-right"></i>
                </button>
            <?php else: ?>
                <button class="pagination-btn" disabled>
                    <i class="fas fa-angle-right"></i>
                </button>
                <button class="pagination-btn" disabled>
                    <i class="fas fa-angle-double-right"></i>
                </button>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>

<style>
    .reservations-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
        gap: 15px;
    }

    .header-left {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .header-controls {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;
    }

    .view-toggle-label {
        font-size: 14px;
        color: var(--text-secondary);
        margin-right: 8px;
    }

    .filters {
        display: flex;
        gap: 10px;
    }

    .filters .form-select {
        min-width: 150px;
    }

    @media (max-width: 768px) {
        .reservations-header {
            flex-direction: column;
            align-items: stretch;
        }

        .header-controls {
            justify-content: space-between;
        }

        .view-toggle {
            order: -1;
        }
    }
</style>

<script>
    // View switching functionality
    function switchView(viewType) {
        const cardsView = document.getElementById('cards-view');
        const tableView = document.getElementById('table-view');
        const toggleButtons = document.querySelectorAll('.view-toggle-btn');

        // Update button states
        toggleButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.view === viewType) {
                btn.classList.add('active');
            }
        });

        // Show/hide views
        if (viewType === 'cards') {
            cardsView.style.display = 'grid';
            tableView.style.display = 'none';
        } else {
            cardsView.style.display = 'none';
            tableView.style.display = 'block';
        }

        // Save preference
        localStorage.setItem('reservations-view', viewType);
    }

    // Load saved view preference
    document.addEventListener('DOMContentLoaded', function() {
        const savedView = localStorage.getItem('reservations-view') || 'cards';
        switchView(savedView);
    });

    // Search functionality
    function searchReservations() {
        applyFilters();
    }

    function clearSearch(type) {
        document.getElementById(`${type}-search`).value = '';
        applyFilters();
    }

    function applyFilters() {
        const searchTerm = document.getElementById('reservations-search').value.toLowerCase().trim();
        const employeeFilter = document.getElementById('employee-filter').value;
        const statusFilter = document.getElementById('status-filter').value;

        let visibleCount = 0;

        // Filter table rows
        const rows = document.querySelectorAll('#table-view tbody tr');
        rows.forEach(row => {
            let showRow = true;

            // Search filter
            if (searchTerm) {
                const searchableText = Array.from(row.cells).map(cell => cell.textContent.toLowerCase()).join(' ');
                if (!searchableText.includes(searchTerm)) {
                    showRow = false;
                }
            }

            // Employee filter
            if (employeeFilter && showRow) {
                const employeeCell = row.cells[5];
                if (employeeCell) {
                    const employeeName = employeeCell.textContent.trim();
                    const selectedEmployee = document.querySelector(`#employee-filter option[value="${employeeFilter}"]`).textContent;
                    if (employeeName !== selectedEmployee) {
                        showRow = false;
                    }
                }
            }

            // Status filter
            if (statusFilter && showRow) {
                const statusCell = row.cells[6];
                if (statusCell) {
                    const statusBadge = statusCell.querySelector('.status-badge');
                    if (statusBadge && !statusBadge.classList.contains(statusFilter)) {
                        showRow = false;
                    }
                }
            }

            row.style.display = showRow ? '' : 'none';
            if (showRow) visibleCount++;
        });

        // Filter cards
        const cards = document.querySelectorAll('#cards-view .data-card');
        let visibleCardCount = 0;

        cards.forEach(card => {
            let showCard = true;

            // Search filter
            if (searchTerm) {
                const customerName = card.querySelector('.card-title')?.textContent.toLowerCase() || '';
                const serviceName = card.querySelector('.card-subtitle')?.textContent.toLowerCase() || '';
                const cardText = card.textContent.toLowerCase();

                if (!customerName.includes(searchTerm) &&
                    !serviceName.includes(searchTerm) &&
                    !cardText.includes(searchTerm)) {
                    showCard = false;
                }
            }

            // Employee filter
            if (employeeFilter && showCard && card.dataset.employee !== employeeFilter) {
                showCard = false;
            }

            // Status filter
            if (statusFilter && showCard && card.dataset.status !== statusFilter) {
                showCard = false;
            }

            card.style.display = showCard ? 'block' : 'none';
            if (showCard) visibleCardCount++;
        });

        // Update search results info
        updateSearchResults(searchTerm, Math.max(visibleCount, visibleCardCount), cards.length);
    }

    function updateSearchResults(searchTerm, visibleCount, totalCount) {
        const resultsInfo = document.getElementById('search-results-info');
        const noResults = document.getElementById('no-results');
        const resultsCount = document.querySelector('.search-results-count');

        if (searchTerm || document.getElementById('employee-filter').value || document.getElementById('status-filter').value) {
            if (visibleCount === 0) {
                resultsInfo.style.display = 'none';
                noResults.style.display = 'block';
            } else {
                resultsInfo.style.display = 'flex';
                noResults.style.display = 'none';
                resultsCount.textContent = `Showing ${visibleCount} of ${totalCount} reservations`;
            }
        } else {
            resultsInfo.style.display = 'none';
            noResults.style.display = 'none';
        }
    }

    function filterReservations() {
        applyFilters();
    }

    // Server-side search functionality for reservations
    function handleReservationsSearchKeypress(event) {
        if (event.key === 'Enter') {
            performReservationsServerSearch();
        }
    }

    function performReservationsServerSearch() {
        const searchTerm = document.getElementById('reservations-search').value.trim();
        const url = new URL(window.location);

        if (searchTerm) {
            url.searchParams.set('search', searchTerm);
        } else {
            url.searchParams.delete('search');
        }
        url.searchParams.set('page', 1); // Reset to first page

        window.location.href = url.toString();
    }

    function clearReservationsServerSearch() {
        const url = new URL(window.location);
        url.searchParams.delete('search');
        url.searchParams.set('page', 1);
        window.location.href = url.toString();
    }

    // Pagination functions for reservations (preserve search terms)
    function goToReservationsPage(page) {
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        // Preserve existing search and pageSize parameters
        window.location.href = url.toString();
    }

    function changeReservationsPageSize(pageSize) {
        const url = new URL(window.location);
        url.searchParams.set('pageSize', pageSize);
        url.searchParams.set('page', 1); // Reset to first page
        // Preserve existing search parameter
        window.location.href = url.toString();
    }

    // Legacy functions (for backward compatibility)
    function searchReservations() {
        performReservationsServerSearch();
    }

    function clearSearch(type) {
        if (type === 'reservations') {
            clearReservationsServerSearch();
        }
    }
</script>