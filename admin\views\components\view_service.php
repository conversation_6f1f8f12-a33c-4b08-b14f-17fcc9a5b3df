<?php
require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
require_once '../../../includes/reservation_handler.php';

$serviceId = $_GET['id'] ?? '';
$services = getServices();
$service = $services[$serviceId] ?? null;

if (!$service) {
    echo '<div class="alert alert-error">Service not found.</div>';
    exit;
}

// Get service statistics
$reservationHandler = new ReservationHandler();
$allReservations = $reservationHandler->getAllReservations();

$serviceStats = [
    'total_bookings' => 0,
    'total_revenue' => 0,
    'completed_bookings' => 0,
    'cancelled_bookings' => 0,
    'upcoming_bookings' => 0,
    'average_rating' => 0,
    'last_booking_date' => null,
    'most_popular_time' => null,
    'busiest_day' => null
];

$timeSlots = [];
$weekDays = [];
$monthlyRevenue = [];

foreach ($allReservations as $reservation) {
    if ($reservation['service'] === $serviceId) {
        $serviceStats['total_bookings']++;

        if ($reservation['status'] === 'completed') {
            $serviceStats['completed_bookings']++;
            $serviceStats['total_revenue'] += $reservation['price'];
        } elseif ($reservation['status'] === 'cancelled') {
            $serviceStats['cancelled_bookings']++;
        } elseif ($reservation['status'] === 'confirmed' && $reservation['date'] >= date('Y-m-d')) {
            $serviceStats['upcoming_bookings']++;
        }

        // Track time slots
        $timeSlot = substr($reservation['time'], 0, 2) . ':00';
        $timeSlots[$timeSlot] = ($timeSlots[$timeSlot] ?? 0) + 1;

        // Track week days
        $dayOfWeek = date('l', strtotime($reservation['date']));
        $weekDays[$dayOfWeek] = ($weekDays[$dayOfWeek] ?? 0) + 1;

        // Track monthly revenue
        $month = date('Y-m', strtotime($reservation['date']));
        if ($reservation['status'] === 'completed') {
            $monthlyRevenue[$month] = ($monthlyRevenue[$month] ?? 0) + $reservation['price'];
        }

        // Update last booking date
        if (!$serviceStats['last_booking_date'] || $reservation['date'] > $serviceStats['last_booking_date']) {
            $serviceStats['last_booking_date'] = $reservation['date'];
        }
    }
}

// Calculate most popular time and day
if (!empty($timeSlots)) {
    arsort($timeSlots);
    $serviceStats['most_popular_time'] = array_key_first($timeSlots);
}

if (!empty($weekDays)) {
    arsort($weekDays);
    $serviceStats['busiest_day'] = array_key_first($weekDays);
}

// Calculate average revenue per booking
$avgRevenuePerBooking = $serviceStats['completed_bookings'] > 0
    ? $serviceStats['total_revenue'] / $serviceStats['completed_bookings']
    : 0;

// Calculate completion rate
$completionRate = $serviceStats['total_bookings'] > 0
    ? round(($serviceStats['completed_bookings'] / $serviceStats['total_bookings']) * 100, 1)
    : 0;

// Get recent bookings for this service
$recentBookings = array_filter($allReservations, function ($reservation) use ($serviceId) {
    return $reservation['service'] === $serviceId;
});

// Sort by date descending and take last 5
usort($recentBookings, function ($a, $b) {
    return strtotime($b['date'] . ' ' . $b['time']) - strtotime($a['date'] . ' ' . $a['time']);
});
$recentBookings = array_slice($recentBookings, 0, 5);
?>

<div class="service-details-enhanced">
    <!-- Service Header -->
    <div class="service-header">
        <div class="service-icon">
            <i class="fas fa-concierge-bell"></i>
        </div>
        <div class="service-info">
            <h3 class="service-name"><?= htmlspecialchars($service['name']) ?></h3>
            <p class="service-id">ID: <?= htmlspecialchars($service['id']) ?></p>
            <div class="service-badges">
                <span class="badge badge-info">
                    <i class="fas fa-clock"></i> <?= $service['duration'] ?> minutes
                </span>
                <span class="badge badge-success">
                    <i class="fas fa-euro-sign"></i> €<?= number_format($service['price'], 2) ?>
                </span>
                <?php if ($serviceStats['total_bookings'] > 0): ?>
                    <span class="badge badge-primary">
                        <i class="fas fa-calendar-check"></i> <?= $serviceStats['total_bookings'] ?> bookings
                    </span>
                <?php endif; ?>
            </div>
        </div>
        <div class="service-actions">
            <button class="btn btn-sm btn-primary" onclick="editService('<?= htmlspecialchars($service['id']) ?>')">
                <i class="fas fa-edit"></i> Edit
            </button>
            <button class="btn btn-sm btn-success" onclick="openModal('Add Reservation', 'views/modals/add_reservation.php?service_id=<?= htmlspecialchars($service['id']) ?>')">
                <i class="fas fa-plus"></i> Book
            </button>
        </div>
    </div>

    <!-- Service Description -->
    <?php if (!empty($service['description'])): ?>
        <div class="service-description">
            <div class="card-header">
                <h4><i class="fas fa-info-circle"></i> Description</h4>
            </div>
            <div class="card-body">
                <p><?= nl2br(htmlspecialchars($service['description'])) ?></p>
            </div>
        </div>
    <?php endif; ?>

    <!-- Service Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?= $serviceStats['total_bookings'] ?></div>
                <div class="stat-label">Total Bookings</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-euro-sign"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">€<?= number_format($serviceStats['total_revenue'], 2) ?></div>
                <div class="stat-label">Total Revenue</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><?= $completionRate ?>%</div>
                <div class="stat-label">Completion Rate</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">€<?= number_format($avgRevenuePerBooking, 2) ?></div>
                <div class="stat-label">Avg per Booking</div>
            </div>
        </div>
    </div>

    <!-- Service Analytics -->
    <div class="analytics-grid">
        <!-- Performance Metrics -->
        <div class="analytics-card">
            <div class="card-header">
                <h4><i class="fas fa-chart-bar"></i> Performance Metrics</h4>
            </div>
            <div class="card-body">
                <div class="metric-item">
                    <span class="metric-label">Completed Bookings:</span>
                    <span class="metric-value"><?= $serviceStats['completed_bookings'] ?></span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Cancelled Bookings:</span>
                    <span class="metric-value"><?= $serviceStats['cancelled_bookings'] ?></span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Upcoming Bookings:</span>
                    <span class="metric-value"><?= $serviceStats['upcoming_bookings'] ?></span>
                </div>
                <?php if ($serviceStats['last_booking_date']): ?>
                    <div class="metric-item">
                        <span class="metric-label">Last Booking:</span>
                        <span class="metric-value"><?= date('M j, Y', strtotime($serviceStats['last_booking_date'])) ?></span>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Popular Times -->
        <div class="analytics-card">
            <div class="card-header">
                <h4><i class="fas fa-clock"></i> Booking Patterns</h4>
            </div>
            <div class="card-body">
                <?php if ($serviceStats['most_popular_time']): ?>
                    <div class="metric-item">
                        <span class="metric-label">Most Popular Time:</span>
                        <span class="metric-value"><?= $serviceStats['most_popular_time'] ?></span>
                    </div>
                <?php endif; ?>
                <?php if ($serviceStats['busiest_day']): ?>
                    <div class="metric-item">
                        <span class="metric-label">Busiest Day:</span>
                        <span class="metric-value"><?= $serviceStats['busiest_day'] ?></span>
                    </div>
                <?php endif; ?>
                <div class="metric-item">
                    <span class="metric-label">Duration:</span>
                    <span class="metric-value"><?= $service['duration'] ?> minutes</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Price per Hour:</span>
                    <span class="metric-value">€<?= number_format(($service['price'] / $service['duration']) * 60, 2) ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Bookings -->
    <?php if (!empty($recentBookings)): ?>
        <div class="recent-bookings">
            <div class="card-header">
                <h4><i class="fas fa-history"></i> Recent Bookings</h4>
            </div>
            <div class="card-body">
                <div class="bookings-list">
                    <?php foreach ($recentBookings as $booking): ?>
                        <div class="booking-item">
                            <div class="booking-info">
                                <div class="booking-date">
                                    <i class="fas fa-calendar"></i>
                                    <?= date('M j, Y', strtotime($booking['date'])) ?>
                                </div>
                                <div class="booking-time">
                                    <i class="fas fa-clock"></i>
                                    <?= format_time_range($booking['time'], $booking['duration']) ?>
                                </div>
                                <div class="booking-customer">
                                    <i class="fas fa-user"></i>
                                    <?= htmlspecialchars($booking['customer_name'] ?? 'Unknown') ?>
                                </div>
                            </div>
                            <div class="booking-status">
                                <span class="status-badge status-<?= strtolower($booking['status']) ?>">
                                    <?= ucfirst($booking['status']) ?>
                                </span>
                                <span class="booking-price">€<?= number_format($booking['price'], 2) ?></span>
                            </div>
                            <div class="booking-actions">
                                <button class="btn btn-xs btn-info" onclick="viewReservation('<?= htmlspecialchars($booking['id']) ?>')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Quick Actions -->
    <div class="service-quick-actions">
        <button class="btn btn-primary" onclick="editService('<?= htmlspecialchars($service['id']) ?>')">
            <i class="fas fa-edit"></i> Edit Service
        </button>
        <button class="btn btn-success" onclick="openModal('Add Reservation', 'views/modals/add_reservation.php?service_id=<?= htmlspecialchars($service['id']) ?>')">
            <i class="fas fa-plus"></i> New Booking
        </button>
        <button class="btn btn-info" onclick="window.print()">
            <i class="fas fa-print"></i> Print Report
        </button>
        <?php if ($serviceStats['total_bookings'] === 0): ?>
            <button class="btn btn-danger" onclick="deleteService('<?= htmlspecialchars($service['id']) ?>')">
                <i class="fas fa-trash"></i> Delete Service
            </button>
        <?php endif; ?>
    </div>
</div>