<?php
require_once '../includes/email_templates/email_template_creator.php';
require_once '../includes/email_templates/email_template_selector.php';

$templateCreator = new EmailTemplateCreator();
$templateSelector = new EmailTemplateSelector();

// Get available templates and languages
$availableTemplates = $templateSelector->getAvailableTemplates();
$availableLanguages = $templateSelector->getAvailableLanguages();

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'regenerate_templates':
                // Regenerate all templates
                require_once '../includes/email_templates/generate_templates.php';
                $generator = new EmailTemplateGenerator();
                ob_start();
                $generator->generateAllTemplates();
                $output = ob_get_clean();
                $successMessage = "All email templates regenerated successfully!";
                break;

            case 'update_texts':
                // Update email texts (this would need to be implemented)
                $successMessage = "Email texts updated successfully!";
                break;
        }
    }
}

// Get current email texts for editing
$emailTexts = $templateCreator->getEmailTexts();
?>

<div class="email-templates-container">
    <div class="page-header">
        <h1><i class="fas fa-envelope"></i> Email Template Management</h1>
        <p>Manage multilingual email templates and customize email content</p>

        <div class="header-actions">
            <button type="button" class="btn btn-info" onclick="previewTemplate()">
                <i class="fas fa-eye"></i> Preview Templates
            </button>
            <button type="button" class="btn btn-warning" onclick="regenerateTemplates()">
                <i class="fas fa-sync"></i> Regenerate All Templates
            </button>
            <button type="button" class="btn btn-success" onclick="saveEmailTexts()">
                <i class="fas fa-save"></i> Save Changes
            </button>
        </div>
    </div>

    <?php if (isset($successMessage)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> <?= htmlspecialchars($successMessage) ?>
        </div>
    <?php endif; ?>

    <div class="email-management-layout">
        <!-- Template Status Sidebar -->
        <div class="template-sidebar">
            <h3>Template Status</h3>

            <div class="template-status-section">
                <h4>Available Languages</h4>
                <div class="language-list">
                    <?php foreach ($availableLanguages as $lang): ?>
                        <div class="language-item">
                            <span class="language-code"><?= strtoupper($lang) ?></span>
                            <span class="language-name">
                                <?= $lang === 'en' ? 'English' : 'Ελληνικά' ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="template-status-section">
                <h4>Template Files</h4>
                <div class="template-list">
                    <?php foreach ($availableTemplates as $template): ?>
                        <div class="template-item">
                            <div class="template-name"><?= ucfirst(str_replace('_', ' ', $template)) ?></div>
                            <div class="template-files">
                                <?php foreach ($availableLanguages as $lang): ?>
                                    <?php $exists = $templateSelector->templateExists($template, $lang); ?>
                                    <span class="file-status <?= $exists ? 'exists' : 'missing' ?>">
                                        <?= strtoupper($lang) ?>
                                        <?= $exists ? '✓' : '✗' ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Email Text Editor -->
        <div class="email-editor-main">
            <h3>Email Content Management</h3>
            <p>Edit email texts for both languages side by side. Changes are grouped by email type.</p>

            <form id="email-texts-form" class="email-text-form">
                <?php
                // Group email texts by template type
                $emailGroups = [
                    'Welcome Email' => ['welcome_title', 'welcome_header', 'welcome_greeting', 'welcome_message', 'welcome_access_title', 'welcome_access_desc', 'welcome_access_note', 'welcome_final'],
                    'Confirmation Email' => ['confirmation_title', 'confirmation_header', 'confirmation_greeting', 'confirmation_message', 'confirmation_details_title', 'confirmation_thanks'],
                    'Cancellation Email' => ['cancellation_title', 'cancellation_header', 'cancellation_greeting', 'cancellation_message', 'cancellation_details_title', 'cancellation_thanks'],
                    'Reminder Email' => ['reminder_title', 'reminder_header', 'reminder_greeting', 'reminder_message', 'reminder_countdown_title', 'reminder_countdown_text', 'reminder_details_title', 'reminder_thanks'],
                    'Verification Email' => ['verification_title', 'verification_header', 'verification_main_title', 'verification_desc', 'verification_expiry'],
                    'Common Fields' => ['field_id', 'field_service', 'field_date', 'field_time', 'field_price', 'field_duration', 'field_name', 'field_email', 'field_mobile', 'field_registered'],
                    'Admin Emails' => ['admin_new_reservation_title', 'admin_new_reservation_header', 'admin_customer_section', 'admin_reservation_section', 'admin_cancellation_title', 'admin_cancellation_header', 'admin_cancelled_section', 'admin_new_customer_title', 'admin_new_customer_header', 'admin_customer_details_section', 'admin_customer_deletion_title', 'admin_customer_deletion_header', 'admin_deleted_customer_section'],
                    'Footer' => ['footer_text']
                ];

                foreach ($emailGroups as $groupName => $keys): ?>
                    <div class="email-group">
                        <h4 class="group-title"><?= $groupName ?></h4>

                        <?php foreach ($keys as $key): ?>
                            <?php if (isset($emailTexts['en'][$key]) && isset($emailTexts['el'][$key])): ?>
                                <div class="form-row">
                                    <div class="field-label">
                                        <label><?= ucfirst(str_replace('_', ' ', $key)) ?></label>
                                        <div class="field-key">Key: <?= $key ?></div>
                                    </div>

                                    <div class="language-fields">
                                        <div class="language-field">
                                            <label class="language-label">🇬🇧 English</label>
                                            <?php if (strlen($emailTexts['en'][$key]) > 100): ?>
                                                <textarea name="en[<?= $key ?>]" class="form-control" rows="3" placeholder="English text..."><?= htmlspecialchars($emailTexts['en'][$key]) ?></textarea>
                                            <?php else: ?>
                                                <input type="text" name="en[<?= $key ?>]" class="form-control" value="<?= htmlspecialchars($emailTexts['en'][$key]) ?>" placeholder="English text...">
                                            <?php endif; ?>
                                        </div>

                                        <div class="language-field">
                                            <label class="language-label">🇬🇷 Ελληνικά</label>
                                            <?php if (strlen($emailTexts['el'][$key]) > 100): ?>
                                                <textarea name="el[<?= $key ?>]" class="form-control" rows="3" placeholder="Ελληνικό κείμενο..."><?= htmlspecialchars($emailTexts['el'][$key]) ?></textarea>
                                            <?php else: ?>
                                                <input type="text" name="el[<?= $key ?>]" class="form-control" value="<?= htmlspecialchars($emailTexts['el'][$key]) ?>" placeholder="Ελληνικό κείμενο...">
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endforeach; ?>
            </form>
        </div>
    </div>
</div>

<style>
    .email-templates-container {
        padding: 20px;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #e9ecef;
    }

    .page-header h1 {
        margin: 0;
        color: #2c3e50;
    }

    .page-header p {
        margin: 5px 0 0 0;
        color: #6c757d;
    }

    .header-actions {
        display: flex;
        gap: 10px;
    }

    .email-management-layout {
        display: grid;
        grid-template-columns: 300px 1fr;
        gap: 30px;
    }

    .template-sidebar {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    .template-sidebar h3 {
        margin-top: 0;
        color: #495057;
    }

    .template-status-section {
        margin-bottom: 25px;
    }

    .template-status-section h4 {
        margin-bottom: 15px;
        color: #6c757d;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .language-list,
    .template-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .language-item,
    .template-item {
        padding: 10px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .language-code {
        font-weight: bold;
        color: #007bff;
    }

    .language-name {
        color: #6c757d;
        margin-left: 10px;
    }

    .template-name {
        font-weight: 600;
        margin-bottom: 5px;
    }

    .template-files {
        display: flex;
        gap: 8px;
    }

    .file-status {
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: bold;
    }

    .file-status.exists {
        background: #d4edda;
        color: #155724;
    }

    .file-status.missing {
        background: #f8d7da;
        color: #721c24;
    }

    .email-editor-main {
        background: white;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }

    .editor-tabs {
        display: flex;
        border-bottom: 1px solid #dee2e6;
    }

    .tab-btn {
        padding: 15px 25px;
        border: none;
        background: #f8f9fa;
        cursor: pointer;
        border-bottom: 3px solid transparent;
        transition: all 0.3s;
    }

    .tab-btn.active {
        background: white;
        border-bottom-color: #007bff;
        color: #007bff;
    }

    .tab-content {
        display: none;
        padding: 25px;
    }

    .tab-content.active {
        display: block;
    }

    .email-text-form .form-group {
        margin-bottom: 20px;
    }

    .email-text-form label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #495057;
    }

    .email-text-form .form-control {
        width: 100%;
        padding: 10px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
    }

    .email-text-form .form-help {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
    }

    /* New grouped layout styles */
    .email-group {
        margin-bottom: 40px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        overflow: hidden;
    }

    .group-title {
        background: #f8f9fa;
        margin: 0;
        padding: 15px 20px;
        border-bottom: 1px solid #e9ecef;
        color: #495057;
        font-size: 16px;
        font-weight: 600;
    }

    .form-row {
        display: grid;
        grid-template-columns: 200px 1fr;
        gap: 20px;
        padding: 20px;
        border-bottom: 1px solid #f1f3f4;
        align-items: start;
    }

    .form-row:last-child {
        border-bottom: none;
    }

    .field-label {
        padding-top: 8px;
    }

    .field-label label {
        display: block;
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }

    .field-key {
        font-size: 11px;
        color: #6c757d;
        font-family: monospace;
        background: #f8f9fa;
        padding: 2px 6px;
        border-radius: 3px;
        display: inline-block;
    }

    .language-fields {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .language-field {
        display: flex;
        flex-direction: column;
    }

    .language-label {
        font-size: 12px;
        font-weight: 600;
        color: #6c757d;
        margin-bottom: 5px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .language-field .form-control {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .language-field .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        outline: 0;
    }

    .language-field textarea.form-control {
        resize: vertical;
        min-height: 80px;
    }

    .alert {
        padding: 15px;
        border-radius: 6px;
        margin-bottom: 20px;
    }

    .alert-success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    /* Responsive design */
    @media (max-width: 1200px) {
        .email-management-layout {
            grid-template-columns: 250px 1fr;
        }
    }

    @media (max-width: 992px) {
        .email-management-layout {
            grid-template-columns: 1fr;
        }

        .template-sidebar {
            order: 2;
        }

        .form-row {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .language-fields {
            grid-template-columns: 1fr;
        }
    }
</style>

<script>
    function regenerateTemplates() {
        if (confirm('This will regenerate all email templates. Continue?')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = '<input type="hidden" name="action" value="regenerate_templates">';
            document.body.appendChild(form);
            form.submit();
        }
    }

    function saveEmailTexts() {
        const form = document.getElementById('email-texts-form');
        const formData = new FormData(form);
        formData.append('action', 'update_texts');

        // Show loading state
        const saveBtn = document.querySelector('[onclick="saveEmailTexts()"]');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        saveBtn.disabled = true;

        // Submit form
        fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                // Reload page to show success message
                window.location.reload();
            })
            .catch(error => {
                alert('Error saving email texts: ' + error);
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            });
    }

    function previewTemplate() {
        // Get selected template type
        const templateName = prompt('Enter template name to preview (confirmation, welcome, etc.):');
        if (templateName) {
            // Open preview in new window
            const previewUrl = `../includes/email_templates/templates/${templateName}.html`;
            window.open(previewUrl, '_blank', 'width=800,height=600');
        }
    }

    // Auto-save functionality
    let saveTimeout;
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('email-texts-form');
        if (form) {
            form.addEventListener('input', function() {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    // Show auto-save indicator
                    const indicator = document.createElement('div');
                    indicator.className = 'auto-save-indicator';
                    indicator.innerHTML = '<i class="fas fa-save"></i> Auto-saving...';
                    indicator.style.cssText = 'position:fixed;top:20px;right:20px;background:#28a745;color:white;padding:10px;border-radius:4px;z-index:1000;';
                    document.body.appendChild(indicator);

                    setTimeout(() => {
                        indicator.remove();
                    }, 2000);
                }, 3000);
            });
        }
    });
</script>