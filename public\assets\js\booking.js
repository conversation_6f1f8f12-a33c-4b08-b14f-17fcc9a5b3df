/**
 * ========================================
 * BOOKING SYSTEM JAVASCRIPT
 * ========================================
 *
 * Main booking functionality including:
 * - Date and time selection
 * - Service booking
 * - Form validation
 * - Confirmation modal
 *
 * <AUTHOR> System
 * @version 1.0
 */

document.addEventListener('DOMContentLoaded', function() {

    /* ========================================
       UTILITY FUNCTIONS
       ======================================== */

    /**
     * Show notification messages to user
     * @param {string} message - Message to display
     * @param {string} type - Message type (success, error, warning)
     */
    function showBookingMessage(message, type = 'success') {
        const existingMessages = document.querySelectorAll('.temp-message');
        existingMessages.forEach(msg => msg.remove());

        const messageDiv = document.createElement('div');
        messageDiv.className = `${type}-message temp-message`;
        messageDiv.textContent = message;

        const header = document.querySelector('header');
        if (header && header.nextSibling) {
            header.parentNode.insertBefore(messageDiv, header.nextSibling);
        } else {
            document.querySelector('.container').insertBefore(messageDiv, document.querySelector('.container').firstChild);
        }

        setTimeout(() => messageDiv.remove(), 5000);
        messageDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    /* ========================================
       DOM ELEMENT REFERENCES
       ======================================== */
    const elements = {
        serviceSelect: document.getElementById('service-select'),
        confirmBtn: document.getElementById('confirm-btn'),
        selectedDateInput: document.getElementById('selected-date'),
        selectedTimeInput: document.getElementById('selected-time'),
        step2: document.getElementById('step-2'),
        step3: document.getElementById('step-3'),
        availableTimesContainer: document.getElementById('available-times'),
        bookingControls: document.getElementById('booking-controls'),
        yearSelect: document.getElementById('year-select'),
        monthSelect: document.getElementById('month-select'),
        daysContainer: document.getElementById('days-container')
    };

    /* ========================================
       APPLICATION STATE
       ======================================== */
    let state = {
        selectedDate: '',
        selectedService: '',
        selectedTime: '',
        availableEmployees: [],
        selectedEmployee: '',
        isLoading: false
    };

    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;

    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];

    // Reset booking state
    function resetBookingState() {
        state.selectedDate = '';
        state.selectedService = '';
        state.selectedTime = '';
        state.isLoading = false;

        if (elements.selectedDateInput) elements.selectedDateInput.value = '';
        if (elements.selectedTimeInput) elements.selectedTimeInput.value = '';
        if (elements.serviceSelect) elements.serviceSelect.value = '';
        if (elements.step2) elements.step2.style.display = 'none';
        if (elements.step3) elements.step3.style.display = 'none';
        if (elements.availableTimesContainer) elements.availableTimesContainer.innerHTML = '';
        if (elements.bookingControls) elements.bookingControls.style.display = 'none';
        if (elements.confirmBtn) elements.confirmBtn.disabled = true;
    }

    /* ========================================
       UI STATE FUNCTIONS
       ======================================== */

    /**
     * Show loading state in container
     * @param {HTMLElement} container - Container to show loading in
     * @param {string} message - Optional loading message
     */
    function showLoading(container, message = null) {
        if (container) {
            const loadingText = message || window.customerTexts?.loading_message || 'Loading...';
            container.innerHTML = `<div class="loading">${loadingText}</div>`;
        }
    }

    /**
     * Show error state in container
     * @param {HTMLElement} container - Container to show error in
     * @param {string} message - Error message to display
     */
    function showError(container, message) {
        if (container) {
            container.innerHTML = `<div class="error-message">${message}</div>`;
        }
    }

    // Year selection handler
    if (elements.yearSelect) {
        elements.yearSelect.addEventListener('change', function() {
            const selectedYear = parseInt(this.value);
            const currentSelectedMonth = elements.monthSelect ? elements.monthSelect.value : '';

            if (elements.daysContainer) {
                elements.daysContainer.style.display = 'none';
                elements.daysContainer.innerHTML = '';
            }

            if (selectedYear) {
                const startMonth = (selectedYear === currentYear) ? currentMonth : 1;

                if (elements.monthSelect) {
                    elements.monthSelect.innerHTML = '<option value="">Select Month</option>';
                    elements.monthSelect.disabled = false;

                    for (let month = startMonth; month <= 12; month++) {
                        const option = document.createElement('option');
                        option.value = month;
                        option.textContent = monthNames[month - 1];
                        if (currentSelectedMonth && month == currentSelectedMonth) {
                            option.selected = true;
                        }
                        elements.monthSelect.appendChild(option);
                    }

                    if (currentSelectedMonth && currentSelectedMonth >= startMonth) {
                        loadDaysForMonth(selectedYear, currentSelectedMonth);
                    }
                }
            } else if (elements.monthSelect) {
                elements.monthSelect.disabled = true;
                elements.monthSelect.innerHTML = '<option value="">Select Month</option>';
            }
        });
    }

    // Month selection handler
    if (elements.monthSelect) {
        elements.monthSelect.addEventListener('change', function() {
            const year = elements.yearSelect ? elements.yearSelect.value : '';
            const month = this.value;

            if (year && month) {
                loadDaysForMonth(year, month);
            } else if (elements.daysContainer) {
                elements.daysContainer.style.display = 'none';
                elements.daysContainer.innerHTML = '';
            }
        });
    }

    // Auto-load current month
    if (elements.yearSelect && elements.monthSelect && elements.yearSelect.value && elements.monthSelect.value) {
        loadDaysForMonth(elements.yearSelect.value, elements.monthSelect.value);
    }



    // Load days for selected month
    function loadDaysForMonth(year, month) {
        if (!elements.daysContainer) return;

        showLoading(elements.daysContainer, window.customerTexts?.js_loading_days || 'Loading available days...');
        elements.daysContainer.style.display = 'block';

        const currentLang = new URLSearchParams(window.location.search).get('lang') || 'primary';
        fetch(`api/get_days.php?year=${year}&month=${month}&lang=${encodeURIComponent(currentLang)}`)
            .then(response => response.ok ? response.text() : Promise.reject('Failed to load'))
            .then(html => {
                elements.daysContainer.innerHTML = html;
                attachDateCardHandlers();
            })
            .catch(error => {
                console.error('Error loading days:', error);
                showError(elements.daysContainer, window.customerTexts?.js_error_loading_days || 'Error loading days. Please try again.');
            });
    }

    // Attach date card click handlers
    function attachDateCardHandlers() {
        document.querySelectorAll('.date-card:not(.disabled):not(.past):not(.empty)').forEach(card => {
            card.addEventListener('click', () => selectDate(card));
        });
    }

    // Select date function
    function selectDate(card) {
        if (state.isLoading) return;

        document.querySelectorAll('.date-card').forEach(c => c.classList.remove('selected'));
        card.classList.add('selected');

        state.selectedDate = card.getAttribute('data-date');
        if (elements.selectedDateInput) elements.selectedDateInput.value = state.selectedDate;

        // Show step 2 and reset subsequent steps
        if (elements.step2) elements.step2.style.display = 'block';
        if (elements.step3) elements.step3.style.display = 'none';
        if (elements.availableTimesContainer) elements.availableTimesContainer.innerHTML = '';
        if (elements.bookingControls) elements.bookingControls.style.display = 'none';
        if (elements.selectedTimeInput) elements.selectedTimeInput.value = '';
        state.selectedTime = '';
        if (elements.confirmBtn) elements.confirmBtn.disabled = true;

        if (elements.step2) elements.step2.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    // Initialize date card handlers
    attachDateCardHandlers();

    // Service selection
    if (elements.serviceSelect) {
        elements.serviceSelect.addEventListener('change', function() {
            if (state.isLoading) return;

            state.selectedService = this.value;

            if (state.selectedDate && state.selectedService) {
                fetchAvailableTimes(state.selectedDate, state.selectedService);
            } else {
                if (elements.step3) elements.step3.style.display = 'none';
                if (elements.availableTimesContainer) elements.availableTimesContainer.innerHTML = '';
                if (elements.bookingControls) elements.bookingControls.style.display = 'none';
                if (elements.selectedTimeInput) elements.selectedTimeInput.value = '';
                state.selectedTime = '';
                if (elements.confirmBtn) elements.confirmBtn.disabled = true;
            }
        });
    }

    // Fetch available times from API
    function fetchAvailableTimes(date, service) {
        if (state.isLoading || !elements.availableTimesContainer) return;

        state.isLoading = true;
        if (elements.step3) elements.step3.style.display = 'block';
        showLoading(elements.availableTimesContainer, window.customerTexts?.loading_times_message || 'Loading available times...');
        if (elements.bookingControls) elements.bookingControls.style.display = 'none';
        if (elements.selectedTimeInput) elements.selectedTimeInput.value = '';
        state.selectedTime = '';
        if (elements.confirmBtn) elements.confirmBtn.disabled = true;

        // Get current language and pass it to API
        const currentLang = new URLSearchParams(window.location.search).get('lang') || 'primary';

        // Construct robust URL that works on both localhost and real servers
        let baseUrl;
        if (window.location.pathname.endsWith('.php')) {
            // If current page is a PHP file, get its directory
            baseUrl = window.location.origin + window.location.pathname.replace(/\/[^\/]*\.php$/, '');
        } else {
            // If current page is root or directory, use as-is
            baseUrl = window.location.origin + window.location.pathname.replace(/\/$/, '');
        }
        const url = `${baseUrl}/api/get_all_times.php?date=${encodeURIComponent(date)}&service=${encodeURIComponent(service)}&lang=${encodeURIComponent(currentLang)}`;

        // Add debugging for server issues
        console.log('Fetching times from URL:', url);

        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText} - URL: ${url}`);
                }
                return response.json();
            })
            .then(data => {
                state.isLoading = false;
                elements.availableTimesContainer.innerHTML = '';

                // Store employee data globally for use in employee selection
                if (data.employees) {
                    window.employeeData = data.employees;
                }

                if (data.success && data.slots && data.slots.length > 0) {
                    // Add service info
                    const infoDiv = document.createElement('div');
                    infoDiv.className = 'time-slots-info';
                    // Generate employee legend or auto-assignment message
                    const allowEmployeeSelection = data.allow_employee_selection === true;
                    let employeeLegend = '';

                    if (allowEmployeeSelection) {
                        // Customer choice service - show employee legend
                        employeeLegend = generateEmployeeLegend(data.employees, 'not-auto-assign');
                    } else {
                        // Auto-assign service - show auto-assignment message in exact same structure
                        employeeLegend = `
                            <div class="employee-legend auto-assign">
                                <h4>${window.customerTexts?.staff_legend_label || 'Staff Legend'}:</h4>
                                <div class="employee-legend-items">
                                    <div class="auto-assign-message">
                                        <i class="fas fa-magic"></i>
                                        <span>Staff will be automatically assigned based on availability</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    }

                    infoDiv.innerHTML = `
                        <div class="service-details">
                            <div class="service-name">${data.service_name}</div>
                            <div class="service-meta">
                                <div class="meta-item">
                                    <i class="fas fa-clock"></i>
                                    <span>${data.service_duration} ${window.customerTexts?.minutes_suffix || 'minutes'}</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-calendar-check"></i>
                                    <span>${data.available_slots} ${data.available_slots === 1 ? (window.customerTexts?.js_available_slot || 'available slot') : (window.customerTexts?.js_available_slots || 'available slots')}</span>
                                </div>
                                ${data.unavailable_slots > 0 ? `
                                <div class="meta-item">
                                    <i class="fas fa-calendar-times"></i>
                                    <span>${data.unavailable_slots} ${data.unavailable_slots === 1 ? (window.customerTexts?.js_unavailable_slot || 'unavailable slot') : (window.customerTexts?.js_unavailable_slots || 'unavailable slots')}</span>
                                </div>
                                ` : ''}
                            </div>
                            ${employeeLegend}
                        </div>
                    `;
                    elements.availableTimesContainer.appendChild(infoDiv);

                    // Group slots by time periods
                    const periods = groupSlotsByPeriod(data.slots);

                    // Create time period sections
                    Object.keys(periods).forEach(periodName => {
                        if (periods[periodName].length > 0) {
                            createTimePeriodSection(elements.availableTimesContainer, periodName, periods[periodName], data.service_duration);
                        }
                    });

                    // Scroll to step 3
                    if (elements.step3) elements.step3.scrollIntoView({ behavior: 'smooth', block: 'start' });
                } else {
                    // Show no slots message
                    const noSlotsDiv = document.createElement('div');
                    noSlotsDiv.className = 'no-slots-message';
                    noSlotsDiv.innerHTML = `
                        <i class="fas fa-calendar-times"></i>
                        <h3>${window.customerTexts?.js_no_times_available || 'No Available Times'}</h3>
                        <p>${data.message || window.customerTexts?.js_no_times_message || 'No available times for this date and service combination.'}</p>
                        <p><small>${window.customerTexts?.js_try_different_date || 'Please try selecting a different date or service.'}</small></p>
                    `;
                    elements.availableTimesContainer.appendChild(noSlotsDiv);

                    if (elements.bookingControls) elements.bookingControls.style.display = 'none';
                    if (elements.selectedTimeInput) elements.selectedTimeInput.value = '';
                    state.selectedTime = '';
                    if (elements.confirmBtn) elements.confirmBtn.disabled = true;
                }
            })
            .catch(error => {
                state.isLoading = false;
                console.error('Error fetching times:', error);
                console.error('Failed URL:', url);

                // Show detailed error message for debugging
                const errorMessage = error.message.includes('HTTP')
                    ? `Server error: ${error.message}`
                    : (window.customerTexts?.js_error_loading_times || 'Error loading available times. Please check your connection and try again.');

                showError(elements.availableTimesContainer, errorMessage);
                if (elements.bookingControls) elements.bookingControls.style.display = 'none';
                if (elements.selectedTimeInput) elements.selectedTimeInput.value = '';
                state.selectedTime = '';
                if (elements.confirmBtn) elements.confirmBtn.disabled = true;
            });
    }

    // Group slots by time periods
    function groupSlotsByPeriod(slots) {
        const periods = {
            morning: [],
            afternoon: [],
            evening: []
        };

        slots.forEach(slot => {
            const hour = parseInt(slot.time.split(':')[0]);
            if (hour < 12) {
                periods.morning.push(slot);
            } else if (hour < 17) {
                periods.afternoon.push(slot);
            } else {
                periods.evening.push(slot);
            }
        });

        return periods;
    }

    // Create time period section
    function createTimePeriodSection(container, periodName, slots, serviceDuration) {
        const periodDiv = document.createElement('div');
        periodDiv.className = 'time-period';

        const periodIcons = {
            morning: 'fas fa-sun',
            afternoon: 'fas fa-cloud-sun',
            evening: 'fas fa-moon'
        };

        const periodLabels = {
            morning: window.customerTexts?.time_morning || 'Morning',
            afternoon: window.customerTexts?.time_afternoon || 'Afternoon',
            evening: window.customerTexts?.time_evening || 'Evening'
        };

        periodDiv.innerHTML = `
            <div class="time-period-header">
                <i class="${periodIcons[periodName]}"></i>
                <h3>${periodLabels[periodName]}</h3>
            </div>
            <div class="time-period-slots"></div>
        `;

        const slotsContainer = periodDiv.querySelector('.time-period-slots');

        slots.forEach(slot => {
            const btn = document.createElement('button');
            btn.type = 'button';
            btn.className = slot.available ? 'time-slot' : 'time-slot unavailable';
            btn.setAttribute('data-time', slot.time);
            btn.disabled = !slot.available;

            // Create employee dots only for customer choice services
            let employeeDots = '';
            const selectedServiceElement = elements.serviceSelect.querySelector(`option[value="${state.selectedService}"]`);
            const allowEmployeeSelection = selectedServiceElement?.getAttribute('data-allow-employee-selection') === '1';

            if (allowEmployeeSelection && slot.available && slot.available_employees && slot.available_employees.length > 0) {
                employeeDots = '<div class="employee-dots">';
                slot.available_employees.forEach((employeeId) => {
                    const style = getEmployeeStyle(employeeId);
                    employeeDots += `<span class="employee-dot" style="background-color: ${style.color}" data-employee-id="${employeeId}" title="${style.name}">${style.initial}</span>`;
                });
                employeeDots += '</div>';
            }

            btn.innerHTML = `
                <div class="time-slot-time">${slot.formatted_time}</div>
                ${employeeDots}
                ${!slot.available ? `<div class="time-slot-status"><i class="fas fa-lock"></i> ${window.customerTexts?.js_reserved_label || 'Reserved'}</div>` : ''}
            `;

            if (slot.available) {
                btn.addEventListener('click', function() {
                    selectTimeSlot(this, slot.time, slot.available_employees || []);
                });
            }

            slotsContainer.appendChild(btn);
        });

        container.appendChild(periodDiv);
    }

    // Select time slot
    function selectTimeSlot(button, time, availableEmployees = []) {
        // Remove selection from all time slots
        document.querySelectorAll('.time-slot').forEach(btn => {
            btn.classList.remove('selected');
        });

        // Select current time slot
        button.classList.add('selected');
        state.selectedTime = time;

        // Only store employee information for customer choice services
        const selectedServiceElement = elements.serviceSelect.querySelector(`option[value="${state.selectedService}"]`);
        const allowEmployeeSelection = selectedServiceElement?.getAttribute('data-allow-employee-selection') === '1';

        if (allowEmployeeSelection) {
            state.availableEmployees = availableEmployees;
        } else {
            state.availableEmployees = []; // Clear employee data for auto-assign services
        }

        if (elements.selectedTimeInput) elements.selectedTimeInput.value = state.selectedTime;

        // Show review button
        if (elements.bookingControls) elements.bookingControls.style.display = 'block';
        const reviewBtn = document.getElementById('review-btn');

        if (reviewBtn) {
            reviewBtn.disabled = false;
        }

        // Scroll to booking controls
        if (elements.bookingControls) elements.bookingControls.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }



    // Generate employee selection HTML
    function generateEmployeeSelection() {
        if (!state.availableEmployees || state.availableEmployees.length === 0) {
            return '';
        }

        // If only one employee, show assigned employee
        if (state.availableEmployees.length === 1) {
            const employeeId = state.availableEmployees[0];
            const style = getEmployeeStyle(employeeId);

            return `
                <div class="confirmation-detail-item">
                    <span class="confirmation-detail-label">
                        <i class="fas fa-user-tie"></i>
                        ${window.customerTexts?.staff_label || 'Staff'}
                    </span>
                    <span class="confirmation-detail-value employee-assigned">
                        <span class="employee-dot" style="background-color: ${style.color}">${style.initial}</span>
                        ${style.name}
                    </span>
                </div>
            `;
        }

        // Multiple employees - show selection
        let optionsHTML = '';

        state.availableEmployees.forEach((employeeId, index) => {
            const style = getEmployeeStyle(employeeId);
            const isSelected = state.selectedEmployee === employeeId || (index === 0 && !state.selectedEmployee);

            optionsHTML += `
                <div class="employee-option">
                    <input type="radio"
                           name="selected_employee"
                           value="${employeeId}"
                           id="employee_${employeeId}"
                           ${isSelected ? 'checked' : ''}
                           onchange="selectEmployee('${employeeId}')">
                    <label for="employee_${employeeId}">
                        <span class="employee-dot" style="background-color: ${style.color}">${style.initial}</span>
                        ${style.name}
                    </label>
                </div>
            `;
        });

        return `
            <div class="employee-selection">
                <h4>${window.customerTexts?.choose_staff_label || 'Choose your preferred staff:'}</h4>
                <div class="employee-options">
                    ${optionsHTML}
                </div>
            </div>
        `;
    }

    // Get employee name by ID (placeholder - will be populated from API)
    function getEmployeeName(employeeId) {
        // This will be populated with actual employee data from the API
        const employeeNames = window.employeeData || {};
        return employeeNames[employeeId] || `Employee ${employeeId.slice(-3)}`;
    }

    // Get consistent employee color and initial based on employee ID
    function getEmployeeStyle(employeeId) {
        // Create a consistent hash from employee ID to ensure same employee always gets same color
        let hash = 0;
        for (let i = 0; i < employeeId.length; i++) {
            hash = employeeId.charCodeAt(i) + ((hash << 5) - hash);
        }

        // Fixed color palette for employees
        const colors = [
            '#3498db', // Blue
            '#2ecc71', // Green
            '#e74c3c', // Red
            '#f39c12', // Orange
            '#9b59b6', // Purple
            '#1abc9c', // Teal
            '#34495e', // Dark blue-gray
            '#e67e22'  // Dark orange
        ];

        // Get consistent color index based on employee ID hash
        const colorIndex = Math.abs(hash) % colors.length;
        const color = colors[colorIndex];

        // Get employee name and extract initial
        const employeeName = getEmployeeName(employeeId);
        const initial = employeeName.charAt(0).toUpperCase();

        return {
            color: color,
            initial: initial,
            name: employeeName
        };
    }

    // Generate employee color legend
    function generateEmployeeLegend(employees, additionalClass = '') {
        if (!employees || Object.keys(employees).length === 0) {
            return '';
        }

        const employeeIds = Object.keys(employees);

        // Only show legend if there are multiple employees
        if (employeeIds.length <= 1) {
            return '';
        }

        let legendItems = '';
        employeeIds.forEach((employeeId) => {
            const style = getEmployeeStyle(employeeId);
            legendItems += `
                <div class="employee-legend-item">
                    <span class="employee-dot" style="background-color: ${style.color}">${style.initial}</span>
                    <span>${style.name}</span>
                </div>
            `;
        });

        return `
            <div class="employee-legend ${additionalClass}">
                <h4>${window.customerTexts?.staff_legend_label || 'Staff Legend'}:</h4>
                <div class="employee-legend-items">
                    ${legendItems}
                </div>
            </div>
        `;
    }

    // Select employee function
    window.selectEmployee = function(employeeId) {
        state.selectedEmployee = employeeId;
    };

    // Create beautiful confirmation modal
    function createConfirmationModal() {
        const overlay = document.createElement('div');
        overlay.className = 'confirmation-overlay';
        overlay.id = 'confirmation-overlay';

        overlay.innerHTML = `
            <div class="confirmation-modal">
                <div class="confirmation-header">
                    <i class="fas fa-calendar-check confirmation-icon"></i>
                    <h2 class="confirmation-title">${window.customerTexts?.js_confirm_appointment || 'Confirm Your Appointment'}</h2>
                </div>
                <div class="confirmation-content">
                    <p class="confirmation-message">
                        ${window.customerTexts?.js_review_booking_details || 'Please review your booking details before confirming.'}
                    </p>
                    <div class="confirmation-details" id="confirmation-details">
                        <!-- Details will be populated dynamically -->
                    </div>
                    <div class="confirmation-actions">
                        <button type="button" class="confirmation-btn confirmation-btn-cancel" onclick="hideConfirmationModal()">
                            <i class="fas fa-times"></i>
                            ${window.customerTexts?.js_cancel_modal_button || 'Cancel'}
                        </button>
                        <button type="button" class="confirmation-btn confirmation-btn-confirm" onclick="submitReservation()">
                            <i class="fas fa-check-circle"></i>
                            ${window.customerTexts?.js_book_now_button || 'Book Now'}
                        </button>
                    </div>
                    <div class="confirmation-note">
                        <i class="fas fa-envelope"></i>
                        ${window.customerTexts?.js_confirmation_email_note || 'You will receive a confirmation email after booking'}
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);

        // Close modal when clicking outside
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                hideConfirmationModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && overlay.classList.contains('show')) {
                hideConfirmationModal();
            }
        });
    }

    // Show confirmation modal
    function showConfirmationModal(serviceData) {
        let overlay = document.getElementById('confirmation-overlay');
        if (!overlay) {
            createConfirmationModal();
            overlay = document.getElementById('confirmation-overlay');
        }

        // Populate details
        const detailsContainer = document.getElementById('confirmation-details');
        let detailsHTML = `
            <div class="confirmation-detail-item">
                <span class="confirmation-detail-label">
                    <i class="fas fa-spa"></i>
                    ${window.customerTexts?.service_label || 'Service'}
                </span>
                <span class="confirmation-detail-value">${serviceData.name}</span>
            </div>
            <div class="confirmation-detail-item">
                <span class="confirmation-detail-label">
                    <i class="fas fa-clock"></i>
                    ${window.customerTexts?.duration_label || 'Duration'}
                </span>
                <span class="confirmation-detail-value">${serviceData.duration} ${window.customerTexts?.minutes_suffix || 'minutes'}</span>
            </div>
            <div class="confirmation-detail-item">
                <span class="confirmation-detail-label">
                    <i class="fas fa-euro-sign"></i>
                    ${window.customerTexts?.price_label || 'Price'}
                </span>
                <span class="confirmation-detail-value">€${serviceData.price}</span>
            </div>
            <div class="confirmation-detail-item">
                <span class="confirmation-detail-label">
                    <i class="fas fa-calendar-alt"></i>
                    ${window.customerTexts?.date_label || 'Date'}
                </span>
                <span class="confirmation-detail-value">${serviceData.date}</span>
            </div>
            <div class="confirmation-detail-item">
                <span class="confirmation-detail-label">
                    <i class="fas fa-clock"></i>
                    ${window.customerTexts?.time_label || 'Time'}
                </span>
                <span class="confirmation-detail-value">${serviceData.time}</span>
            </div>
        `;

        // Add employee selection only if service allows it and employees are available
        const selectedServiceElement = elements.serviceSelect.querySelector(`option[value="${state.selectedService}"]`);
        const allowEmployeeSelection = selectedServiceElement?.getAttribute('data-allow-employee-selection') === '1';

        if (allowEmployeeSelection && state.availableEmployees && state.availableEmployees.length > 0) {
            detailsHTML += generateEmployeeSelection();
        }
        // For auto-assign services, show NO employee information at all

        detailsContainer.innerHTML = detailsHTML;

        // Show modal with animation
        overlay.classList.add('show');
        document.body.style.overflow = 'hidden'; // Prevent background scrolling
    }

    // Hide confirmation modal
    window.hideConfirmationModal = function() {
        const overlay = document.getElementById('confirmation-overlay');
        if (overlay) {
            overlay.classList.remove('show');
            document.body.style.overflow = ''; // Restore scrolling
        }
    };

    // Submit reservation
    window.submitReservation = function() {
        const reservationForm = document.getElementById('reservation-form');
        if (reservationForm) {
            // Check if service allows employee selection
            const selectedServiceElement = elements.serviceSelect.querySelector(`option[value="${state.selectedService}"]`);
            const allowEmployeeSelection = selectedServiceElement?.getAttribute('data-allow-employee-selection') === '1';

            // Set selected employee value only if service allows employee selection
            const selectedEmployeeInput = document.getElementById('selected-employee');
            if (selectedEmployeeInput) {
                if (allowEmployeeSelection) {
                    // Customer choice service - get selected employee
                    const selectedRadio = document.querySelector('input[name="selected_employee"]:checked');
                    if (selectedRadio) {
                        selectedEmployeeInput.value = selectedRadio.value;
                    } else if (state.availableEmployees && state.availableEmployees.length === 1) {
                        selectedEmployeeInput.value = state.availableEmployees[0];
                    } else if (state.selectedEmployee) {
                        selectedEmployeeInput.value = state.selectedEmployee;
                    }
                } else {
                    // Auto-assign service - clear employee selection (server will auto-assign)
                    selectedEmployeeInput.value = '';
                }
            }

            hideConfirmationModal();
            reservationForm.submit();
        }
    };

    // Confirm reservation (show modal)
    window.confirmReservation = function() {
        const selectedService = elements.serviceSelect?.options[elements.serviceSelect.selectedIndex]?.text?.split('(')[0]?.trim();
        const selectedDate = elements.selectedDateInput?.value;
        const selectedOption = elements.serviceSelect?.options[elements.serviceSelect.selectedIndex];
        const selectedServiceDuration = selectedOption?.getAttribute('data-duration');
        const selectedServicePrice = selectedOption?.getAttribute('data-price');

        // Get time from the preview display
        const timeElements = document.querySelectorAll('.preview-detail-row .preview-detail-value');
        const timeValue = timeElements[timeElements.length - 1]?.textContent || state.selectedTime;

        // Format date
        // Use Greek locale for primary language, English for secondary
        const currentLang = new URLSearchParams(window.location.search).get('lang') || 'primary';
        const locale = currentLang === 'primary' ? 'el-GR' : 'en-US';

        const formattedDate = new Date(selectedDate + 'T00:00:00').toLocaleDateString(locale, {
            weekday: 'long',
            month: 'long',
            day: 'numeric',
            year: 'numeric'
        });

        // Prepare service data
        const serviceData = {
            name: selectedService,
            duration: selectedServiceDuration,
            price: parseFloat(selectedServicePrice).toFixed(2),
            date: formattedDate,
            time: timeValue
        };

        // Show beautiful confirmation modal
        showConfirmationModal(serviceData);
    };

    // Add event listener for review button
    const reviewBtn = document.getElementById('review-btn');
    if (reviewBtn) {
        reviewBtn.addEventListener('click', confirmReservation);
    }

    // Form submission validation
    const reservationForm = document.getElementById('reservation-form');
    if (reservationForm) {
        reservationForm.addEventListener('submit', function(e) {
            if (!elements.selectedDateInput?.value || !elements.serviceSelect?.value || !elements.selectedTimeInput?.value) {
                e.preventDefault();
                showBookingMessage(window.customerTexts?.complete_all_steps_error || 'Please complete all steps: 1. Select a date, 2. Choose a service, 3. Pick a time slot', 'error');
                return false;
            }

            // Disable submit button to prevent double submission
            if (elements.confirmBtn) {
                elements.confirmBtn.disabled = true;
                elements.confirmBtn.textContent = window.customerTexts?.processing_button || 'Processing...';
            }

            // Re-enable after 3 seconds in case of error
            setTimeout(() => {
                if (elements.confirmBtn) {
                    elements.confirmBtn.disabled = false;
                    elements.confirmBtn.textContent = window.customerTexts?.confirm_reservation_button || 'Confirm Reservation';
                }
            }, 3000);
        });
    }

    // Auto-refresh available times every 2 minutes if step 3 is visible
    setInterval(() => {
        if (elements.step3?.style.display === 'block' && state.selectedDate && state.selectedService && !state.isLoading) {
            fetchAvailableTimes(state.selectedDate, state.selectedService);
        }
    }, 120000); // 2 minutes

    // Handle browser back/forward buttons
    window.addEventListener('popstate', function(e) {
        resetBookingState();
    });

    // Prevent form submission on Enter key in input fields (except submit button)
    document.querySelectorAll('input, select').forEach(input => {
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && this.type !== 'submit') {
                e.preventDefault();
            }
        });
    });
});
