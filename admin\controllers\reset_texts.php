<?php
require_once '../../includes/config.php';
require_once '../../includes/text_manager.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $success = TextManager::resetCustomerTexts();
    
    if ($success) {
        echo json_encode([
            'success' => true, 
            'message' => 'All texts reset to defaults successfully'
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to reset texts']);
    }
    
} catch (Exception $e) {
    error_log('Text reset error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Server error occurred']);
}
?>
