<?php
/**
 * Test Tenant Subdomain Issues
 * This script tests specific issues reported for tenant subdomains
 */

require_once 'includes/config.php';
require_once 'includes/tenant_init.php';
require_once 'includes/functions.php';
require_once 'includes/admin_functions.php';

echo "<h1>Tenant Subdomain Issues Test</h1>";
echo "<p>Current URL: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . $_SERVER['REQUEST_URI'] . "</p>";

// Test 1: Basic tenant detection
echo "<h2>1. Tenant Detection</h2>";
try {
    require_once 'includes/TenantContext.php';
    $tenantId = TenantContext::getTenant();
    $tenantData = TenantContext::getTenantData();
    
    echo "✅ Tenant ID: " . ($tenantId ?? 'NULL') . "<br>";
    if ($tenantData) {
        echo "✅ Business Name: " . htmlspecialchars($tenantData['business_name'] ?? 'N/A') . "<br>";
        echo "✅ Subdomain: " . htmlspecialchars($tenantData['subdomain'] ?? 'N/A') . "<br>";
    }
    
    // Check if this is a tenant subdomain
    $host = $_SERVER['HTTP_HOST'] ?? '';
    if (strpos($host, '.skrtz.gr') !== false && !in_array(explode('.', $host)[0], ['www', 'admin', 'api'])) {
        echo "✅ This appears to be a tenant subdomain<br>";
    } else {
        echo "ℹ️ This appears to be the main domain<br>";
    }
} catch (Exception $e) {
    echo "❌ Tenant detection error: " . $e->getMessage() . "<br>";
}

// Test 2: Employee functionality on tenant subdomain
echo "<h2>2. Employee Functionality</h2>";
try {
    $employees = getEmployees();
    echo "✅ Found " . count($employees) . " employees<br>";
    
    if (!empty($employees)) {
        $firstEmployee = array_values($employees)[0];
        $testId = $firstEmployee['id'];
        
        // Test getEmployeeById
        $employee = getEmployeeById($testId);
        if ($employee) {
            echo "✅ getEmployeeById works for ID: " . htmlspecialchars($testId) . "<br>";
        } else {
            echo "❌ getEmployeeById failed for ID: " . htmlspecialchars($testId) . "<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Employee functionality error: " . $e->getMessage() . "<br>";
}

// Test 3: Services functionality
echo "<h2>3. Services Functionality</h2>";
try {
    $services = getServices();
    echo "✅ Found " . count($services) . " services<br>";
    
    if (!empty($services)) {
        $firstService = array_values($services)[0];
        echo "✅ First service: " . htmlspecialchars($firstService['name']) . " (ID: " . htmlspecialchars($firstService['id']) . ")<br>";
    }
} catch (Exception $e) {
    echo "❌ Services functionality error: " . $e->getMessage() . "<br>";
}

// Test 4: Modal file accessibility
echo "<h2>4. Modal File Accessibility</h2>";
$modalPaths = [
    'admin/views/modals/view_employee.php',
    'admin/views/modals/edit_employee.php',
    'admin/views/modals/add_employee.php',
    'admin/views/modals/view_service.php',
    'admin/views/modals/edit_service.php',
    'admin/views/modals/add_service.php'
];

foreach ($modalPaths as $path) {
    if (file_exists($path)) {
        echo "✅ " . $path . " exists<br>";
    } else {
        echo "❌ " . $path . " missing<br>";
    }
}

// Test 5: Admin panel accessibility
echo "<h2>5. Admin Panel Accessibility</h2>";
if (file_exists('admin/index.php')) {
    echo "✅ admin/index.php exists<br>";
    
    // Test if we can include it without errors
    try {
        ob_start();
        $testInclude = true; // Flag to prevent full page rendering
        include 'admin/index.php';
        $content = ob_get_clean();
        
        if (strlen($content) > 1000) {
            echo "✅ Admin panel loads successfully (" . strlen($content) . " chars)<br>";
        } else {
            echo "❌ Admin panel content too short<br>";
        }
    } catch (Exception $e) {
        echo "❌ Admin panel include error: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ admin/index.php missing<br>";
}

// Test 6: Database isolation check
echo "<h2>6. Database Isolation Check</h2>";
try {
    require_once 'includes/Database.php';
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    if ($tenantId) {
        // Count employees for this tenant
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM employees WHERE tenant_id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        $tenantEmployees = $row['count'];
        
        // Count all employees
        $result = $conn->query("SELECT COUNT(*) as count FROM employees");
        $row = $result->fetchArray(SQLITE3_ASSOC);
        $totalEmployees = $row['count'];
        
        echo "✅ Tenant employees: $tenantEmployees<br>";
        echo "✅ Total employees: $totalEmployees<br>";
        
        if ($tenantEmployees <= $totalEmployees) {
            echo "✅ Data isolation appears to be working<br>";
        } else {
            echo "❌ Data isolation issue detected<br>";
        }
        
        // Same for services
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM services WHERE tenant_id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        $tenantServices = $row['count'];
        
        $result = $conn->query("SELECT COUNT(*) as count FROM services");
        $row = $result->fetchArray(SQLITE3_ASSOC);
        $totalServices = $row['count'];
        
        echo "✅ Tenant services: $tenantServices<br>";
        echo "✅ Total services: $totalServices<br>";
    } else {
        echo "ℹ️ No tenant context - showing all data<br>";
    }
} catch (Exception $e) {
    echo "❌ Database isolation check error: " . $e->getMessage() . "<br>";
}

// Test 7: Settings functionality
echo "<h2>7. Settings Functionality</h2>";
try {
    $settings = getSettings();
    echo "✅ Settings loaded successfully<br>";
    echo "📋 Site Name: " . htmlspecialchars($settings['site_name'] ?? 'NOT SET') . "<br>";
    echo "📋 Admin Email: " . htmlspecialchars($settings['admin_email'] ?? 'NOT SET') . "<br>";
} catch (Exception $e) {
    echo "❌ Settings error: " . $e->getMessage() . "<br>";
}

// Test 8: Error log check
echo "<h2>8. Recent Error Log</h2>";
$errorLogFile = ini_get('error_log');
if ($errorLogFile && file_exists($errorLogFile)) {
    $logLines = file($errorLogFile);
    $recentLines = array_slice($logLines, -10); // Last 10 lines
    
    echo "📋 Recent error log entries:<br>";
    echo "<pre style='background: #f5f5f5; padding: 10px; font-size: 12px; max-height: 200px; overflow-y: auto;'>";
    foreach ($recentLines as $line) {
        echo htmlspecialchars($line);
    }
    echo "</pre>";
} else {
    echo "❌ Error log file not found or not accessible<br>";
}

echo "<h2>Test Summary</h2>";
echo "<p><strong>Expected Results for Tenant Subdomains:</strong></p>";
echo "<ul>";
echo "<li>Tenant detection should work and show correct business name</li>";
echo "<li>Employee and service counts should be isolated to the tenant</li>";
echo "<li>Modal files should exist and be accessible</li>";
echo "<li>Admin panel should load without HTTP 500 errors</li>";
echo "<li>Settings should load tenant-specific values</li>";
echo "</ul>";

echo "<p><strong>If any tests fail, check:</strong></p>";
echo "<ul>";
echo "<li>Tenant initialization in includes/tenant_init.php</li>";
echo "<li>Database tenant_id columns and data</li>";
echo "<li>File permissions and paths</li>";
echo "<li>Error logs for specific error messages</li>";
echo "</ul>";
?>
