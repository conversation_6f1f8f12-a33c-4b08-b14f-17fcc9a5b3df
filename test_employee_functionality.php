<?php
/**
 * Test Employee Functionality
 * This script tests all employee-related functionality including modals
 */

require_once 'includes/config.php';
require_once 'includes/tenant_init.php';
require_once 'includes/functions.php';
require_once 'includes/admin_functions.php';

echo "<h1>Employee Functionality Test</h1>";
echo "<p>Current URL: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . $_SERVER['REQUEST_URI'] . "</p>";

// Test 1: Tenant context
echo "<h2>1. Tenant Context</h2>";
try {
    require_once 'includes/TenantContext.php';
    $tenantId = TenantContext::getTenant();
    $tenantData = TenantContext::getTenantData();
    
    echo "✅ Tenant ID: " . ($tenantId ?? 'NULL') . "<br>";
    echo "✅ Tenant Data: " . json_encode($tenantData) . "<br>";
} catch (Exception $e) {
    echo "❌ Tenant context error: " . $e->getMessage() . "<br>";
}

// Test 2: Get all employees
echo "<h2>2. Get All Employees</h2>";
try {
    $employees = getEmployees();
    echo "✅ Found " . count($employees) . " employees<br>";
    
    if (!empty($employees)) {
        echo "📋 Employee IDs:<br>";
        foreach ($employees as $emp) {
            echo "&nbsp;&nbsp;- " . htmlspecialchars($emp['id']) . " (" . htmlspecialchars($emp['name']) . ")<br>";
        }
        
        // Test with first employee
        $firstEmployee = array_values($employees)[0];
        $testEmployeeId = $firstEmployee['id'];
        echo "🎯 Using test employee ID: " . htmlspecialchars($testEmployeeId) . "<br>";
    } else {
        echo "❌ No employees found<br>";
        $testEmployeeId = null;
    }
} catch (Exception $e) {
    echo "❌ Get employees error: " . $e->getMessage() . "<br>";
    $testEmployeeId = null;
}

// Test 3: Get employee by ID
if ($testEmployeeId) {
    echo "<h2>3. Get Employee By ID</h2>";
    try {
        $employee = getEmployeeById($testEmployeeId);
        if ($employee) {
            echo "✅ Employee found: " . htmlspecialchars($employee['name']) . "<br>";
            echo "📋 Email: " . htmlspecialchars($employee['email'] ?? 'N/A') . "<br>";
            echo "📋 Phone: " . htmlspecialchars($employee['phone'] ?? 'N/A') . "<br>";
        } else {
            echo "❌ Employee not found with ID: " . htmlspecialchars($testEmployeeId) . "<br>";
        }
    } catch (Exception $e) {
        echo "❌ Get employee by ID error: " . $e->getMessage() . "<br>";
    }
}

// Test 4: Modal file existence
echo "<h2>4. Modal File Existence</h2>";
$modalFiles = [
    'admin/views/modals/view_employee.php',
    'admin/views/modals/edit_employee.php',
    'admin/views/modals/add_employee.php'
];

foreach ($modalFiles as $file) {
    if (file_exists($file)) {
        echo "✅ " . $file . " exists<br>";
    } else {
        echo "❌ " . $file . " missing<br>";
    }
}

// Test 5: Modal content loading simulation
if ($testEmployeeId) {
    echo "<h2>5. Modal Content Loading Test</h2>";
    
    // Test view employee modal
    echo "<h3>5a. View Employee Modal</h3>";
    try {
        ob_start();
        $_GET['id'] = $testEmployeeId;
        include 'admin/views/modals/view_employee.php';
        $viewContent = ob_get_clean();
        
        if (strlen($viewContent) > 100) {
            echo "✅ View modal content loaded (" . strlen($viewContent) . " chars)<br>";
        } else {
            echo "❌ View modal content too short or empty<br>";
            echo "Content: " . htmlspecialchars(substr($viewContent, 0, 200)) . "<br>";
        }
    } catch (Exception $e) {
        echo "❌ View modal error: " . $e->getMessage() . "<br>";
    }
    
    // Test edit employee modal
    echo "<h3>5b. Edit Employee Modal</h3>";
    try {
        ob_start();
        $_GET['id'] = $testEmployeeId;
        include 'admin/views/modals/edit_employee.php';
        $editContent = ob_get_clean();
        
        if (strlen($editContent) > 100) {
            echo "✅ Edit modal content loaded (" . strlen($editContent) . " chars)<br>";
        } else {
            echo "❌ Edit modal content too short or empty<br>";
            echo "Content: " . htmlspecialchars(substr($editContent, 0, 200)) . "<br>";
        }
    } catch (Exception $e) {
        echo "❌ Edit modal error: " . $e->getMessage() . "<br>";
    }
}

// Test 6: Services for employee assignment
echo "<h2>6. Services for Employee Assignment</h2>";
try {
    $services = getServices();
    echo "✅ Found " . count($services) . " services<br>";
    
    if (!empty($services)) {
        echo "📋 Service IDs:<br>";
        $serviceCount = 0;
        foreach ($services as $service) {
            if ($serviceCount < 5) { // Show first 5
                echo "&nbsp;&nbsp;- " . htmlspecialchars($service['id']) . " (" . htmlspecialchars($service['name']) . ")<br>";
            }
            $serviceCount++;
        }
        if ($serviceCount > 5) {
            echo "&nbsp;&nbsp;... and " . ($serviceCount - 5) . " more<br>";
        }
    } else {
        echo "❌ No services found - this will prevent employee creation<br>";
    }
} catch (Exception $e) {
    echo "❌ Get services error: " . $e->getMessage() . "<br>";
}

// Test 7: ID generation
echo "<h2>7. ID Generation Test</h2>";
try {
    $newEmployeeId = generate_employee_id();
    echo "✅ Generated employee ID: " . htmlspecialchars($newEmployeeId) . "<br>";
    
    // Validate format
    if (preg_match('/^EM-[0-9A-F]{10}$/', $newEmployeeId)) {
        echo "✅ ID format is correct<br>";
    } else {
        echo "❌ ID format is incorrect<br>";
    }
} catch (Exception $e) {
    echo "❌ ID generation error: " . $e->getMessage() . "<br>";
}

// Test 8: Database connection and table structure
echo "<h2>8. Database Structure Test</h2>";
try {
    require_once 'includes/Database.php';
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Check employees table structure
    $result = $conn->query("PRAGMA table_info(employees)");
    $columns = [];
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $columns[] = $row['name'];
    }
    
    echo "✅ Employees table columns: " . implode(', ', $columns) . "<br>";
    
    // Check if tenant_id column exists
    if (in_array('tenant_id', $columns)) {
        echo "✅ tenant_id column exists<br>";
    } else {
        echo "❌ tenant_id column missing<br>";
    }
    
    // Check employee_services table
    $result = $conn->query("PRAGMA table_info(employee_services)");
    $esColumns = [];
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $esColumns[] = $row['name'];
    }
    
    echo "✅ Employee_services table columns: " . implode(', ', $esColumns) . "<br>";
    
} catch (Exception $e) {
    echo "❌ Database structure error: " . $e->getMessage() . "<br>";
}

echo "<h2>Test Summary</h2>";
echo "<p><strong>Key Points:</strong></p>";
echo "<ul>";
echo "<li>If employees are found and modals load correctly, the view/edit functionality should work</li>";
echo "<li>If services are found and ID generation works, adding new employees should work</li>";
echo "<li>If tenant context is working, data isolation should be maintained</li>";
echo "<li>Check error logs for any additional issues during modal loading</li>";
echo "</ul>";

// Clear $_GET to avoid affecting other scripts
unset($_GET['id']);
?>
