<?php
require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
?>

<form method="post" action="actions/save_service.php" data-validate>
    <div class="form-row">
        <div class="form-group">
            <label>Service Name *</label>
            <input type="text" name="service_name" class="form-control" required>
        </div>
        <div class="form-group">
            <label>Duration (minutes) *</label>
            <input type="number" name="duration" class="form-control" min="1" required>
        </div>
    </div>

    <div class="form-group">
        <label>Price (€) *</label>
        <input type="number" step="0.01" name="price" class="form-control" min="0" required>
    </div>

    <div class="form-group">
        <label>Description</label>
        <textarea name="service_description" class="form-control" rows="3" placeholder="Describe what this service includes..."></textarea>
    </div>

    <div class="form-group">
        <div class="form-check">
            <input type="checkbox" name="allow_employee_selection" id="allow_employee_selection" class="form-check-input" value="1">
            <label for="allow_employee_selection" class="form-check-label">
                <i class="fas fa-user-check"></i> Customer choose employee
                <small class="form-text text-muted">If unchecked, system will auto-assign employee with least bookings</small>
            </label>
        </div>
    </div>

    <button type="submit" class="btn btn-primary" data-original-text="Add">
        <i class="fas fa-save"></i> Add Service
    </button>
</form>