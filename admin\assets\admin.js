/**
 * Admin Panel JavaScript
 * Handles all interactive functionality for the admin dashboard
 */

// Global variables
let currentModal = null;
let refreshInterval = null;
let notificationTimeout = null;

// Initialize admin panel when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeAdminPanel();
    startAutoRefresh();
    initializeResponsiveViewToggle();
});

/**
 * Initialize admin panel functionality
 */
function initializeAdminPanel() {
    // Initialize sidebar toggle for mobile
    initializeSidebarToggle();

    // Initialize form handlers (includes enhanced validation)
    initializeFormHandlers();

    // Initialize table functionality
    initializeTableFeatures();

    // Initialize notification system
    initializeNotifications();

    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();

    // Initialize tooltips
    initializeTooltips();

    // Admin panel initialized
    console.log('Admin panel initialized with enhanced modal system');
}

/**
 * Sidebar toggle functionality for mobile
 */
function initializeSidebarToggle() {
    // Get existing elements
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const overlay = document.getElementById('sidebar-overlay');

    // Add event listeners
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    if (overlay) {
        overlay.addEventListener('click', closeSidebar);
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 1024) {
            closeSidebar();
        }
    });

    // Handle escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeSidebar();
        }
    });
}

/**
 * Toggle sidebar visibility
 */
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');

    if (sidebar.classList.contains('open')) {
        closeSidebar();
    } else {
        openSidebar();
    }
}

/**
 * Open sidebar
 */
function openSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    if (sidebar) {
        sidebar.classList.add('open');
    }
    if (overlay) {
        overlay.classList.add('show');
    }
    document.body.style.overflow = 'hidden';
}

/**
 * Close sidebar
 */
function closeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebar-overlay');

    if (sidebar) {
        sidebar.classList.remove('open');
    }
    if (overlay) {
        overlay.classList.remove('show');
    }
    document.body.style.overflow = '';
}

/**
 * Modern Modal Manager Class
 */
class ModalManager {
    constructor() {
        this.currentModal = null;
        this.modalStack = [];
        this.templates = new Map();
        this.init();
    }

    init() {
        this.loadTemplates();
        this.bindGlobalEvents();
    }

    loadTemplates() {
        // Load modal templates
        const templates = ['modal-template', 'confirm-modal-template', 'alert-modal-template'];
        templates.forEach(templateId => {
            const template = document.getElementById(templateId);
            if (template) {
                this.templates.set(templateId, template);
            }
        });
    }

    bindGlobalEvents() {
        // Close modal when clicking overlay
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.close();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.currentModal) {
                this.close();
            }
        });

        // Handle modal close buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.modal-close')) {
                this.close();
            }
        });
    }

    createModalFromTemplate(templateId) {
        const template = this.templates.get(templateId);
        if (!template) {
            throw new Error(`Template ${templateId} not found`);
        }

        const clone = template.content.cloneNode(true);
        return clone.querySelector('.modal-overlay');
    }

    async open(title, contentUrl, options = {}) {
        try {
            const modal = this.createModalFromTemplate('modal-template');

            // Set title
            const titleElement = modal.querySelector('.modal-title');
            titleElement.textContent = title;

            // Configure modal
            this.configureModal(modal, options);

            // Add to DOM
            document.body.appendChild(modal);
            this.currentModal = modal;
            this.modalStack.push(modal);

            // Show loading state
            this.showLoading(modal);

            // Prevent body scroll
            document.body.style.overflow = 'hidden';

            // Show modal with animation
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });

            // Load content if URL provided
            if (contentUrl) {
                await this.loadContent(modal, contentUrl, options);
            }

            // Focus management for accessibility
            this.manageFocus(modal);

            return modal;
        } catch (error) {
            console.error('Error opening modal:', error);
            this.showError(modal || null, 'Failed to open modal');
        }
    }

    configureModal(modal, options) {
        // Set size
        if (options.size) {
            modal.querySelector('.modal').classList.add(`modal-${options.size}`);
        }

        // Set custom class
        if (options.className) {
            modal.classList.add(options.className);
        }

        // Configure footer
        if (options.showFooter) {
            const footer = modal.querySelector('.modal-footer');
            footer.style.display = 'block';
            if (options.footerContent) {
                footer.innerHTML = options.footerContent;
            }
        }
    }

    showLoading(modal) {
        const loading = modal.querySelector('.modal-loading');
        const content = modal.querySelector('.modal-content');
        loading.style.display = 'flex';
        content.style.display = 'none';
    }

    hideLoading(modal) {
        const loading = modal.querySelector('.modal-loading');
        const content = modal.querySelector('.modal-content');
        loading.style.display = 'none';
        content.style.display = 'block';
    }

    async loadContent(modal, url, options = {}) {
        try {
            const response = await fetch(url, {
                method: options.method || 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                body: options.data ? JSON.stringify(options.data) : null
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const html = await response.text();
            const contentContainer = modal.querySelector('.modal-content');
            contentContainer.innerHTML = html;

            this.hideLoading(modal);
            this.initializeModalContent(modal);

        } catch (error) {
            console.error('Error loading modal content:', error);
            this.showError(modal, 'Failed to load content. Please try again.');
        }
    }

    initializeModalContent(modal) {
        // Initialize forms in the modal
        const forms = modal.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e, modal));
        });

        // Execute any scripts in the loaded content
        const scripts = modal.querySelectorAll('script');
        scripts.forEach(script => {
            const newScript = document.createElement('script');
            if (script.src) {
                newScript.src = script.src;
            } else {
                newScript.textContent = script.textContent;
            }
            document.head.appendChild(newScript);

            // Clean up after execution
            setTimeout(() => {
                if (newScript.parentNode) {
                    newScript.parentNode.removeChild(newScript);
                }
            }, 100);
        });

        // Trigger custom event for modal content loaded
        const event = new CustomEvent('modalContentLoaded', {
            detail: { modal, container: modal.querySelector('.modal-content') }
        });
        document.dispatchEvent(event);
    }

    showError(modal, message) {
        const content = modal ? modal.querySelector('.modal-content') : null;
        const errorHtml = `
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                ${message}
            </div>
        `;

        if (content) {
            content.innerHTML = errorHtml;
            this.hideLoading(modal);
        } else {
            this.showAlert('Error', message, 'error');
        }
    }

    manageFocus(modal) {
        // Store the previously focused element
        modal._previousFocus = document.activeElement;

        // Focus the first focusable element in the modal
        const focusableElements = modal.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        }
    }

    close() {
        if (!this.currentModal) return;

        const modal = this.currentModal;

        // Remove from stack
        this.modalStack = this.modalStack.filter(m => m !== modal);

        // Hide modal with animation
        modal.classList.remove('show');

        setTimeout(() => {
            // Restore focus
            if (modal._previousFocus) {
                modal._previousFocus.focus();
            }

            // Remove from DOM
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }

            // Update current modal
            this.currentModal = this.modalStack.length > 0 ?
                this.modalStack[this.modalStack.length - 1] : null;

            // Restore body scroll if no modals are open
            if (this.modalStack.length === 0) {
                document.body.style.overflow = '';
            }
        }, 300);
    }

    async handleFormSubmit(e, modal) {
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');

        // Show loading state on submit button
        if (submitBtn) {
            submitBtn.disabled = true;
            const originalText = submitBtn.textContent;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            submitBtn._originalText = originalText;
        }

        try {
            const response = await fetch(form.action || window.location.href, {
                method: form.method || 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            const data = await response.json();

            if (data.success) {
                showNotification(data.message || 'Operation completed successfully', 'success');
                this.close();

                // Refresh page or specific content
                if (data.refresh) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            } else {
                showNotification(data.message || 'An error occurred', 'error');

                // Display field-specific errors
                if (data.errors) {
                    Object.keys(data.errors).forEach(fieldName => {
                        const field = form.querySelector(`[name="${fieldName}"]`);
                        if (field) {
                            const validator = new FormValidator();
                            validator.displayFieldErrors(field, [data.errors[fieldName]]);
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Form submission error:', error);
            showNotification('An error occurred while processing your request', 'error');
        } finally {
            // Restore submit button
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = submitBtn._originalText || 'Submit';
            }
        }
    }

    // Confirmation modal
    confirm(title, message, options = {}) {
        return new Promise((resolve) => {
            const modal = this.createModalFromTemplate('confirm-modal-template');

            // Set content
            modal.querySelector('.modal-title').textContent = title;
            modal.querySelector('.confirm-message').textContent = message;

            // Configure buttons
            const confirmBtn = modal.querySelector('.confirm-btn');
            const cancelBtn = modal.querySelector('.cancel-btn');

            if (options.confirmText) {
                confirmBtn.textContent = options.confirmText;
            }
            if (options.cancelText) {
                cancelBtn.textContent = options.cancelText;
            }
            if (options.confirmClass) {
                confirmBtn.className = `btn ${options.confirmClass}`;
            }

            // Handle button clicks
            confirmBtn.addEventListener('click', () => {
                this.close();
                resolve(true);
            });

            cancelBtn.addEventListener('click', () => {
                this.close();
                resolve(false);
            });

            // Add to DOM and show
            document.body.appendChild(modal);
            this.currentModal = modal;
            this.modalStack.push(modal);
            document.body.style.overflow = 'hidden';

            requestAnimationFrame(() => {
                modal.classList.add('show');
            });

            this.manageFocus(modal);
        });
    }

    // Alert modal
    alert(title, message, type = 'info') {
        return new Promise((resolve) => {
            const modal = this.createModalFromTemplate('alert-modal-template');

            // Set content
            modal.querySelector('.modal-title').textContent = title;
            modal.querySelector('.alert-message').textContent = message;

            // Set icon based on type
            const icon = modal.querySelector('.alert-icon i');
            const iconClasses = {
                'info': 'fas fa-info-circle',
                'success': 'fas fa-check-circle',
                'warning': 'fas fa-exclamation-triangle',
                'error': 'fas fa-times-circle'
            };
            icon.className = iconClasses[type] || iconClasses.info;

            // Handle OK button
            const okBtn = modal.querySelector('.ok-btn');
            okBtn.addEventListener('click', () => {
                this.close();
                resolve();
            });

            // Add to DOM and show
            document.body.appendChild(modal);
            this.currentModal = modal;
            this.modalStack.push(modal);
            document.body.style.overflow = 'hidden';

            requestAnimationFrame(() => {
                modal.classList.add('show');
            });

            this.manageFocus(modal);
        });
    }

    // Show alert alias for backward compatibility
    showAlert(title, message, type = 'info') {
        return this.alert(title, message, type);
    }
}

// Global modal instance
let modalManager;

// Initialize modal manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    modalManager = new ModalManager();
});

// Backward compatibility functions
function openModal(title, contentUrl, options = {}) {
    if (!modalManager) {
        console.error('Modal manager not initialized');
        return;
    }
    return modalManager.open(title, contentUrl, options);
}

function closeModal() {
    if (!modalManager) {
        console.error('Modal manager not initialized');
        return;
    }
    modalManager.close();
}

function confirmAction(message, callback, options = {}) {
    if (!modalManager) {
        console.error('Modal manager not initialized');
        return;
    }

    const title = options.title || 'Confirm Action';
    modalManager.confirm(title, message, options).then(confirmed => {
        if (confirmed && callback) {
            callback();
        }
    });
}

function showAlert(title, message, type = 'info') {
    if (!modalManager) {
        console.error('Modal manager not initialized');
        return;
    }
    return modalManager.alert(title, message, type);
}

/**
 * Enhanced Form Validation and Handling
 */
class FormValidator {
    constructor() {
        this.rules = new Map();
        this.messages = new Map();
        this.init();
    }

    init() {
        this.setupDefaultRules();
        this.setupDefaultMessages();
        this.bindEvents();
    }

    setupDefaultRules() {
        this.rules.set('required', (value) => value.trim() !== '');
        this.rules.set('email', (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value));
        this.rules.set('phone', (value) => /^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/\s/g, '')));
        this.rules.set('min', (value, min) => value.length >= parseInt(min));
        this.rules.set('max', (value, max) => value.length <= parseInt(max));
        this.rules.set('numeric', (value) => /^\d+$/.test(value));
        this.rules.set('decimal', (value) => /^\d+(\.\d{1,2})?$/.test(value));
        this.rules.set('url', (value) => {
            try {
                new URL(value);
                return true;
            } catch {
                return false;
            }
        });
    }

    setupDefaultMessages() {
        this.messages.set('required', 'This field is required');
        this.messages.set('email', 'Please enter a valid email address');
        this.messages.set('phone', 'Please enter a valid phone number');
        this.messages.set('min', 'Minimum length is {0} characters');
        this.messages.set('max', 'Maximum length is {0} characters');
        this.messages.set('numeric', 'Please enter numbers only');
        this.messages.set('decimal', 'Please enter a valid decimal number');
        this.messages.set('url', 'Please enter a valid URL');
    }

    bindEvents() {
        // Real-time validation on input
        document.addEventListener('input', (e) => {
            if (e.target.hasAttribute('data-validate-field')) {
                this.validateField(e.target);
            }
        });

        // Form submission validation
        document.addEventListener('submit', (e) => {
            if (e.target.hasAttribute('data-validate')) {
                if (!this.validateForm(e.target)) {
                    e.preventDefault();
                }
            }
        });
    }

    validateField(field) {
        const rules = field.getAttribute('data-validate-field').split('|');
        const errors = [];

        for (const rule of rules) {
            const [ruleName, ...params] = rule.split(':');
            const ruleFunction = this.rules.get(ruleName);

            if (ruleFunction) {
                const isValid = params.length > 0 ?
                    ruleFunction(field.value, ...params) :
                    ruleFunction(field.value);

                if (!isValid) {
                    let message = this.messages.get(ruleName) || 'Invalid input';
                    // Replace placeholders in message
                    params.forEach((param, index) => {
                        message = message.replace(`{${index}}`, param);
                    });
                    errors.push(message);
                }
            }
        }

        this.displayFieldErrors(field, errors);
        return errors.length === 0;
    }

    validateForm(form) {
        const fields = form.querySelectorAll('[data-validate-field]');
        let isValid = true;

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    displayFieldErrors(field, errors) {
        // Remove existing error messages
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        // Remove error styling
        field.classList.remove('error');

        if (errors.length > 0) {
            // Add error styling
            field.classList.add('error');

            // Create error message element
            const errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            errorElement.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${errors[0]}`;

            // Insert after the field
            field.parentNode.insertBefore(errorElement, field.nextSibling);
        }
    }

    addRule(name, validator, message) {
        this.rules.set(name, validator);
        this.messages.set(name, message);
    }
}

/**
 * Form handlers
 */
function initializeFormHandlers() {
    // Initialize form validator
    const formValidator = new FormValidator();

    // Auto-save forms with debouncing
    const autoSaveForms = document.querySelectorAll('[data-auto-save]');
    autoSaveForms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea');
        let saveTimeout;

        inputs.forEach(input => {
            input.addEventListener('input', () => {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    autoSaveForm(form);
                }, 1000); // Save after 1 second of inactivity
            });
        });
    });

    // Enhanced form submission with loading states
    document.addEventListener('submit', (e) => {
        const form = e.target;
        if (form.hasAttribute('data-enhanced-submit')) {
            e.preventDefault();
            handleEnhancedFormSubmit(form);
        }
    });
}

/**
 * Auto-save form data
 */
async function autoSaveForm(form) {
    try {
        const formData = new FormData(form);
        formData.append('auto_save', '1');

        const response = await fetch(form.action || window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (response.ok) {
            showNotification('Changes saved automatically', 'success', 2000);
        }
    } catch (error) {
        console.error('Auto-save failed:', error);
    }
}

/**
 * Form validation
 */
function validateForm(e) {
    const form = e.target;
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        showNotification('Please fill in all required fields', 'warning');
    }
}

/**
 * Enhanced form submission handler
 */
async function handleEnhancedFormSubmit(form) {
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn ? submitBtn.textContent : '';

    try {
        // Show loading state
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        }

        const formData = new FormData(form);
        const response = await fetch(form.action || window.location.href, {
            method: form.method || 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            showNotification(data.message || 'Operation completed successfully', 'success');

            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1000);
            } else if (data.refresh) {
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        } else {
            showNotification(data.message || 'An error occurred', 'error');

            // Display field-specific errors
            if (data.errors) {
                Object.keys(data.errors).forEach(fieldName => {
                    const field = form.querySelector(`[name="${fieldName}"]`);
                    if (field) {
                        const validator = new FormValidator();
                        validator.displayFieldErrors(field, [data.errors[fieldName]]);
                    }
                });
            }
        }
    } catch (error) {
        console.error('Form submission error:', error);
        showNotification('An error occurred while processing your request', 'error');
    } finally {
        // Restore submit button
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        }
    }
}

/**
 * Table functionality
 */
function initializeTableFeatures() {
    // Sortable tables
    const sortableTables = document.querySelectorAll('.table[data-sortable]');
    sortableTables.forEach(table => {
        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => sortTable(table, header));
        });
    });
    
    // Searchable tables
    const searchInputs = document.querySelectorAll('[data-table-search]');
    searchInputs.forEach(input => {
        const tableId = input.getAttribute('data-table-search');
        const table = document.getElementById(tableId);
        if (table) {
            input.addEventListener('input', () => searchTable(table, input.value));
        }
    });
}

/**
 * Sort table by column
 */
function sortTable(table, header) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const sortOrder = header.getAttribute('data-sort-order') === 'asc' ? 'desc' : 'asc';
    
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();
        
        // Try to parse as numbers
        const aNum = parseFloat(aValue);
        const bNum = parseFloat(bValue);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return sortOrder === 'asc' ? aNum - bNum : bNum - aNum;
        }
        
        // Sort as strings
        return sortOrder === 'asc' ? 
            aValue.localeCompare(bValue) : 
            bValue.localeCompare(aValue);
    });
    
    // Update table
    rows.forEach(row => tbody.appendChild(row));
    
    // Update sort indicators
    table.querySelectorAll('th[data-sort]').forEach(th => {
        th.removeAttribute('data-sort-order');
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    header.setAttribute('data-sort-order', sortOrder);
    header.classList.add(`sort-${sortOrder}`);
}

/**
 * Search table content
 */
function searchTable(table, searchTerm) {
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');
    const term = searchTerm.toLowerCase();
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(term) ? '' : 'none';
    });
}

/**
 * Notification system
 */
function initializeNotifications() {
    // Create notification container if it doesn't exist
    if (!document.querySelector('.notification-container')) {
        const container = document.createElement('div');
        container.className = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 3000;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info', duration = 5000) {
    // Initialize notifications if not already done
    initializeNotifications();
    
    const container = document.querySelector('.notification-container');
    if (!container) {
        console.error('Notification container not found');
        return;
    }
    
    const notification = document.createElement('div');
    
    const icons = {
        success: 'check-circle',
        error: 'exclamation-triangle',
        warning: 'exclamation-circle',
        info: 'info-circle'
    };
    
    notification.className = `alert alert-${type}`;
    notification.style.cssText = `
        margin-bottom: 10px;
        animation: slideInRight 0.3s ease-out;
        cursor: pointer;
    `;
    
    notification.innerHTML = `
        <i class="fas fa-${icons[type] || 'info-circle'}"></i>
        ${message}
    `;
    
    // Auto-remove notification
    const timeout = setTimeout(() => {
        removeNotification(notification);
    }, duration);
    
    // Remove on click
    notification.addEventListener('click', () => {
        clearTimeout(timeout);
        removeNotification(notification);
    });
    
    container.appendChild(notification);
}

/**
 * Remove notification
 */
function removeNotification(notification) {
    notification.style.animation = 'slideOutRight 0.3s ease-in';
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

/**
 * Keyboard shortcuts
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + shortcuts
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'n':
                    e.preventDefault();
                    openModal('New Reservation', 'views/modals/add_reservation.php');
                    break;
                case 'f':
                    e.preventDefault();
                    const searchInput = document.querySelector('[data-table-search]');
                    if (searchInput) searchInput.focus();
                    break;
                case 's':
                    e.preventDefault();
                    const autoSaveForm = document.querySelector('form[data-auto-save]');
                    if (autoSaveForm) autoSaveForm(autoSaveForm);
                    break;
            }
        }
    });
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

/**
 * Show tooltip
 */
function showTooltip(e) {
    const element = e.target;
    const text = element.getAttribute('data-tooltip');
    
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 0.8rem;
        z-index: 4000;
        pointer-events: none;
        white-space: nowrap;
        opacity: 0;
        transition: opacity 0.2s ease;
    `;
    
    document.body.appendChild(tooltip);
    
    // Position tooltip
    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();
    
    let top = rect.top - tooltipRect.height - 8;
    let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
    
    // Adjust if tooltip goes off screen
    if (top < 0) {
        top = rect.bottom + 8;
    }
    if (left < 0) {
        left = 8;
    }
    if (left + tooltipRect.width > window.innerWidth) {
        left = window.innerWidth - tooltipRect.width - 8;
    }
    
    tooltip.style.top = top + 'px';
    tooltip.style.left = left + 'px';
    
    // Show tooltip
    setTimeout(() => {
        tooltip.style.opacity = '1';
    }, 10);
    
    element._tooltip = tooltip;
}

/**
 * Hide tooltip
 */
function hideTooltip(e) {
    const element = e.target;
    if (element._tooltip) {
        element._tooltip.style.opacity = '0';
        setTimeout(() => {
            if (element._tooltip && element._tooltip.parentNode) {
                document.body.removeChild(element._tooltip);
            }
            element._tooltip = null;
        }, 200);
    }
}

/**
 * Auto-refresh functionality
 */
function startAutoRefresh() {
    // Refresh dashboard data every 30 seconds
    refreshInterval = setInterval(() => {
        refreshDashboardData();
    }, 30000);
}

/**
 * Refresh dashboard data
 */
function refreshDashboardData() {
    const dashboardElements = document.querySelectorAll('[data-auto-refresh]');
    
    dashboardElements.forEach(element => {
        const url = element.getAttribute('data-refresh-url');
        if (url) {
            fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.text())
            .then(html => {
                element.innerHTML = html;
            })
            .catch(error => {
                console.error('Auto-refresh error:', error);
            });
        }
    });
}

/**
 * Confirmation dialogs
 */
function confirmAction(message, callback) {
    const overlay = document.createElement('div');
    overlay.className = 'modal-overlay';
    
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.maxWidth = '400px';
    
    modal.innerHTML = `
        <div class="modal-header">
            <h3>Confirm Action</h3>
        </div>
        <div class="modal-body">
            <p style="margin-bottom: 20px;">${message}</p>
            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                <button class="btn btn-secondary" onclick="closeConfirmDialog()">Cancel</button>
                <button class="btn btn-danger" onclick="executeConfirmAction()">Confirm</button>
            </div>
        </div>
    `;
    
    overlay.appendChild(modal);
    document.body.appendChild(overlay);
    
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);
    
    // Store callback
    window._confirmCallback = callback;
    window._confirmOverlay = overlay;
    
    document.body.style.overflow = 'hidden';
}

/**
 * Close confirmation dialog
 */
function closeConfirmDialog() {
    if (window._confirmOverlay) {
        window._confirmOverlay.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(window._confirmOverlay);
            window._confirmOverlay = null;
            window._confirmCallback = null;
            document.body.style.overflow = '';
        }, 300);
    }
}

/**
 * Execute confirmed action
 */
function executeConfirmAction() {
    if (window._confirmCallback) {
        window._confirmCallback();
    }
    closeConfirmDialog();
}

/**
 * Export functionality
 */
function exportData(type, format = 'csv') {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'export.php';
    form.style.display = 'none';
    
    const typeInput = document.createElement('input');
    typeInput.name = 'export_type';
    typeInput.value = type;
    
    const formatInput = document.createElement('input');
    formatInput.name = 'export_format';
    formatInput.value = format;
    
    form.appendChild(typeInput);
    form.appendChild(formatInput);
    document.body.appendChild(form);
    
    form.submit();
    document.body.removeChild(form);
    
    showNotification(`Exporting ${type} data...`, 'info');
}

/**
 * Quick actions
 */
function quickAddReservation() {
    openModal('Quick Add Reservation', 'views/modals/quick_add_reservation.php', { size: 'lg' });
}

function quickAddService() {
    openModal('Add New Service', 'views/modals/add_service.php', { size: 'lg' });
}

function viewSystemStatus() {
    openModal('System Status', 'views/modals/system_status.php');
}

function openSettings() {
    openModal('Settings', 'views/modals/settings.php');
}

/**
 * Reservation management
 */
function viewReservation(reservationId) {
    openModal('View Reservation', `views/modals/view_reservation.php?id=${reservationId}`);
}

function editReservation(reservationId) {
    openModal('Edit Reservation', `views/modals/edit_reservation.php?id=${reservationId}`);
}

function cancelReservation(reservationId) {
    confirmAction('Are you sure you want to cancel this reservation?', () => {
        fetch('actions/cancel_reservation.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ reservation_id: reservationId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Reservation cancelled successfully', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Error cancelling reservation', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error cancelling reservation', 'error');
        });
    });
}

function completeReservation(reservationId) {
    confirmAction('Mark this reservation as completed?', () => {
        fetch('actions/complete_reservation.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ reservation_id: reservationId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Reservation marked as completed', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Error updating reservation', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error updating reservation', 'error');
        });
    });
}

/**
 * Service management
 */
function viewService(serviceId) {
    openModal('Service Details', `views/modals/view_service.php?id=${serviceId}`);
}

function editService(serviceId) {
    openModal('Edit Service', `views/modals/edit_service.php?id=${serviceId}`);
}

function deleteService(serviceId) {
    confirmAction('Are you sure you want to delete this service? This action cannot be undone.', () => {
        fetch('actions/delete_service.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ service_id: serviceId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Service deleted successfully', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Error deleting service', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting service', 'error');
        });
    });
}

// Employee Management Functions
function quickAddEmployee() {
    openModal('Add New Employee', 'views/modals/add_employee.php', { size: 'lg' });
}

function viewEmployee(employeeId) {
    console.log('viewEmployee called with ID:', employeeId);
    openModal('Employee Details', `views/modals/view_employee.php?id=${employeeId}`, { size: 'lg' });
}

function editEmployee(employeeId) {
    console.log('editEmployee called with ID:', employeeId);
    openModal('Edit Employee', `views/modals/edit_employee.php?id=${employeeId}`, { size: 'lg' });
}

function deleteEmployee(employeeId) {
    confirmAction('Are you sure you want to delete this employee? This action cannot be undone.', () => {
        fetch('actions/delete_employee.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ employee_id: employeeId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Employee deleted successfully', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message || 'Error deleting employee', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting employee', 'error');
        });
    });
}

/**
 * Customer management
 */
function viewCustomer(customerId) {
    openModal('Customer Details', `views/modals/customer_details.php?id=${customerId}`, { size: 'lg' });
}

function editCustomer(customerId) {
    openModal('Edit Customer', `views/modals/edit_customer.php?id=${customerId}`, { size: 'lg' });
}

function deleteCustomer(customerId) {
    confirmAction('Are you sure you want to delete this customer? This action cannot be undone.', () => {
        fetch('actions/delete_customer.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ customer_id: customerId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Customer deleted successfully', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else if (data.has_future_reservations) {
                // Add a small delay to ensure the first dialog is fully closed
                setTimeout(() => {
                    confirmAction(data.message + '\n\nDo you want to proceed and cancel all upcoming reservations?', () => {
                        forceDeleteCustomer(customerId);
                    });
                }, 300); // 300ms delay
            } else {
                showNotification(data.message || 'Error deleting customer', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting customer', 'error');
        });
    });
}

function forceDeleteCustomer(customerId) {
    fetch('actions/delete_customer.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ customer_id: customerId, force_delete: true })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Customer and reservations deleted successfully', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showNotification(data.message || 'Error deleting customer', 'error');
        }
    })
    .catch(error => {
        showNotification('Error deleting customer', 'error');
    });
}




/**
 * Utility functions
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatTime(timeString) {
    const [hours, minutes] = timeString.split(':');
    const date = new Date();
    date.setHours(parseInt(hours), parseInt(minutes));
    return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

/**
 * Chart functionality (simple bar charts)
 */
function createSimpleChart(containerId, data, options = {}) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const maxValue = Math.max(...data.map(item => item.value));
    const chartHeight = options.height || 200;
    
    container.innerHTML = '';
    container.className = 'chart-container';
    
    data.forEach(item => {
        const barContainer = document.createElement('div');
        barContainer.className = 'chart-bar';
        
        const bar = document.createElement('div');
        bar.className = 'bar';
        const height = (item.value / maxValue) * (chartHeight - 50);
        bar.style.height = height + 'px';
        
        const label = document.createElement('div');
        label.className = 'bar-label';
        label.innerHTML = `<strong>${item.value}</strong><span>${item.label}</span>`;
        
        barContainer.appendChild(bar);
        barContainer.appendChild(label);
        container.appendChild(barContainer);
    });
}

/**
 * Print functionality
 */
function printPage() {
    window.print();
}

function printElement(elementId) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Print</title>
            <link rel="stylesheet" href="assets/admin.css">
            <style>
                body { margin: 20px; }
                .no-print { display: none !important; }
            </style>
        </head>
        <body>
            ${element.outerHTML}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

/**
 * Initialize responsive view toggle - auto-select cards on mobile, table on desktop
 */
function initializeResponsiveViewToggle() {
    function setResponsiveView() {
        const isMobile = window.innerWidth <= 768;
        const viewToggleButtons = document.querySelectorAll('.view-toggle .btn');
        const tableView = document.querySelector('.table-view');
        const cardView = document.querySelector('.card-view');

        if (viewToggleButtons.length > 0) {
            // Find the cards and table buttons
            const cardsBtn = Array.from(viewToggleButtons).find(btn =>
                btn.textContent.includes('Cards') || btn.querySelector('i.fa-th')
            );
            const tableBtn = Array.from(viewToggleButtons).find(btn =>
                btn.textContent.includes('Table') || btn.querySelector('i.fa-table')
            );

            if (isMobile && cardsBtn) {
                // Auto-select cards on mobile
                if (!cardsBtn.classList.contains('active')) {
                    cardsBtn.click();
                }
            } else if (!isMobile && tableBtn) {
                // Auto-select table on desktop
                if (!tableBtn.classList.contains('active')) {
                    tableBtn.click();
                }
            }
        }
    }

    // Set initial view
    setResponsiveView();

    // Update view on window resize
    let resizeTimeout;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(setResponsiveView, 250);
    });
}

/**
 * Cleanup on page unload
 */
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    if (notificationTimeout) {
        clearTimeout(notificationTimeout);
    }
});

/**
 * Error handling
 */
window.addEventListener('error', function(e) {
    console.error('JavaScript error:', e.error);
    showNotification('An unexpected error occurred', 'error');
});

/**
 * Add CSS animations
 */
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .mobile-menu-btn {
        display: none;
        background: var(--primary);
        color: white;
        border: none;
        padding: 10px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 15px;
    }
    
    @media (max-width: 1024px) {
        .mobile-menu-btn {
            display: block;
        }
    }
    
`;
document.head.appendChild(style);

// Export functions for global access
window.AdminPanel = {
    openModal,
    closeModal,
    confirmAction,
    showAlert,
    showNotification,
    exportData,
    quickAddReservation,
    quickAddService,
    quickAddEmployee,
    viewSystemStatus,
    openSettings,
    editReservation,
    cancelReservation,
    completeReservation,
    viewService,
    editService,
    deleteService,
    viewEmployee,
    editEmployee,
    deleteEmployee,
    viewCustomer,
    editCustomer,
    formatCurrency,
    formatDate,
    formatTime,
    createSimpleChart,
    printPage,
    printElement,
    refreshDashboardData,
    toggleSidebar,
    // New enhanced modal methods
    get modalManager() { return modalManager; }
};

// Make functions available globally for onclick handlers
window.openModal = openModal;
window.closeModal = closeModal;
window.showNotification = showNotification;
window.confirmAction = confirmAction;
window.closeConfirmDialog = closeConfirmDialog;
window.executeConfirmAction = executeConfirmAction;
window.exportData = exportData;
window.quickAddReservation = quickAddReservation;
window.quickAddService = quickAddService;
window.viewSystemStatus = viewSystemStatus;
window.openSettings = openSettings;
window.editReservation = editReservation;
window.cancelReservation = cancelReservation;
window.completeReservation = completeReservation;
window.editService = editService;
window.deleteService = deleteService;
window.viewEmployee = viewEmployee;
window.editEmployee = editEmployee;
window.deleteEmployee = deleteEmployee;
window.viewCustomer = viewCustomer;
window.editCustomer = editCustomer;
window.printPage = printPage;
window.printElement = printElement;
window.toggleSidebar = toggleSidebar;

// Admin Panel JavaScript loaded
