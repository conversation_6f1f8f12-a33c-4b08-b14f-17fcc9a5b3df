<?php

/**
 * Migration: Add allow_employee_selection field to services table
 * 
 * This migration adds a new boolean field to control whether customers
 * can select employees for each service or if the system should auto-assign.
 * 
 * Default: FALSE (auto-assignment enabled)
 */

require_once __DIR__ . '/../Database.php';

function migrate_add_service_employee_selection(): bool
{
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        echo "Running migration: Add allow_employee_selection to services table...\n";

        // Check if column already exists
        $result = $conn->query("PRAGMA table_info(services)");
        $columnExists = false;
        
        while ($column = $result->fetchArray(SQLITE3_ASSOC)) {
            if ($column['name'] === 'allow_employee_selection') {
                $columnExists = true;
                break;
            }
        }

        if ($columnExists) {
            echo "✓ Column 'allow_employee_selection' already exists\n";
            return true;
        }

        // Add the new column with default value FALSE (auto-assignment enabled)
        $conn->exec("ALTER TABLE services ADD COLUMN allow_employee_selection BOOLEAN DEFAULT 0");

        // Verify the column was added
        $result = $conn->query("PRAGMA table_info(services)");
        $columnAdded = false;
        
        while ($column = $result->fetchArray(SQLITE3_ASSOC)) {
            if ($column['name'] === 'allow_employee_selection') {
                $columnAdded = true;
                break;
            }
        }

        if ($columnAdded) {
            echo "✓ Successfully added 'allow_employee_selection' column to services table\n";
            echo "✓ Default value: FALSE (auto-assignment enabled)\n";
            return true;
        } else {
            echo "✗ Failed to add column\n";
            return false;
        }

    } catch (Exception $e) {
        echo "✗ Migration failed: " . $e->getMessage() . "\n";
        return false;
    }
}

// Run migration if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $result = migrate_add_service_employee_selection();
    exit($result ? 0 : 1);
}
