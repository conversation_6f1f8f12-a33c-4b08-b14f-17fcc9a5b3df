<?php
require_once '../../includes/config.php';
require_once '../../includes/unified_text_manager.php';

header('Content-Type: application/json');

try {
    // Get the request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['email_type'])) {
        throw new Exception('Email type not specified');
    }
    
    $emailType = $input['email_type'];
    
    // Validate email type
    $validTypes = ['confirmation', 'cancellation', 'reminder', 'welcome', 'verification'];
    if (!in_array($emailType, $validTypes)) {
        throw new Exception('Invalid email type');
    }
    
    // Get current email texts
    $currentTexts = UnifiedTextManager::getEmailTexts();
    
    // Get original default texts
    $originalTexts = UnifiedTextManager::getOriginalEmailTexts();
    
    // Reset only the specified email type
    $htmlKey = $emailType . '_html';
    $currentTexts['en'][$htmlKey] = $originalTexts['en'][$htmlKey];
    $currentTexts['el'][$htmlKey] = $originalTexts['el'][$htmlKey];
    
    // Save the updated texts
    $result = UnifiedTextManager::saveEmailTexts($currentTexts);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => ucfirst($emailType) . ' email reset to default successfully'
        ]);
    } else {
        throw new Exception('Failed to save email texts');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
