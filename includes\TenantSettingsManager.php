<?php

/**
 * Tenant Settings Manager
 * 
 * Manages tenant-specific settings and texts in the database
 * instead of PHP files, ensuring proper multi-tenant isolation.
 */

require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/TenantContext.php';

class TenantSettingsManager
{
    /**
     * Get tenant setting
     */
    public static function getSetting(string $key, $default = null)
    {
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            $db = Database::getInstance();
            $conn = $db->getConnection();

            $stmt = $conn->prepare("SELECT setting_value FROM tenant_settings WHERE tenant_id = :tenant_id AND setting_key = :key");
            $stmt->bindValue(':tenant_id', $tenantId);
            $stmt->bindValue(':key', $key);
            $result = $stmt->execute();

            $row = $result->fetchArray(SQLITE3_ASSOC);
            if ($row) {
                // Try to decode JSON, fallback to string
                $decoded = json_decode($row['setting_value'], true);
                return $decoded !== null ? $decoded : $row['setting_value'];
            }

            return $default;
        } catch (Exception $e) {
            error_log('Failed to get tenant setting: ' . $e->getMessage());
            return $default;
        }
    }

    /**
     * Set tenant setting
     */
    public static function setSetting(string $key, $value): bool
    {
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            error_log("TenantSettingsManager::setSetting - tenantId: $tenantId, key: $key, value: " . json_encode($value));

            $db = Database::getInstance();
            $conn = $db->getConnection();

            // Encode value as JSON if it's an array/object
            $encodedValue = is_array($value) || is_object($value) ? json_encode($value) : $value;

            $now = date('Y-m-d H:i:s');

            $stmt = $conn->prepare("
                INSERT OR REPLACE INTO tenant_settings 
                (tenant_id, setting_key, setting_value, created_at, updated_at) 
                VALUES (:tenant_id, :key, :value, :created_at, :updated_at)
            ");

            $stmt->bindValue(':tenant_id', $tenantId);
            $stmt->bindValue(':key', $key);
            $stmt->bindValue(':value', $encodedValue);
            $stmt->bindValue(':created_at', $now);
            $stmt->bindValue(':updated_at', $now);

            $result = $stmt->execute();
            $success = $result !== false;

            error_log("TenantSettingsManager::setSetting - SQL result: " . ($success ? 'success' : 'failed'));

            return $success;
        } catch (Exception $e) {
            error_log('Failed to set tenant setting: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Get all tenant settings
     */
    public static function getAllSettings(): array
    {
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            error_log("TenantSettingsManager::getAllSettings - Using tenant: $tenantId");

            $db = Database::getInstance();
            $conn = $db->getConnection();

            $stmt = $conn->prepare("SELECT setting_key, setting_value FROM tenant_settings WHERE tenant_id = :tenant_id");
            $stmt->bindValue(':tenant_id', $tenantId);
            $result = $stmt->execute();

            $settings = [];
            while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                $decoded = json_decode($row['setting_value'], true);
                $settings[$row['setting_key']] = $decoded !== null ? $decoded : $row['setting_value'];
            }

            error_log("TenantSettingsManager::getAllSettings - Found " . count($settings) . " settings");
            return $settings;
        } catch (Exception $e) {
            error_log('Failed to get all tenant settings: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            return [];
        }
    }

    /**
     * Get tenant text
     */
    public static function getText(string $textType, string $language, string $textKey, $default = null)
    {
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            $db = Database::getInstance();
            $conn = $db->getConnection();

            $stmt = $conn->prepare("
                SELECT text_value FROM tenant_texts 
                WHERE tenant_id = :tenant_id AND text_type = :type AND language = :lang AND text_key = :key
            ");
            $stmt->bindValue(':tenant_id', $tenantId);
            $stmt->bindValue(':type', $textType);
            $stmt->bindValue(':lang', $language);
            $stmt->bindValue(':key', $textKey);
            $result = $stmt->execute();

            $row = $result->fetchArray(SQLITE3_ASSOC);
            return $row ? $row['text_value'] : $default;
        } catch (Exception $e) {
            error_log('Failed to get tenant text: ' . $e->getMessage());
            return $default;
        }
    }

    /**
     * Set tenant text
     */
    public static function setText(string $textType, string $language, string $textKey, string $textValue): bool
    {
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            $db = Database::getInstance();
            $conn = $db->getConnection();

            $now = date('Y-m-d H:i:s');

            $stmt = $conn->prepare("
                INSERT OR REPLACE INTO tenant_texts 
                (tenant_id, text_type, language, text_key, text_value, created_at, updated_at) 
                VALUES (:tenant_id, :type, :lang, :key, :value, :created_at, :updated_at)
            ");

            $stmt->bindValue(':tenant_id', $tenantId);
            $stmt->bindValue(':type', $textType);
            $stmt->bindValue(':lang', $language);
            $stmt->bindValue(':key', $textKey);
            $stmt->bindValue(':value', $textValue);
            $stmt->bindValue(':created_at', $now);
            $stmt->bindValue(':updated_at', $now);

            return $stmt->execute();
        } catch (Exception $e) {
            error_log('Failed to set tenant text: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all texts for a specific type and language
     */
    public static function getAllTexts(string $textType, string $language): array
    {
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            $db = Database::getInstance();
            $conn = $db->getConnection();

            $stmt = $conn->prepare("
                SELECT text_key, text_value FROM tenant_texts 
                WHERE tenant_id = :tenant_id AND text_type = :type AND language = :lang
            ");
            $stmt->bindValue(':tenant_id', $tenantId);
            $stmt->bindValue(':type', $textType);
            $stmt->bindValue(':lang', $language);
            $result = $stmt->execute();

            $texts = [];
            while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
                $texts[$row['text_key']] = $row['text_value'];
            }

            return $texts;
        } catch (Exception $e) {
            error_log('Failed to get all tenant texts: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Set multiple texts at once
     */
    public static function setMultipleTexts(string $textType, string $language, array $texts): bool
    {
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            $db = Database::getInstance();
            $conn = $db->getConnection();

            $conn->exec('BEGIN TRANSACTION');

            $now = date('Y-m-d H:i:s');

            $stmt = $conn->prepare("
                INSERT OR REPLACE INTO tenant_texts 
                (tenant_id, text_type, language, text_key, text_value, created_at, updated_at) 
                VALUES (:tenant_id, :type, :lang, :key, :value, :created_at, :updated_at)
            ");

            foreach ($texts as $key => $value) {
                $stmt->bindValue(':tenant_id', $tenantId);
                $stmt->bindValue(':type', $textType);
                $stmt->bindValue(':lang', $language);
                $stmt->bindValue(':key', $key);
                $stmt->bindValue(':value', $value);
                $stmt->bindValue(':created_at', $now);
                $stmt->bindValue(':updated_at', $now);
                $stmt->execute();
            }

            $conn->exec('COMMIT');
            return true;
        } catch (Exception $e) {
            if (isset($conn)) {
                $conn->exec('ROLLBACK');
            }
            error_log('Failed to set multiple tenant texts: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Initialize default settings for a tenant
     */
    public static function initializeDefaultSettings(string $tenantId): bool
    {
        try {
            $db = Database::getInstance();
            $conn = $db->getConnection();

            $now = date('Y-m-d H:i:s');

            // Default settings
            $defaultSettings = [
                'site_name' => 'Booking System',
                'email_from' => '<EMAIL>',
                'admin_email' => '<EMAIL>',
                'business_hours' => json_encode([
                    'Monday' => [['start' => '09:00', 'end' => '17:00']],
                    'Tuesday' => [['start' => '09:00', 'end' => '17:00']],
                    'Wednesday' => [['start' => '09:00', 'end' => '17:00']],
                    'Thursday' => [['start' => '09:00', 'end' => '17:00']],
                    'Friday' => [['start' => '09:00', 'end' => '17:00']],
                    'Saturday' => [],
                    'Sunday' => []
                ]),
                'special_days' => json_encode([])
            ];

            $stmt = $conn->prepare("
                INSERT OR IGNORE INTO tenant_settings 
                (tenant_id, setting_key, setting_value, created_at, updated_at) 
                VALUES (:tenant_id, :key, :value, :created_at, :updated_at)
            ");

            foreach ($defaultSettings as $key => $value) {
                $stmt->bindValue(':tenant_id', $tenantId);
                $stmt->bindValue(':key', $key);
                $stmt->bindValue(':value', $value);
                $stmt->bindValue(':created_at', $now);
                $stmt->bindValue(':updated_at', $now);
                $stmt->execute();
            }

            return true;
        } catch (Exception $e) {
            error_log('Failed to initialize default settings: ' . $e->getMessage());
            return false;
        }
    }
}
