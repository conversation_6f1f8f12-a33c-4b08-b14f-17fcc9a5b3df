<?php
// Get week offset from URL parameter
$weekOffset = (int)($_GET['offset'] ?? 0);

// Calculate the start date based on offset
$startDate = date('Y-m-d', strtotime("today +{$weekOffset} days"));

// Show a week view of reservations starting from the calculated date
$week = [];
for ($i = 0; $i < 7; $i++) {
    $date = date('Y-m-d', strtotime($startDate . " +{$i} days"));
    $week[$date] = $reservationHandler->getReservationsByDate($date);
}
$services = getServices();

// Get all customers for name lookup
$customers = [];
$allCustomers = $customerHandler->getAllCustomers();
foreach ($allCustomers as $customer) {
    $customers[$customer['id']] = $customer;
}

// Get all employees for name lookup
require_once '../includes/admin_functions.php';
$employees = [];
$allEmployees = getEmployees();
foreach ($allEmployees as $employee) {
    $employees[$employee['id']] = $employee;
}

// Calculate week range for display
$weekStart = $startDate;
$weekEnd = date('Y-m-d', strtotime($startDate . ' +6 days'));
?>

<div class="schedule-container">
    <div class="schedule-header">
        <h2><i class="fas fa-calendar-week"></i> Weekly Schedule</h2>
        <div class="schedule-nav">
            <button class="btn btn-secondary btn-sm" onclick="previousWeek(<?= $weekOffset ?>)">
                <i class="fas fa-chevron-left"></i> Previous Week
            </button>
            <span class="current-week">
                <?= date('M j', strtotime($weekStart)) ?> - <?= date('M j, Y', strtotime($weekEnd)) ?>
            </span>
            <button class="btn btn-secondary btn-sm" onclick="nextWeek(<?= $weekOffset ?>)">
                Next Week <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <div class="week-schedule">
        <?php foreach ($week as $date => $reservations): ?>
            <?php
            $dayName = date('l', strtotime($date));
            $dayDate = date('M j', strtotime($date));
            $isToday = $date === date('Y-m-d');
            $isPast = $date < date('Y-m-d');
            ?>
            <div class="day-block <?= $isToday ? 'today' : '' ?> <?= $isPast ? 'past' : '' ?>">
                <div class="day-header">
                    <h4 class="day-name"><?= $dayName ?></h4>
                    <span class="day-date"><?= $dayDate ?></span>
                    <?php if ($isToday): ?>
                        <span class="today-badge">Today</span>
                    <?php endif; ?>
                </div>

                <div class="day-content">
                    <?php if (empty($reservations)): ?>
                        <div class="empty-state">
                            <i class="fas fa-calendar-times"></i>
                            <span>No reservations</span>
                        </div>
                    <?php else: ?>
                        <div class="reservations-list">
                            <?php
                            // Sort reservations by time
                            usort($reservations, function ($a, $b) {
                                return strcmp($a['time'], $b['time']);
                            });

                            foreach ($reservations as $r):
                                $customerName = $customers[$r['customer_id']]['name'] ?? 'Unknown Customer';
                                $serviceName = $services[$r['service']]['name'] ?? $r['service'];
                                $employeeName = 'Not Assigned';
                                if (!empty($r['employee_id']) && isset($employees[$r['employee_id']])) {
                                    $employeeName = $employees[$r['employee_id']]['name'];
                                }
                                $statusClass = $r['status'];
                            ?>
                                <div class="reservation-item <?= $statusClass ?>">
                                    <div class="reservation-time">
                                        <i class="fas fa-clock"></i>
                                        <?= format_time_range($r['time'], (int)$r['duration']) ?>
                                    </div>
                                    <div class="reservation-details">
                                        <div class="service-name"><?= htmlspecialchars($serviceName) ?></div>
                                        <div class="customer-name"><?= htmlspecialchars($customerName) ?></div>
                                        <div class="employee-name">
                                            <i class="fas fa-user-tie"></i>
                                            <?= htmlspecialchars($employeeName) ?>
                                        </div>
                                    </div>
                                    <div class="reservation-status">
                                        <span class="status-badge <?= $r['status'] ?>">
                                            <?php if ($r['status'] === 'confirmed'): ?>
                                                <i class="fas fa-check-circle"></i>
                                            <?php elseif ($r['status'] === 'cancelled'): ?>
                                                <i class="fas fa-times-circle"></i>
                                            <?php else: ?>
                                                <i class="fas fa-question-circle"></i>
                                            <?php endif; ?>
                                            <?= ucfirst($r['status']) ?>
                                        </span>
                                    </div>
                                    <div class="reservation-actions">
                                        <button class="btn btn-xs btn-info" onclick="viewReservation('<?= $r['id'] ?>')" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if ($r['status'] === 'confirmed' && !$isPast): ?>
                                            <button class="btn btn-xs btn-secondary" onclick="editReservation('<?= $r['id'] ?>')" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-xs btn-danger" onclick="cancelReservation('<?= $r['id'] ?>')" title="Cancel">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="day-summary">
                            <small class="text-muted">
                                <i class="fas fa-list"></i>
                                <?= count($reservations) ?> reservation<?= count($reservations) !== 1 ? 's' : '' ?>
                            </small>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Add Reservation Button -->
                <?php if (!$isPast): ?>
                    <button class="add-reservation-btn" onclick="addReservationForDate('<?= $date ?>')" title="Add reservation for <?= $dayDate ?>">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="add-customer-btn" onclick="openModal('Add Customer', 'views/modals/add_customer.php')">
                        <i class="fas fa-user-plus"></i>
                    </button>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- Add "Back to Current Week" button if not on current week -->
    <?php if ($weekOffset !== 0): ?>
        <div class="schedule-actions">
            <button class="btn btn-primary" onclick="goToCurrentWeek()">
                <i class="fas fa-calendar-day"></i> Back to Current Week
            </button>
        </div>
    <?php endif; ?>

    <div class="schedule-legend">
        <h5>Status Legend</h5>
        <div class="legend-items">
            <span class="legend-item">
                <span class="status-badge confirmed"><i class="fas fa-check-circle"></i> Confirmed</span>
            </span>
            <span class="legend-item">
                <span class="status-badge cancelled"><i class="fas fa-times-circle"></i> Cancelled</span>
            </span>
            <span class="legend-item">
                <span class="status-badge pending"><i class="fas fa-question-circle"></i> Pending</span>
            </span>
        </div>
    </div>
</div>

<style>
    .employee-name {
        font-size: 0.85em;
        color: #666;
        margin-top: 2px;
    }

    .employee-name i {
        margin-right: 4px;
    }

    .reservation-details {
        flex: 1;
    }
</style>

<script>
    function previousWeek(currentOffset) {
        const newOffset = currentOffset - 7;
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('offset', newOffset);
        window.location.href = currentUrl.toString();
    }

    function nextWeek(currentOffset) {
        const newOffset = currentOffset + 7;
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('offset', newOffset);
        window.location.href = currentUrl.toString();
    }

    function goToCurrentWeek() {
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.delete('offset');
        window.location.href = currentUrl.toString();
    }

    function addReservationForDate(date) {
        // Open the add reservation modal with the selected date pre-filled
        openModal('Add Reservation for ' + formatDateForDisplay(date), 'views/modals/add_reservation.php?date=' + date);
    }

    function formatDateForDisplay(dateString) {
        const date = new Date(dateString + 'T00:00:00');
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        return date.toLocaleDateString('en-US', options);
    }

    function viewReservation(reservationId) {
        openModal('Reservation Details', 'views/modals/view_reservation.php?id=' + reservationId);
    }

    function editReservation(reservationId) {
        openModal('Edit Reservation', 'views/modals/edit_reservation.php?id=' + reservationId);
    }
</script>