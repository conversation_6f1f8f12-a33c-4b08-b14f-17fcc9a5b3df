<?php
require_once '../../includes/config.php';
require_once '../../includes/unified_text_manager.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Regenerate email templates using unified manager
    $success = UnifiedTextManager::regenerateEmailTemplatesManually();
    
    if ($success) {
        echo json_encode([
            'success' => true,
            'message' => 'Email templates regenerated successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to regenerate email templates'
        ]);
    }
} catch (Exception $e) {
    error_log('Template regeneration error: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Server error occurred: ' . $e->getMessage()
    ]);
}
