<?php

require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
require_once '../../../includes/customer_handler.php';
?>
<form method="post" action="actions/save_customer.php" data-validate>
    <div class="form-row">
        <div class="form-group">
            <label>Name *</label>
            <input type="text" name="customer_name" class="form-control" required>
        </div>
        <div class="form-group">
            <label>Email *</label>
            <input type="email" name="customer_email" class="form-control" required>
        </div>
    </div>

    <div class="form-row">
        <div class="form-group">
            <label>Mobile</label>
            <input type="text" name="customer_mobile" class="form-control">
        </div>
        <div class="form-group">
            <label>Date of Birth</label>
            <input type="date" name="customer_dob" class="form-control">
        </div>
    </div>

    <div class="form-group">
        <label>Address</label>
        <textarea name="customer_address" class="form-control" rows="2"></textarea>
    </div>

    <div class="form-row">
        <div class="form-group">
            <label>Preferred Contact Method</label>
            <select name="preferred_contact" class="form-control">
                <option value="email">Email</option>
                <option value="mobile">Mobile</option>
            </select>
        </div>
        <div class="form-group">
            <label>Preferred Language</label>
            <select name="preferred_language" class="form-control">
                <option value="el" selected>🇬🇷 Ελληνικά (Greek)</option>
                <option value="en">🇬🇧 English</option>
            </select>
            <small class="form-text text-muted">Language for email communications</small>
        </div>
    </div>

    <div class="form-group">
        <label>Notes</label>
        <textarea name="customer_notes" class="form-control" rows="3" placeholder="Any special notes about this customer..."></textarea>
    </div>

    <button type="submit" class="btn btn-primary">
        <i class="fas fa-save"></i> Save Customer
    </button>
</form>