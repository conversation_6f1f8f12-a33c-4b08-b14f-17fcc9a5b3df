<?php

require_once __DIR__ . '/Database.php';

class EmployeeHandler
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Get all employees (tenant-aware)
     */
    public function getAllEmployees(): array
    {
        $employees = [];
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT * FROM employees WHERE tenant_id = :tenant_id ORDER BY name");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $employees[] = [
                'id' => $row['id'],
                'name' => $row['name'],
                'email' => $row['email'] ?? '',
                'phone' => $row['phone'] ?? '',
                'working_hours' => json_decode($row['working_hours'] ?? '{}', true),
                'status' => $row['status'] ?? 'active',
                'created_at' => $row['created_at']
            ];
        }

        return $employees;
    }

    /**
     * Get employees with pagination (tenant-aware)
     */
    public function getEmployeesPaginated(int $limit = 20, int $offset = 0): array
    {
        $employees = [];
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT * FROM employees WHERE tenant_id = :tenant_id ORDER BY name LIMIT :limit OFFSET :offset");
        $stmt->bindValue(':tenant_id', $tenantId);
        $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
        $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);

        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $employees[] = [
                'id' => $row['id'],
                'name' => $row['name'],
                'email' => $row['email'] ?? '',
                'phone' => $row['phone'] ?? '',
                'working_hours' => json_decode($row['working_hours'] ?? '{}', true),
                'status' => $row['status'] ?? 'active',
                'created_at' => $row['created_at']
            ];
        }

        return $employees;
    }

    /**
     * Get total employee count (tenant-aware)
     */
    public function getEmployeeCount(): int
    {
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM employees WHERE tenant_id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
        return (int)($row['count'] ?? 0);
    }

    /**
     * Search employees with pagination
     */
    public function searchEmployees(string $searchTerm, int $limit = 20, int $offset = 0): array
    {
        $employees = [];
        $conn = $this->db->getConnection();

        $searchTerm = '%' . strtolower(trim($searchTerm)) . '%';

        $stmt = $conn->prepare("
            SELECT e.*, GROUP_CONCAT(s.name) as service_names
            FROM employees e
            LEFT JOIN employee_services es ON e.id = es.employee_id
            LEFT JOIN services s ON es.service_id = s.id
            WHERE LOWER(e.name) LIKE :search 
               OR LOWER(e.email) LIKE :search 
               OR LOWER(e.phone) LIKE :search 
               OR LOWER(e.status) LIKE :search
               OR LOWER(s.name) LIKE :search
            GROUP BY e.id
            ORDER BY e.name 
            LIMIT :limit OFFSET :offset
        ");

        $stmt->bindValue(':search', $searchTerm);
        $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
        $stmt->bindValue(':offset', $offset, SQLITE3_INTEGER);

        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $employees[] = [
                'id' => $row['id'],
                'name' => $row['name'],
                'email' => $row['email'] ?? '',
                'phone' => $row['phone'] ?? '',
                'working_hours' => json_decode($row['working_hours'] ?? '{}', true),
                'status' => $row['status'] ?? 'active',
                'created_at' => $row['created_at'],
                'service_names' => $row['service_names'] ?? ''
            ];
        }

        return $employees;
    }

    /**
     * Get total count of search results (tenant-aware)
     */
    public function getSearchEmployeeCount(string $searchTerm): int
    {
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $searchTerm = '%' . strtolower(trim($searchTerm)) . '%';

        $stmt = $conn->prepare("
            SELECT COUNT(DISTINCT e.id) as count
            FROM employees e
            LEFT JOIN employee_services es ON e.id = es.employee_id
            LEFT JOIN services s ON es.service_id = s.id AND s.tenant_id = :tenant_id
            WHERE e.tenant_id = :tenant_id
               AND (LOWER(e.name) LIKE :search
               OR LOWER(e.email) LIKE :search
               OR LOWER(e.phone) LIKE :search
               OR LOWER(e.status) LIKE :search
               OR LOWER(s.name) LIKE :search)
        ");

        $stmt->bindValue(':tenant_id', $tenantId);
        $stmt->bindValue(':search', $searchTerm);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);

        return (int)($row['count'] ?? 0);
    }

    /**
     * Get employee by ID (tenant-aware)
     */
    public function getEmployeeById(string $id): ?array
    {
        $conn = $this->db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT * FROM employees WHERE id = :id AND tenant_id = :tenant_id LIMIT 1");
        $stmt->bindValue(':id', $id);
        $stmt->bindValue(':tenant_id', $tenantId);

        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);

        if ($row) {
            return [
                'id' => $row['id'],
                'name' => $row['name'],
                'email' => $row['email'] ?? '',
                'phone' => $row['phone'] ?? '',
                'working_hours' => json_decode($row['working_hours'] ?? '{}', true),
                'status' => $row['status'] ?? 'active',
                'created_at' => $row['created_at']
            ];
        }

        return null;
    }

    /**
     * Get services for an employee
     */
    public function getEmployeeServices(string $employeeId): array
    {
        $conn = $this->db->getConnection();
        $services = [];

        $stmt = $conn->prepare("
            SELECT s.* 
            FROM services s
            JOIN employee_services es ON s.id = es.service_id
            WHERE es.employee_id = :employee_id
            ORDER BY s.name
        ");

        $stmt->bindValue(':employee_id', $employeeId);
        $result = $stmt->execute();

        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $services[] = [
                'id' => $row['id'],
                'name' => $row['name'],
                'duration' => (int)$row['duration'],
                'price' => (float)$row['price'],
                'description' => $row['description'] ?? ''
            ];
        }

        return $services;
    }
}
