<?php

/**
 * Unified Tenant Management System
 * 
 * This file consolidates all tenant management functionality:
 * - Create Business/Tenant
 * - Delete Business/Tenant  
 * - Debug Tenant System
 * - Clean & Setup Database
 * - Run Migrations
 * - Test Tenant Isolation
 * - File-to-Database Migration
 */

require_once 'includes/config.php';
require_once 'includes/Database.php';
require_once 'includes/TenantContext.php';

// Initialize tenant context
TenantContext::initializeFromRequest();

$action = $_GET['action'] ?? 'dashboard';
$message = '';
$success = false;

// Handle POST actions
if ($_POST) {
    switch ($_POST['action'] ?? '') {
        case 'create_business':
            $result = handleCreateBusiness();
            $message = $result['message'];
            $success = $result['success'];
            break;

        case 'delete_business':
            $result = handleDeleteBusiness();
            $message = $result['message'];
            $success = $result['success'];
            break;

        case 'clean_setup':
            $result = handleCleanSetup();
            $message = $result['message'];
            $success = $result['success'];
            break;

        case 'run_migration':
            $result = handleRunMigration();
            $message = $result['message'];
            $success = $result['success'];
            break;

        case 'file_migration':
            $result = handleFileMigration();
            $message = $result['message'];
            $success = $result['success'];
            break;
    }
}

/**
 * Handle Create Business
 */
function handleCreateBusiness()
{
    try {
        $businessName = trim($_POST['business_name']);
        $subdomain = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $businessName));
        $subdomain = substr($subdomain, 0, 15);

        if (empty($businessName) || empty($subdomain)) {
            throw new Exception('Business name is required');
        }

        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Check if subdomain exists
        $stmt = $conn->prepare("SELECT id FROM tenants WHERE subdomain = :subdomain");
        $stmt->bindValue(':subdomain', $subdomain);
        $result = $stmt->execute();

        if ($result->fetchArray()) {
            throw new Exception('Subdomain already exists. Try a different business name.');
        }

        // Generate tenant ID
        $tenantId = 'TN-' . strtoupper(substr(md5(uniqid()), 0, 10));

        // Insert new tenant
        $stmt = $conn->prepare("
            INSERT INTO tenants 
            (id, business_name, subdomain, plan, status, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");

        $now = date('Y-m-d H:i:s');
        $stmt->bindValue(1, $tenantId);
        $stmt->bindValue(2, $businessName);
        $stmt->bindValue(3, $subdomain);
        $stmt->bindValue(4, 'trial');
        $stmt->bindValue(5, 'active');
        $stmt->bindValue(6, $now);
        $stmt->bindValue(7, $now);

        if ($stmt->execute()) {
            return [
                'success' => true,
                'message' => "✅ Business created successfully!<br>
                            <strong>Business:</strong> $businessName<br>
                            <strong>Subdomain:</strong> <a href='https://$subdomain.skrtz.gr/' target='_blank'>$subdomain.skrtz.gr</a><br>
                            <strong>Tenant ID:</strong> $tenantId"
            ];
        } else {
            throw new Exception('Failed to create business');
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => "❌ Error: " . $e->getMessage()];
    }
}

/**
 * Handle Delete Business
 */
function handleDeleteBusiness()
{
    try {
        $tenantId = trim($_POST['tenant_id']);
        $confirmName = trim($_POST['confirm_name']);

        if (empty($tenantId)) {
            throw new Exception('Tenant ID is required');
        }

        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Get business details
        $stmt = $conn->prepare("SELECT * FROM tenants WHERE id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
        $business = $result->fetchArray(SQLITE3_ASSOC);

        if (!$business) {
            throw new Exception('Business not found');
        }

        // Prevent deletion of default tenant
        if ($business['subdomain'] === 'default') {
            throw new Exception('Cannot delete the default business');
        }

        // Confirm business name
        if (strtolower($confirmName) !== strtolower($business['business_name'])) {
            throw new Exception('Business name confirmation does not match');
        }

        // Start transaction
        $conn->exec('BEGIN TRANSACTION');

        // Delete all tenant data
        $tables = ['customers', 'employees', 'reservations', 'services', 'employee_services'];

        foreach ($tables as $table) {
            $stmt = $conn->prepare("DELETE FROM $table WHERE tenant_id = :tenant_id");
            $stmt->bindValue(':tenant_id', $tenantId);
            $stmt->execute();
        }

        // Delete the tenant
        $stmt = $conn->prepare("DELETE FROM tenants WHERE id = :tenant_id");
        $stmt->bindValue(':tenant_id', $tenantId);
        $stmt->execute();

        // Commit transaction
        $conn->exec('COMMIT');

        return [
            'success' => true,
            'message' => "✅ Business '{$business['business_name']}' has been permanently deleted!"
        ];
    } catch (Exception $e) {
        if (isset($conn)) {
            $conn->exec('ROLLBACK');
        }
        return ['success' => false, 'message' => "❌ Error: " . $e->getMessage()];
    }
}

/**
 * Handle Clean & Setup
 */
function handleCleanSetup()
{
    try {
        $output = "🚀 Starting clean & setup process...\n\n";

        // Step 1: Delete data folder
        $output .= "Step 1: Cleaning data folder...\n";
        $dataDir = __DIR__ . '/data';
        if (is_dir($dataDir)) {
            function deleteDirectory($dir)
            {
                if (!is_dir($dir)) return false;
                $files = array_diff(scandir($dir), array('.', '..'));
                foreach ($files as $file) {
                    $path = $dir . DIRECTORY_SEPARATOR . $file;
                    is_dir($path) ? deleteDirectory($path) : unlink($path);
                }
                return rmdir($dir);
            }
            if (deleteDirectory($dataDir)) {
                $output .= "✅ Data folder deleted successfully\n";
            } else {
                $output .= "⚠️ Could not delete data folder completely\n";
            }
        } else {
            $output .= "ℹ️ Data folder already clean\n";
        }

        // Step 2: Recreate data folder
        $output .= "\nStep 2: Recreating data folder...\n";
        if (!is_dir($dataDir)) {
            if (mkdir($dataDir, 0755, true)) {
                $output .= "✅ Data folder recreated successfully\n";
            } else {
                throw new Exception("Failed to create data folder");
            }
        } else {
            $output .= "ℹ️ Data folder already exists\n";
        }

        // Step 3: Initialize database with dummy data
        $output .= "\nStep 3: Creating fresh database with dummy data...\n";
        $dbPath = DATA_DIR . 'gk_booking.sqlite';
        $output .= "Database path: $dbPath\n";

        if (file_exists($dbPath)) {
            $output .= "⚠️ Database file already exists, removing...\n";
            unlink($dbPath);
        }

        // Ensure data directory exists with proper permissions
        if (!is_dir(DATA_DIR)) {
            mkdir(DATA_DIR, 0755, true);
            $output .= "✅ Data directory created\n";
        }

        // Check directory permissions
        if (!is_writable(DATA_DIR)) {
            $output .= "❌ Data directory is not writable: " . DATA_DIR . "\n";
            throw new Exception("Data directory is not writable: " . DATA_DIR);
        }

        try {
            // Reset any existing Database singleton instance
            $output .= "Resetting Database singleton instance...\n";
            Database::resetInstance();

            // Force database creation
            $output .= "Attempting to create Database instance...\n";
            $db = Database::getInstance();
            $output .= "Database instance created successfully\n";

            $conn = $db->getConnection();
            $output .= "Database connection obtained\n";

            // Execute a simple query to ensure database is working
            $conn->exec("SELECT 1");
            $output .= "Test query executed successfully\n";

            // Add a small delay to ensure file system sync
            usleep(100000); // 100ms delay

            if (file_exists($dbPath)) {
                $fileSize = filesize($dbPath);
                $output .= "✅ Fresh database created successfully at: $dbPath (Size: {$fileSize} bytes)\n";
            } else {
                $output .= "❌ Database file was not created at: $dbPath\n";
                // List directory contents for debugging
                $files = scandir(DATA_DIR);
                $output .= "Directory contents: " . implode(', ', $files) . "\n";
                throw new Exception("Database file creation failed - file not found after creation");
            }
        } catch (Exception $e) {
            $output .= "❌ Database creation error: " . $e->getMessage() . "\n";
            $output .= "Error details: " . $e->getFile() . " line " . $e->getLine() . "\n";
            throw $e;
        }

        // Step 4: Run tenant migration
        $output .= "\nStep 4: Adding tenant system...\n";
        require_once 'includes/migrations/add_tenant_system.php';

        ob_start();
        $migrationSuccess = migrate_add_tenant_system(true);
        ob_get_clean();

        if ($migrationSuccess) {
            $output .= "✅ Tenant system added successfully\n";
        } else {
            $output .= "❌ Tenant migration failed\n";
            throw new Exception("Tenant migration returned false");
        }

        // Step 4.5: Add tenant configuration tables
        $output .= "\nStep 4.5: Adding tenant configuration tables...\n";
        $db = Database::getInstance();
        $conn = $db->getConnection();

        try {
            // Add activity logs table
            $conn->exec("CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tenant_id TEXT NOT NULL,
                message TEXT NOT NULL,
                created_at TEXT NOT NULL
            )");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_activity_logs_tenant ON activity_logs(tenant_id)");

            // Add tenant settings table
            $conn->exec("CREATE TABLE IF NOT EXISTS tenant_settings (
                tenant_id TEXT NOT NULL,
                setting_key TEXT NOT NULL,
                setting_value TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                PRIMARY KEY (tenant_id, setting_key)
            )");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_tenant_settings_tenant ON tenant_settings(tenant_id)");

            // Add tenant texts table
            $conn->exec("CREATE TABLE IF NOT EXISTS tenant_texts (
                tenant_id TEXT NOT NULL,
                text_type TEXT NOT NULL,
                language TEXT NOT NULL DEFAULT 'en',
                text_key TEXT NOT NULL,
                text_value TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                PRIMARY KEY (tenant_id, text_type, language, text_key)
            )");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_tenant_texts_tenant ON tenant_texts(tenant_id)");
            $conn->exec("CREATE INDEX IF NOT EXISTS idx_tenant_texts_type ON tenant_texts(text_type)");

            $output .= "✅ Tenant configuration tables added successfully\n";
        } catch (Exception $e) {
            $output .= "⚠️ Tenant configuration tables may already exist\n";
        }

        // Step 5: Initialize tenant context
        $output .= "\nStep 5: Setting up tenant context...\n";
        require_once 'includes/tenant_init.php';
        require_once 'includes/TenantSettingsManager.php';

        $tenantId = TenantContext::getTenant();
        if ($tenantId) {
            $output .= "✅ Tenant context initialized: $tenantId\n";

            if (TenantSettingsManager::initializeDefaultSettings($tenantId)) {
                $output .= "✅ Default tenant settings initialized\n";
            } else {
                $output .= "⚠️ Could not initialize default settings (may already exist)\n";
            }
        } else {
            $output .= "⚠️ No tenant context found, but system should still work\n";
        }

        $output .= "\n🎉 Setup complete! Your multi-tenant system is ready to use.\n";

        return ['success' => true, 'message' => $output];
    } catch (Exception $e) {
        $output .= "\n❌ Fatal Error: " . $e->getMessage() . "\n";
        return ['success' => false, 'message' => $output];
    }
}

/**
 * Handle Run Migration
 */
function handleRunMigration()
{
    try {
        require_once 'includes/migrations/add_tenant_system.php';

        ob_start();
        $success = migrate_add_tenant_system(true);
        $output = ob_get_clean();

        if ($success) {
            return [
                'success' => true,
                'message' => "✅ MIGRATION COMPLETED SUCCESSFULLY!\n\n" . $output
            ];
        } else {
            return [
                'success' => false,
                'message' => "❌ Migration failed.\n\n" . $output
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "❌ Error: " . $e->getMessage()
        ];
    }
}

/**
 * Handle File Migration
 */
function handleFileMigration()
{
    try {
        require_once 'includes/tenant_init.php';
        require_once 'includes/TenantSettingsManager.php';

        $tenantId = TenantContext::getTenant();
        if (!$tenantId) {
            throw new Exception('No tenant context found.');
        }

        $migrated = 0;
        $errors = 0;
        $details = [];

        // Migrate settings
        $settingsFile = __DIR__ . '/includes/settings.php';
        if (file_exists($settingsFile)) {
            $settings = require $settingsFile;
            if (is_array($settings)) {
                foreach ($settings as $key => $value) {
                    if (TenantSettingsManager::setSetting($key, $value)) {
                        $details[] = "✅ Migrated setting: $key";
                        $migrated++;
                    } else {
                        $details[] = "❌ Failed to migrate setting: $key";
                        $errors++;
                    }
                }
            }
        } else {
            $details[] = "ℹ️ No settings.php file found";
        }

        $output = "🎉 File Migration Completed!\n";
        $output .= "Tenant ID: $tenantId\n";
        $output .= "Items Migrated: $migrated\n";
        $output .= "Errors: $errors\n\n";
        $output .= "Details:\n" . implode("\n", $details);

        return [
            'success' => $errors === 0,
            'message' => $output
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => "❌ Error: " . $e->getMessage()
        ];
    }
}

/**
 * Get all tenants
 */
function getAllTenants()
{
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        $result = $conn->query("SELECT * FROM tenants ORDER BY created_at DESC");

        $tenants = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $tenants[] = $row;
        }
        return $tenants;
    } catch (Exception $e) {
        return [];
    }
}

/**
 * Get debug information
 */
function getDebugInfo()
{
    $info = [];

    // Current request info
    $info['request'] = [
        'full_url' => (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'],
        'host' => $_SERVER['HTTP_HOST'],
        'server_name' => $_SERVER['SERVER_NAME']
    ];

    // Tenant detection
    $host = $_SERVER['HTTP_HOST'];
    $parts = explode('.', $host);
    $info['tenant_detection'] = [
        'host_parts' => $parts,
        'subdomain' => count($parts) >= 3 ? $parts[0] : null
    ];

    // Current tenant
    $currentTenant = TenantContext::getTenant();
    $info['current_tenant'] = $currentTenant;

    // Tenant data
    if ($currentTenant) {
        try {
            $db = Database::getInstance();
            $conn = $db->getConnection();
            $stmt = $conn->prepare("SELECT * FROM tenants WHERE id = :id");
            $stmt->bindValue(':id', $currentTenant);
            $result = $stmt->execute();
            $info['tenant_data'] = $result->fetchArray(SQLITE3_ASSOC);
        } catch (Exception $e) {
            $info['tenant_data'] = null;
        }
    }

    // Database stats
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        $result = $conn->query("SELECT COUNT(*) as count FROM customers");
        $info['stats']['total_customers'] = $result->fetchArray()['count'];

        if ($currentTenant) {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM customers WHERE tenant_id = :tenant_id");
            $stmt->bindValue(':tenant_id', $currentTenant);
            $result = $stmt->execute();
            $info['stats']['tenant_customers'] = $result->fetchArray()['count'];
        }
    } catch (Exception $e) {
        $info['stats'] = null;
    }

    return $info;
}

/**
 * Test tenant isolation
 */
function testTenantIsolation()
{
    $results = [];

    try {
        require_once 'includes/customer_handler.php';
        require_once 'includes/employee_handler.php';
        require_once 'includes/service_handler.php';
        require_once 'includes/reservation_handler.php';

        $currentTenant = TenantContext::getTenant();
        $results['tenant_id'] = $currentTenant;

        // Test Customer Handler
        $customerHandler = new CustomerHandler();
        $allCustomers = $customerHandler->getAllCustomers();
        $results['customer_handler'] = [
            'getAllCustomers' => count($allCustomers),
            'getCustomerCount' => $customerHandler->getCustomerCount()
        ];

        // Test Employee Handler
        $employeeHandler = new EmployeeHandler();
        $allEmployees = $employeeHandler->getAllEmployees();
        $results['employee_handler'] = [
            'getAllEmployees' => count($allEmployees)
        ];

        // Test Service Handler
        $serviceHandler = new ServiceHandler();
        $allServices = $serviceHandler->getAllServices();
        $results['service_handler'] = [
            'getAllServices' => count($allServices)
        ];

        // Test Reservation Handler
        $reservationHandler = new ReservationHandler();
        $allReservations = $reservationHandler->getAllReservations();
        $results['reservation_handler'] = [
            'getAllReservations' => count($allReservations)
        ];

        $results['success'] = true;
    } catch (Exception $e) {
        $results['error'] = $e->getMessage();
        $results['success'] = false;
    }

    return $results;
}

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified Tenant Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }

        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }

        .nav-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .nav-tab {
            padding: 10px 15px;
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-bottom: none;
            cursor: pointer;
            text-decoration: none;
            color: #333;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .nav-tab.active {
            background: white;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
            color: #007bff;
            font-weight: bold;
        }

        .nav-tab:hover {
            background: #e9ecef;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .message {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            white-space: pre-line;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"],
        select,
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .btn-success {
            background-color: #28a745;
            color: white;
        }

        .btn-success:hover {
            background-color: #218838;
        }

        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background-color: #e0a800;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #545b62;
        }

        .tenant-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .tenant-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }

        .tenant-item.default {
            border-left-color: #28a745;
        }

        .tenant-item h3 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .tenant-item p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }

        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-line;
            margin: 10px 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .navigation {
            text-align: center;
            margin-bottom: 20px;
        }

        .navigation a {
            margin: 0 10px;
            color: #007bff;
            text-decoration: none;
        }

        .navigation a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div class="navigation">
        <a href="index.php">← Main Site</a> |
        <a href="admin/">Admin Panel</a> |
        <a href="https://skrtz.gr/tenant_manager.php">Main Domain</a> |
        <a href="https://realma.skrtz.gr/tenant_manager.php">Realma Subdomain</a>
    </div>

    <div class="container">
        <h1>🏢 Unified Tenant Management System</h1>

        <?php if ($message): ?>
            <div class="message <?= $success ? 'success' : 'error' ?>">
                <?= $message ?>
            </div>
        <?php endif; ?>

        <div class="nav-tabs">
            <a href="#dashboard" class="nav-tab <?= $action === 'dashboard' ? 'active' : '' ?>" onclick="showTab('dashboard')">📊 Dashboard</a>
            <a href="#create" class="nav-tab <?= $action === 'create' ? 'active' : '' ?>" onclick="showTab('create')">➕ Create Business</a>
            <a href="#delete" class="nav-tab <?= $action === 'delete' ? 'active' : '' ?>" onclick="showTab('delete')">🗑️ Delete Business</a>
            <a href="#debug" class="nav-tab <?= $action === 'debug' ? 'active' : '' ?>" onclick="showTab('debug')">🔍 Debug</a>
            <a href="#clean" class="nav-tab <?= $action === 'clean' ? 'active' : '' ?>" onclick="showTab('clean')">🧹 Clean & Setup</a>
            <a href="#migration" class="nav-tab <?= $action === 'migration' ? 'active' : '' ?>" onclick="showTab('migration')">🚀 Migration</a>
            <a href="#test" class="nav-tab <?= $action === 'test' ? 'active' : '' ?>" onclick="showTab('test')">🧪 Test</a>
        </div>

        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content <?= $action === 'dashboard' ? 'active' : '' ?>">
            <h2>📊 Tenant System Dashboard</h2>

            <?php
            $tenants = getAllTenants();
            $currentTenant = TenantContext::getTenant();
            $debugInfo = getDebugInfo();
            ?>

            <div class="info">
                <h3>🌐 Current Context</h3>
                <p><strong>Host:</strong> <?= htmlspecialchars($_SERVER['HTTP_HOST']) ?></p>
                <p><strong>Current Tenant:</strong> <?= $currentTenant ? htmlspecialchars($currentTenant) : 'None detected' ?></p>
                <?php if ($debugInfo['tenant_data']): ?>
                    <p><strong>Business Name:</strong> <?= htmlspecialchars($debugInfo['tenant_data']['business_name']) ?></p>
                    <p><strong>Subdomain:</strong> <?= htmlspecialchars($debugInfo['tenant_data']['subdomain']) ?></p>
                    <p><strong>Status:</strong> <?= htmlspecialchars($debugInfo['tenant_data']['status']) ?></p>
                <?php endif; ?>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?= count($tenants) ?></div>
                    <div class="stat-label">Total Businesses</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $debugInfo['stats']['total_customers'] ?? 0 ?></div>
                    <div class="stat-label">Total Customers</div>
                </div>
                <?php if ($currentTenant): ?>
                    <div class="stat-card">
                        <div class="stat-number"><?= $debugInfo['stats']['tenant_customers'] ?? 0 ?></div>
                        <div class="stat-label">Current Tenant Customers</div>
                    </div>
                <?php endif; ?>
            </div>

            <h3>📋 All Businesses</h3>
            <?php if (empty($tenants)): ?>
                <p>No businesses found. <a href="#create" onclick="showTab('create')">Create your first business</a>.</p>
            <?php else: ?>
                <div class="tenant-list">
                    <?php foreach ($tenants as $tenant): ?>
                        <div class="tenant-item <?= $tenant['subdomain'] === 'default' ? 'default' : '' ?>">
                            <h3><?= htmlspecialchars($tenant['business_name']) ?></h3>
                            <p><strong>Subdomain:</strong>
                                <a href="https://<?= htmlspecialchars($tenant['subdomain']) ?>.skrtz.gr/" target="_blank">
                                    <?= htmlspecialchars($tenant['subdomain']) ?>.skrtz.gr
                                </a>
                            </p>
                            <p><strong>Status:</strong> <?= htmlspecialchars($tenant['status']) ?></p>
                            <p><strong>Plan:</strong> <?= htmlspecialchars($tenant['plan']) ?></p>
                            <p><strong>Created:</strong> <?= htmlspecialchars($tenant['created_at']) ?></p>
                            <?php if ($tenant['id'] === $currentTenant): ?>
                                <p><em>🎯 Current Tenant</em></p>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Create Business Tab -->
        <div id="create" class="tab-content <?= $action === 'create' ? 'active' : '' ?>">
            <h2>➕ Create New Business</h2>

            <div class="info">
                <h3>📋 What This Does:</h3>
                <ul>
                    <li>✅ Creates a new tenant/business in the system</li>
                    <li>✅ Generates a unique subdomain (e.g., "realma.skrtz.gr")</li>
                    <li>✅ Sets up isolated data storage for the business</li>
                    <li>✅ Makes the business immediately accessible via subdomain</li>
                </ul>
            </div>

            <form method="POST">
                <input type="hidden" name="action" value="create_business">

                <div class="form-group">
                    <label for="business_name">Business Name:</label>
                    <input type="text" id="business_name" name="business_name"
                        placeholder="e.g., Realma Salon, Maria's Spa, etc."
                        value="<?= htmlspecialchars($_POST['business_name'] ?? '') ?>" required>
                    <small style="color: #666;">This will be converted to subdomain (e.g., "Realma Salon" → "realmasalon.skrtz.gr")</small>
                </div>

                <button type="submit" class="btn btn-primary">➕ Create Business</button>
            </form>
        </div>

        <!-- Delete Business Tab -->
        <div id="delete" class="tab-content <?= $action === 'delete' ? 'active' : '' ?>">
            <h2>🗑️ Delete Business</h2>

            <div class="warning">
                <strong>⚠️ WARNING:</strong> Deleting a business will permanently remove:
                <ul>
                    <li>All customers and their data</li>
                    <li>All employees and their assignments</li>
                    <li>All reservations and booking history</li>
                    <li>All services and configurations</li>
                    <li>The business subdomain access</li>
                </ul>
                <strong>This action cannot be undone!</strong>
            </div>

            <?php $tenants = getAllTenants(); ?>
            <?php if (!empty($tenants)): ?>
                <form method="POST">
                    <input type="hidden" name="action" value="delete_business">

                    <div class="form-group">
                        <label for="tenant_id">Select Business to Delete:</label>
                        <select name="tenant_id" id="tenant_id" required onchange="updateConfirmation()">
                            <option value="">-- Select a business --</option>
                            <?php foreach ($tenants as $tenant): ?>
                                <?php if ($tenant['subdomain'] !== 'default'): ?>
                                    <option value="<?= htmlspecialchars($tenant['id']) ?>"
                                        data-name="<?= htmlspecialchars($tenant['business_name']) ?>">
                                        <?= htmlspecialchars($tenant['business_name']) ?> (<?= htmlspecialchars($tenant['subdomain']) ?>)
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="confirm_name">Type the business name to confirm deletion:</label>
                        <input type="text" name="confirm_name" id="confirm_name"
                            placeholder="Enter exact business name" required>
                        <small id="confirm_hint" style="color: #666;"></small>
                    </div>

                    <button type="submit" class="btn btn-danger"
                        onclick="return confirm('Are you absolutely sure? This will permanently delete all data for this business!')">
                        🗑️ Permanently Delete Business
                    </button>
                </form>
            <?php else: ?>
                <p>No businesses available for deletion.</p>
            <?php endif; ?>
        </div>

        <!-- Debug Tab -->
        <div id="debug" class="tab-content <?= $action === 'debug' ? 'active' : '' ?>">
            <h2>🔍 Debug Tenant System</h2>

            <?php $debugInfo = getDebugInfo(); ?>

            <div class="info">
                <h3>📋 Current Request Info</h3>
                <p><strong>Full URL:</strong> <?= htmlspecialchars($debugInfo['request']['full_url']) ?></p>
                <p><strong>Host:</strong> <?= htmlspecialchars($debugInfo['request']['host']) ?></p>
                <p><strong>Server Name:</strong> <?= htmlspecialchars($debugInfo['request']['server_name']) ?></p>
            </div>

            <div class="info">
                <h3>🏢 Tenant Detection</h3>
                <p><strong>Host parts:</strong> <?= implode(' | ', $debugInfo['tenant_detection']['host_parts']) ?></p>
                <?php if ($debugInfo['tenant_detection']['subdomain']): ?>
                    <p><strong>Detected subdomain:</strong> '<?= htmlspecialchars($debugInfo['tenant_detection']['subdomain']) ?>'</p>
                <?php else: ?>
                    <p><strong>No subdomain detected</strong> (main domain)</p>
                <?php endif; ?>

                <?php if ($debugInfo['current_tenant']): ?>
                    <p><strong>✅ Current Tenant ID:</strong> <?= htmlspecialchars($debugInfo['current_tenant']) ?></p>
                    <?php if ($debugInfo['tenant_data']): ?>
                        <p><strong>Business Name:</strong> <?= htmlspecialchars($debugInfo['tenant_data']['business_name']) ?></p>
                        <p><strong>Subdomain:</strong> <?= htmlspecialchars($debugInfo['tenant_data']['subdomain']) ?></p>
                        <p><strong>Status:</strong> <?= htmlspecialchars($debugInfo['tenant_data']['status']) ?></p>
                    <?php endif; ?>
                <?php else: ?>
                    <p><strong>❌ No tenant detected!</strong></p>
                <?php endif; ?>
            </div>

            <?php if ($debugInfo['stats']): ?>
                <div class="info">
                    <h3>📊 Database Test</h3>
                    <p><strong>Total customers (no filter):</strong> <?= $debugInfo['stats']['total_customers'] ?></p>
                    <?php if ($debugInfo['current_tenant']): ?>
                        <p><strong>Customers for current tenant:</strong> <?= $debugInfo['stats']['tenant_customers'] ?></p>
                    <?php endif; ?>

                    <h4>All tenants in database:</h4>
                    <?php $tenants = getAllTenants(); ?>
                    <?php foreach ($tenants as $tenant): ?>
                        <?php $current = ($tenant['id'] === $debugInfo['current_tenant']) ? " ← CURRENT" : ""; ?>
                        <p>- <?= htmlspecialchars($tenant['business_name']) ?> (<?= htmlspecialchars($tenant['subdomain']) ?>) - <?= htmlspecialchars($tenant['id']) ?><?= $current ?></p>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>

            <div class="info">
                <h3>🔗 Test Links</h3>
                <p><a href="https://skrtz.gr/tenant_manager.php?action=debug">Main Domain Debug</a></p>
                <p><a href="https://realma.skrtz.gr/tenant_manager.php?action=debug">Realma Subdomain Debug</a></p>
                <p><a href="https://test.skrtz.gr/tenant_manager.php?action=debug">Test Subdomain Debug</a></p>
            </div>
        </div>

        <!-- Clean & Setup Tab -->
        <div id="clean" class="tab-content <?= $action === 'clean' ? 'active' : '' ?>">
            <h2>🧹 Clean & Setup Database</h2>

            <div class="warning">
                <strong>⚠️ WARNING:</strong> This will completely reset your database!
                <ul>
                    <li>All customers, reservations, employees, and services will be deleted</li>
                    <li>All custom settings and texts will be lost</li>
                    <li>Fresh dummy data will be created</li>
                    <li>Multi-tenant system will be set up automatically</li>
                </ul>
                <strong>This action cannot be undone!</strong>
            </div>

            <div class="info">
                <h3>🚀 What This Will Do:</h3>
                <ol>
                    <li>Delete the data folder completely</li>
                    <li>Create a fresh database with dummy data</li>
                    <li>Add multi-tenant support automatically</li>
                    <li>Initialize default tenant settings</li>
                    <li>Set up everything ready for use</li>
                </ol>
            </div>

            <form method="POST">
                <input type="hidden" name="action" value="clean_setup">
                <button type="submit" class="btn btn-danger"
                    onclick="return confirm('Are you absolutely sure? This will delete ALL data and cannot be undone!')">
                    🧹 Clean & Setup Database
                </button>
            </form>
        </div>

        <!-- Migration Tab -->
        <div id="migration" class="tab-content <?= $action === 'migration' ? 'active' : '' ?>">
            <h2>🚀 Migration Tools</h2>

            <div class="info">
                <h3>📋 Available Migrations:</h3>
                <ul>
                    <li><strong>Tenant System Migration:</strong> Adds multi-tenant support to existing database</li>
                    <li><strong>File-to-Database Migration:</strong> Moves file-based settings to database</li>
                </ul>
            </div>

            <h3>🏢 Tenant System Migration</h3>
            <p>This migration adds tenant support to your existing database. It's safe to run multiple times.</p>
            <form method="POST" style="margin-bottom: 30px;">
                <input type="hidden" name="action" value="run_migration">
                <button type="submit" class="btn btn-primary">🚀 Run Tenant Migration</button>
            </form>

            <h3>📁 File-to-Database Migration</h3>
            <p>This migration moves your file-based settings and texts to the database for tenant isolation.</p>
            <form method="POST">
                <input type="hidden" name="action" value="file_migration">
                <button type="submit" class="btn btn-success">📁 Run File Migration</button>
            </form>
        </div>

        <!-- Test Tab -->
        <div id="test" class="tab-content <?= $action === 'test' ? 'active' : '' ?>">
            <h2>🧪 Test Tenant Isolation</h2>

            <div class="info">
                <h3>📋 What This Tests:</h3>
                <ul>
                    <li>Customer Handler tenant filtering</li>
                    <li>Employee Handler tenant filtering</li>
                    <li>Service Handler tenant filtering</li>
                    <li>Reservation Handler tenant filtering</li>
                </ul>
            </div>

            <?php if ($_GET['run_test'] === '1'): ?>
                <?php $testResults = testTenantIsolation(); ?>

                <div class="<?= $testResults['success'] ? 'success' : 'error' ?>">
                    <h3><?= $testResults['success'] ? '✅ Test Results' : '❌ Test Failed' ?></h3>

                    <?php if ($testResults['success']): ?>
                        <p><strong>Current Tenant:</strong> <?= htmlspecialchars($testResults['tenant_id']) ?></p>

                        <h4>Handler Results:</h4>
                        <ul>
                            <li><strong>Customer Handler:</strong>
                                getAllCustomers: <?= $testResults['customer_handler']['getAllCustomers'] ?>,
                                getCustomerCount: <?= $testResults['customer_handler']['getCustomerCount'] ?>
                            </li>
                            <li><strong>Employee Handler:</strong>
                                getAllEmployees: <?= $testResults['employee_handler']['getAllEmployees'] ?>
                            </li>
                            <li><strong>Service Handler:</strong>
                                getAllServices: <?= $testResults['service_handler']['getAllServices'] ?>
                            </li>
                            <li><strong>Reservation Handler:</strong>
                                getAllReservations: <?= $testResults['reservation_handler']['getAllReservations'] ?>
                            </li>
                        </ul>
                    <?php else: ?>
                        <p><strong>Error:</strong> <?= htmlspecialchars($testResults['error']) ?></p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <a href="?action=test&run_test=1" class="btn btn-warning">🧪 Run Isolation Test</a>
        </div>
    </div>

    <script>
        // Tab functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            const selectedContent = document.getElementById(tabName);
            if (selectedContent) {
                selectedContent.classList.add('active');
            }

            // Add active class to selected tab
            const selectedTab = document.querySelector(`[onclick="showTab('${tabName}')"]`);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // Update URL without page reload
            const url = new URL(window.location);
            url.searchParams.set('action', tabName);
            window.history.pushState({}, '', url);
        }

        // Update confirmation field for delete business
        function updateConfirmation() {
            const select = document.getElementById('tenant_id');
            const confirmInput = document.getElementById('confirm_name');
            const hint = document.getElementById('confirm_hint');

            if (select.selectedIndex > 0) {
                const selectedOption = select.options[select.selectedIndex];
                const businessName = selectedOption.getAttribute('data-name');
                hint.textContent = `Type exactly: "${businessName}"`;
                confirmInput.placeholder = businessName;
            } else {
                hint.textContent = '';
                confirmInput.placeholder = 'Enter exact business name';
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Get current action from URL or default to dashboard
            const urlParams = new URLSearchParams(window.location.search);
            const currentAction = urlParams.get('action') || 'dashboard';

            // Show the appropriate tab
            showTab(currentAction);
        });

        // Handle browser back/forward buttons
        window.addEventListener('popstate', function(event) {
            const urlParams = new URLSearchParams(window.location.search);
            const currentAction = urlParams.get('action') || 'dashboard';
            showTab(currentAction);
        });
    </script>

</body>

</html>