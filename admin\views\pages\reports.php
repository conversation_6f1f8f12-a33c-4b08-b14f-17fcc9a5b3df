<?php
// Show recent activity and reservation stats
$activityLog = getRecentActivity(50);
$totalReservations = count($reservationHandler->getAllReservations());
$totalCustomers = count($customerHandler->getAllCustomers());
?>
<div style="padding:20px;">
    <h2>Reports</h2>
    <div>
        <strong>Total Reservations:</strong> <?= $totalReservations ?><br>
        <strong>Total Customers:</strong> <?= $totalCustomers ?><br>
    </div>
    <h3 style="margin-top:30px;">Recent Activity</h3>
    <ul>
        <?php foreach ($activityLog as $activity): ?>
            <li>
                <strong><?= date('Y-m-d H:i', strtotime($activity['timestamp'])) ?>:</strong>
                <?= htmlspecialchars($activity['message']) ?>
            </li>
        <?php endforeach; ?>
    </ul>
</div>