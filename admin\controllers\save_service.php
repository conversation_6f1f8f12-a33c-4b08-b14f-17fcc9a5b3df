<?php

require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/admin_functions.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$serviceId = sanitize_input($_POST['service_id'] ?? '');
$serviceName = sanitize_input($_POST['service_name'] ?? '');
$duration = intval($_POST['duration'] ?? 0);
$price = floatval($_POST['price'] ?? 0);
$description = sanitize_input($_POST['service_description'] ?? '');
$allowEmployeeSelection = isset($_POST['allow_employee_selection']) ? 1 : 0;

if (!$serviceName || !$duration || !$price) {
    echo json_encode(['success' => false, 'message' => 'All fields are required']);
    exit;
}

if ($serviceId) {
    // Update existing
    $result = updateService($serviceId, $serviceName, $duration, $price, $description, $allowEmployeeSelection);
} else {
    // Add new
    $serviceId = generate_service_id();
    $result = addService($serviceId, $serviceName, $duration, $price, $description, $allowEmployeeSelection);
}

if ($result) {
    echo json_encode(['success' => true, 'message' => 'Service saved successfully', 'refresh' => true]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to save service']);
}
