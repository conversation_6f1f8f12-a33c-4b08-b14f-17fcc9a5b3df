<?php

/**
 * ========================================
 * MULTILINGUAL EMAIL TEMPLATE GENERATOR
 * ========================================
 * 
 * This script generates all email templates using the
 * EmailTemplateCreator class for consistent styling
 * and multilingual support.
 * 
 * Run this script to regenerate all email templates
 * with unified styling, inline CSS, and multiple languages.
 * 
 * <AUTHOR> System
 * @version 2.0
 */

require_once __DIR__ . '/email_template_creator.php';

class EmailTemplateGenerator
{
    private $creator;
    private $templatesDir;

    public function __construct()
    {
        $this->creator = new EmailTemplateCreator();
        $this->templatesDir = __DIR__ . '/templates/';
    }

    /**
     * Generate all email templates for all languages
     */
    public function generateAllTemplates()
    {
        echo "🚀 Starting multilingual email template generation...\n\n";

        $languages = ['en', 'el'];

        foreach ($languages as $lang) {
            echo "🌐 Generating templates for language: " . strtoupper($lang) . "\n";

            // Customer templates
            $this->generateConfirmationTemplate($lang);
            $this->generateCancellationTemplate($lang);
            $this->generateReminderTemplate($lang);
            $this->generateWelcomeTemplate($lang);
            $this->generateVerificationTemplate($lang);

            // Admin templates
            $this->generateAdminNewReservationTemplate($lang);
            $this->generateAdminCancellationTemplate($lang);
            $this->generateAdminNewCustomerTemplate($lang);
            $this->generateAdminCustomerDeletionTemplate($lang);

            echo "\n";
        }

        echo "✅ All multilingual email templates generated successfully!\n";
        echo "📁 Templates saved in: " . $this->templatesDir . "\n";
        echo "🌐 Languages: English (en), Greek (el)\n";
    }

    /**
     * Generate confirmation email template
     */
    private function generateConfirmationTemplate($language)
    {
        $content = $this->creator->generateGreeting($language, 'confirmation_greeting') . '
            
            ' . $this->creator->generateParagraph($language, 'confirmation_message') . '
            
            ' . $this->creator->generateReservationId($this->creator->getText($language, 'field_id', 'ID') . ': {{reservation_id}}') . '
            
            ' . $this->creator->generateDetailSection($language, 'confirmation_details_title', '📋', [
            'field_service' => '{{service_name}}',
            'field_date' => '{{date}}',
            'field_time' => '{{time}}',
            'field_price' => '€{{service_price}}'
        ]) . '
            
            ' . $this->creator->generateParagraph($language, 'confirmation_thanks');

        $html = $this->creator->generateTemplate(
            'confirmation',
            $language,
            'confirmation_title',
            'confirmation_header',
            $content
        );

        $filename = $language === 'en' ? 'confirmation.html' : 'confirmation_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate cancellation email template
     */
    private function generateCancellationTemplate($language)
    {
        $content = $this->creator->generateGreeting($language, 'cancellation_greeting') . '
            
            ' . $this->creator->generateParagraph($language, 'cancellation_message') . '
            
            ' . $this->creator->generateReservationId($this->creator->getText($language, 'field_id', 'ID') . ': {{reservation_id}}') . '
            
            ' . $this->creator->generateDetailSection($language, 'cancellation_details_title', '📋', [
            'field_service' => '{{service_name}}',
            'field_date' => '{{date}}',
            'field_time' => '{{time}}'
        ]) . '
            
            ' . $this->creator->generateParagraph($language, 'cancellation_thanks');

        $html = $this->creator->generateTemplate(
            'cancellation',
            $language,
            'cancellation_title',
            'cancellation_header',
            $content
        );

        $filename = $language === 'en' ? 'cancellation.html' : 'cancellation_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate reminder email template
     */
    private function generateReminderTemplate($language)
    {
        $content = $this->creator->generateGreeting($language, 'reminder_greeting') . '
            
            ' . $this->creator->generateParagraph($language, 'reminder_message') . '
            
            ' . $this->creator->generateHighlightBox($language, 'reminder_countdown_title', 'reminder_countdown_text') . '
            
            ' . $this->creator->generateReservationId($this->creator->getText($language, 'field_id', 'ID') . ': {{reservation_id}}') . '
            
            ' . $this->creator->generateDetailSection($language, 'reminder_details_title', '📋', [
            'field_service' => '{{service_name}}',
            'field_date' => '{{date}}',
            'field_time' => '{{time}}',
            'field_duration' => '{{duration}} minutes',
            'field_price' => '€{{price}}'
        ]) . '
            
            ' . $this->creator->generateParagraph($language, 'reminder_thanks');

        $html = $this->creator->generateTemplate(
            'reminder',
            $language,
            'reminder_title',
            'reminder_header',
            $content
        );

        $filename = $language === 'en' ? 'reminder.html' : 'reminder_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate welcome email template
     */
    private function generateWelcomeTemplate($language)
    {
        $content = $this->creator->generateGreeting($language, 'welcome_greeting') . '
            
            ' . $this->creator->generateParagraph($language, 'welcome_message') . '
            
            ' . $this->creator->generateAccessCode(
            $language,
            'welcome_access_title',
            'welcome_access_desc',
            '{{user_hash}}',
            'welcome_access_note'
        ) . '
            
            ' . $this->creator->generateParagraph($language, 'welcome_final');

        $html = $this->creator->generateTemplate(
            'welcome',
            $language,
            'welcome_title',
            'welcome_header',
            $content
        );

        $filename = $language === 'en' ? 'welcome.html' : 'welcome_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate verification email template
     */
    private function generateVerificationTemplate($language)
    {
        $content = '<h2 style="text-align:center;margin:0 0 25px 0;font-size:22px;color:#2c3e50">' .
            $this->creator->getText($language, 'verification_main_title', 'Your Verification Code') . '</h2>
            <p style="text-align:center;margin:0 0 25px 0;font-size:16px;color:#555">' .
            $this->creator->getText($language, 'verification_desc', 'Please enter this code to continue:') . '</p>
            
            ' . $this->creator->generateVerificationCode('{{code}}') . '
            
            <p style="text-align:center;margin:25px 0 0 0;font-size:14px;color:#666">' .
            $this->creator->getText($language, 'verification_expiry', 'This code expires in {{expiry_minutes}} minutes.') . '</p>';

        $html = $this->creator->generateTemplate(
            'verification',
            $language,
            'verification_title',
            'verification_header',
            $content
        );

        $filename = $language === 'en' ? 'verification.html' : 'verification_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate admin new reservation template
     */
    private function generateAdminNewReservationTemplate($language)
    {
        $content = $this->creator->generateDetailSection($language, 'admin_customer_section', '👤', [
            'field_name' => '{{customer_name}}',
            'field_email' => '{{customer_email}}',
            'field_mobile' => '{{customer_mobile}}'
        ]) . '

            ' . $this->creator->generateDetailSection($language, 'admin_reservation_section', '📅', [
            'field_id' => '{{reservation_id}}',
            'field_service' => '{{service_name}}',
            'field_date' => '{{date}}',
            'field_time' => '{{time}}',
            'field_price' => '€{{price}}'
        ]);

        $html = $this->creator->generateTemplate(
            'admin_new_reservation',
            $language,
            'admin_new_reservation_title',
            'admin_new_reservation_header',
            $content
        );

        $filename = $language === 'en' ? 'admin_new_reservation.html' : 'admin_new_reservation_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate admin cancellation template
     */
    private function generateAdminCancellationTemplate($language)
    {
        $content = $this->creator->generateDetailSection($language, 'admin_customer_section', '👤', [
            'field_name' => '{{customer_name}}',
            'field_email' => '{{customer_email}}'
        ]) . '

            ' . $this->creator->generateDetailSection($language, 'admin_cancelled_section', '🚫', [
            'field_id' => '{{reservation_id}}',
            'field_service' => '{{service_name}}',
            'field_date' => '{{date}}',
            'field_time' => '{{time}}',
            'field_lost_revenue' => '€{{price}}'
        ]);

        $html = $this->creator->generateTemplate(
            'admin_cancellation',
            $language,
            'admin_cancellation_title',
            'admin_cancellation_header',
            $content
        );

        $filename = $language === 'en' ? 'admin_cancellation.html' : 'admin_cancellation_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate admin new customer template
     */
    private function generateAdminNewCustomerTemplate($language)
    {
        $content = $this->creator->generateDetailSection($language, 'admin_customer_details_section', '👤', [
            'field_name' => '{{customer_name}}',
            'field_email' => '{{customer_email}}',
            'field_mobile' => '{{customer_mobile}}',
            'field_registered' => '{{registration_date}}'
        ]);

        $html = $this->creator->generateTemplate(
            'admin_new_customer',
            $language,
            'admin_new_customer_title',
            'admin_new_customer_header',
            $content
        );

        $filename = $language === 'en' ? 'admin_new_customer.html' : 'admin_new_customer_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Generate admin customer deletion template
     */
    private function generateAdminCustomerDeletionTemplate($language)
    {
        $content = $this->creator->generateDetailSection($language, 'admin_deleted_customer_section', '🗑️', [
            'field_name' => '{{customer_name}}',
            'field_email' => '{{customer_email}}',
            'field_deleted_at' => '{{deleted_at}}',
            'field_total_reservations' => '{{total_reservations}}'
        ]);

        $html = $this->creator->generateTemplate(
            'admin_customer_deletion',
            $language,
            'admin_customer_deletion_title',
            'admin_customer_deletion_header',
            $content
        );

        $filename = $language === 'en' ? 'admin_customer_deletion.html' : 'admin_customer_deletion_' . $language . '.html';
        $this->saveTemplate($filename, $html);
        echo "✅ Generated: $filename\n";
    }

    /**
     * Save template to file
     */
    private function saveTemplate($filename, $html)
    {
        file_put_contents($this->templatesDir . $filename, $html);
    }
}

// Run the generator if this script is executed directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $generator = new EmailTemplateGenerator();
    $generator->generateAllTemplates();
}
