<?php
// Try to load tenant system, but continue if it fails
try {
    require_once '../includes/tenant_init.php';
} catch (Exception $e) {
    // Log error but continue without tenant system
    error_log('Tenant system error in login: ' . $e->getMessage());
}

session_start();

// Load settings with tenant awareness
try {
    require_once '../includes/functions.php';
    $settings = getSettings();
} catch (Exception $e) {
    error_log('Settings error in login: ' . $e->getMessage());
    // Fallback to default settings
    $settings = [
        'site_name' => 'Booking System',
        'email_from' => '<EMAIL>',
        'admin_email' => '<EMAIL>'
    ];
}

// If already logged in, redirect to dashboard
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: index.php');
    exit();
}

require_once '../includes/config.php';
require_once '../includes/functions.php';

// Default admin credentials (change these in production!)
define('ADMIN_USERNAME', 'admin');
define('ADMIN_PASSWORD', 'admin'); // Change this password!

$error = '';
$loginAttempts = $_SESSION['login_attempts'] ?? 0;
$lastAttempt = $_SESSION['last_attempt'] ?? 0;

// Rate limiting: max 5 attempts per 15 minutes
$maxAttempts = 5;
$lockoutTime = 900; // 15 minutes

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize_input($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $currentTime = time();

    // Check if account is locked
    if ($loginAttempts >= $maxAttempts && ($currentTime - $lastAttempt) < $lockoutTime) {
        $remainingTime = $lockoutTime - ($currentTime - $lastAttempt);
        $minutes = ceil($remainingTime / 60);
        $error = "Too many failed attempts. Please try again in {$minutes} minutes.";
    } else {
        // Reset attempts if lockout period has passed
        if (($currentTime - $lastAttempt) >= $lockoutTime) {
            $loginAttempts = 0;
        }

        // Validate credentials
        if (empty($username) || empty($password)) {
            $error = 'Please enter both username and password.';
            $loginAttempts++;
        } elseif ($username === ADMIN_USERNAME && $password === ADMIN_PASSWORD) {
            // Successful login
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_username'] = $username;
            $_SESSION['admin_login_time'] = $currentTime;

            // Reset login attempts
            unset($_SESSION['login_attempts']);
            unset($_SESSION['last_attempt']);

            // Log successful login
            log_activity("Admin login successful: {$username}");

            // Redirect to dashboard
            header('Location: index.php');
            exit();
        } else {
            $error = 'Invalid username or password.';
            $loginAttempts++;
            log_activity("Admin login failed: {$username}");
        }

        // Update session with attempt info
        $_SESSION['login_attempts'] = $loginAttempts;
        $_SESSION['last_attempt'] = $currentTime;
    }
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <?php echo htmlspecialchars($settings['site_name']); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/admin.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            animation: slideUp 0.5s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header i {
            font-size: 3rem;
            margin-bottom: 10px;
            opacity: 0.9;
        }

        .form-group {
            position: relative;
        }

        .form-group i {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            margin-top: 12px;
        }

        .password-toggle {
            cursor: pointer;
            user-select: none;
        }

        .password-toggle:hover {
            color: #4caf50;
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-header">
            <i class="fas fa-user-shield"></i>
            <h1><?php echo htmlspecialchars($settings['site_name']); ?></h1>
            <p>Admin Panel Access</p>
        </div>

        <form class="login-form" method="POST" action="">
            <?php if ($error): ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($loginAttempts > 0 && $loginAttempts < $maxAttempts): ?>
                <div class="attempts-warning">
                    <i class="fas fa-exclamation-circle"></i>
                    Warning: <?php echo $loginAttempts; ?> failed attempt(s).
                    <?php echo ($maxAttempts - $loginAttempts); ?> attempts remaining.
                </div>
            <?php endif; ?>

            <div class="security-info">
                <i class="fas fa-info-circle"></i>
                Secure admin access. All login attempts are logged.
            </div>

            <div class="form-group">
                <label for="username">
                    <i class="fas fa-user"></i> Username
                </label>
                <input type="text"
                    id="username"
                    name="username"
                    required
                    autocomplete="username"
                    value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                    <?php echo ($loginAttempts >= $maxAttempts && (time() - $lastAttempt) < $lockoutTime) ? 'disabled' : ''; ?>>
            </div>

            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i> Password
                </label>
                <input type="password"
                    id="password"
                    name="password"
                    required
                    autocomplete="current-password"
                    <?php echo ($loginAttempts >= $maxAttempts && (time() - $lastAttempt) < $lockoutTime) ? 'disabled' : ''; ?>>
                <i class="fas fa-eye password-toggle" onclick="togglePassword()"></i>
            </div>

            <button type="submit"
                class="login-btn"
                <?php echo ($loginAttempts >= $maxAttempts && (time() - $lastAttempt) < $lockoutTime) ? 'disabled' : ''; ?>>
                <i class="fas fa-sign-in-alt"></i>
                <?php echo ($loginAttempts >= $maxAttempts && (time() - $lastAttempt) < $lockoutTime) ? 'Account Locked' : 'Login to Dashboard'; ?>
            </button>
        </form>

        <div class="login-footer">
            <p>
                <i class="fas fa-shield-alt"></i>
                Protected by security monitoring
            </p>
            <a href="../index.php" class="back-link">
                <i class="fas fa-arrow-left"></i>
                Back to Booking Site
            </a>
        </div>
    </div>

    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.querySelector('.password-toggle');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Auto-focus username field
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('username');
            if (usernameField && !usernameField.disabled) {
                usernameField.focus();
            }
        });

        // Prevent form submission if account is locked
        document.querySelector('.login-form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('.login-btn');
            if (submitBtn.disabled) {
                e.preventDefault();
                return false;
            }
        });
    </script>
</body>

</html>