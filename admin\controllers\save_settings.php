<?php

require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/TenantSettingsManager.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Debug logging
    error_log("Save Settings - POST data: " . json_encode($_POST));

    $siteName = trim($_POST['site_name'] ?? '');
    $emailFrom = trim($_POST['email_from'] ?? '');
    $adminEmail = trim($_POST['admin_email'] ?? '');

    error_log("Save Settings - Parsed: siteName=$siteName, emailFrom=$emailFrom, adminEmail=$adminEmail");

    if (empty($siteName) || empty($adminEmail)) {
        error_log("Save Settings - Validation failed: empty siteName or adminEmail");
        echo json_encode(['success' => false, 'message' => 'Site name and admin email are required']);
        exit;
    }

    // Save basic settings using tenant-aware manager
    $success = true;

    error_log("Save Settings - Attempting to save site_name: $siteName");
    $result1 = TenantSettingsManager::setSetting('site_name', $siteName);
    error_log("Save Settings - site_name result: " . ($result1 ? 'success' : 'failed'));
    $success &= $result1;

    if ($emailFrom) {
        error_log("Save Settings - Attempting to save email_from: $emailFrom");
        $result2 = TenantSettingsManager::setSetting('email_from', $emailFrom);
        error_log("Save Settings - email_from result: " . ($result2 ? 'success' : 'failed'));
        $success &= $result2;
    }

    error_log("Save Settings - Attempting to save admin_email: $adminEmail");
    $result3 = TenantSettingsManager::setSetting('admin_email', $adminEmail);
    error_log("Save Settings - admin_email result: " . ($result3 ? 'success' : 'failed'));
    $success &= $result3;

    // Only handle business hours and special days if they are provided in the form
    // This prevents the modal from deleting existing data
    if (isset($_POST['bh_Monday_start1']) || isset($_POST['special_days_text'])) {
        // Handle business hours (up to 2 intervals per day)
        $businessHours = [];
        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        foreach ($days as $day) {
            $intervals = [];
            for ($i = 1; $i <= 2; $i++) {
                $start = trim($_POST["bh_{$day}_start{$i}"] ?? '');
                $end = trim($_POST["bh_{$day}_end{$i}"] ?? '');
                if ($start && $end) {
                    $intervals[] = ['start' => $start, 'end' => $end];
                }
            }
            $businessHours[$day] = $intervals;
        }

        // Handle special days from textarea
        $specialDays = [];
        $specialDaysText = trim($_POST['special_days_text'] ?? '');
        if ($specialDaysText) {
            $entries = explode('|', $specialDaysText);
            foreach ($entries as $entry) {
                $entry = trim($entry);
                if (!$entry) continue;
                $parts = preg_split('/\s+/', $entry);
                $date = array_shift($parts);
                $intervals = [];
                foreach ($parts as $interval) {
                    if (preg_match('/^([0-2][0-9]:[0-5][0-9])-([0-2][0-9]:[0-5][0-9])$/', $interval, $m)) {
                        $intervals[] = ['start' => $m[1], 'end' => $m[2]];
                    }
                }
                $specialDays[$date] = $intervals; // empty array = closed
            }
        }

        $success &= TenantSettingsManager::setSetting('business_hours', $businessHours);
        $success &= TenantSettingsManager::setSetting('special_days', $specialDays);
    }

    if ($success) {
        log_activity("Settings updated: Site name = $siteName, Admin email = $adminEmail");
        echo json_encode(['success' => true, 'message' => 'Settings saved successfully', 'refresh' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save settings']);
    }
} catch (Exception $e) {
    log_activity("Settings save error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
