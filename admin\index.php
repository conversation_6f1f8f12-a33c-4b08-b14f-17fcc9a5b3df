<?php
// Try to load tenant system, but continue if it fails
try {
    require_once '../includes/tenant_init.php';
} catch (Exception $e) {
    // Log error but continue without tenant system
    error_log('Tenant system error: ' . $e->getMessage());
}

session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Get tenant-aware settings with detailed error handling
try {
    $settings = getSettings();
    error_log('Admin panel - Settings loaded successfully: ' . json_encode($settings));
} catch (Exception $e) {
    error_log('Settings error in admin/index.php: ' . $e->getMessage());
    error_log('Settings error file: ' . $e->getFile() . ' line ' . $e->getLine());
    error_log('Settings error trace: ' . $e->getTraceAsString());

    // Try to get settings directly from TenantSettingsManager
    try {
        require_once '../includes/TenantSettingsManager.php';
        $settings = TenantSettingsManager::getAllSettings();
        error_log('Admin panel - Direct TenantSettingsManager loaded: ' . json_encode($settings));

        if (empty($settings)) {
            // Initialize defaults if empty
            require_once '../includes/TenantContext.php';
            $tenantId = TenantContext::getTenant();
            if ($tenantId) {
                TenantSettingsManager::initializeDefaultSettings($tenantId);
                $settings = TenantSettingsManager::getAllSettings();
                error_log('Admin panel - Initialized defaults: ' . json_encode($settings));
            }
        }
    } catch (Exception $e2) {
        error_log('Direct TenantSettingsManager error: ' . $e2->getMessage());
        $settings = [];
    }

    // Final fallback to default settings only if still empty
    if (empty($settings)) {
        $settings = [
            'site_name' => 'Booking System',
            'email_from' => '<EMAIL>',
            'admin_email' => '<EMAIL>',
            'business_hours' => [],
            'special_days' => []
        ];
        error_log('Admin panel - Using final fallback settings');
    }
} catch (Error $e) {
    error_log('Fatal settings error in admin/index.php: ' . $e->getMessage());
    error_log('Fatal error file: ' . $e->getFile() . ' line ' . $e->getLine());

    // Fallback to default settings
    $settings = [
        'site_name' => 'Booking System',
        'email_from' => '<EMAIL>',
        'admin_email' => '<EMAIL>',
        'business_hours' => [],
        'special_days' => []
    ];
}
require_once '../includes/reservation_handler.php';
require_once '../includes/customer_handler.php';
require_once '../includes/admin_functions.php';
require_once '../includes/employee_handler.php';
require_once '../includes/service_handler.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

// Initialize handlers
$reservationHandler = new ReservationHandler();
$customerHandler = new CustomerHandler();
$employeeHandler = new EmployeeHandler();
$serviceHandler = new ServiceHandler();

// Handle actions
$action = $_GET['action'] ?? 'dashboard';
$message = '';
$error = '';

// Process POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $postAction = $_POST['action'] ?? '';

    switch ($postAction) {
        case 'cancel_reservation':
            $reservationId = sanitize_input($_POST['reservation_id'] ?? '');
            $reason = sanitize_input($_POST['reason'] ?? 'Cancelled by admin');

            if (!empty($reservationId)) {
                $result = $reservationHandler->cancelReservation($reservationId, null, $reason, false, true);
                if ($result['success']) {
                    $message = "Reservation {$reservationId} has been cancelled successfully.";
                    log_activity("Admin cancelled reservation: {$reservationId}");
                } else {
                    $error = $result['message'];
                }
            }
            break;

        case 'update_reservation':
            $reservationId = sanitize_input($_POST['reservation_id'] ?? '');
            $newDate = sanitize_input($_POST['new_date'] ?? '');
            $newTime = sanitize_input($_POST['new_time'] ?? '');
            $newService = sanitize_input($_POST['new_service'] ?? '');

            if (!empty($reservationId) && !empty($newDate) && !empty($newTime)) {
                $result = $reservationHandler->updateReservation($reservationId, $newDate, $newTime, $newService);
                if ($result['success']) {
                    $message = "Reservation {$reservationId} has been updated successfully.";
                    log_activity("Admin updated reservation: {$reservationId}");
                } else {
                    $error = $result['message'];
                }
            }
            break;

        case 'add_service':
            $serviceId = sanitize_input($_POST['service_id'] ?? '');
            $serviceName = sanitize_input($_POST['service_name'] ?? '');
            $duration = (int)($_POST['duration'] ?? 0);
            $price = (float)($_POST['price'] ?? 0);

            if (!empty($serviceId) && !empty($serviceName) && $duration > 0 && $price > 0) {
                $result = addService($serviceId, $serviceName, $duration, $price);
                if ($result) {
                    $message = "Service '{$serviceName}' has been added successfully.";
                    log_activity("Admin added service: {$serviceId} - {$serviceName}");
                } else {
                    $error = "Failed to add service. Service ID might already exist.";
                }
            }
            break;

        case 'update_business_hours':
            $businessHours = $_POST['business_hours'] ?? [];
            $result = updateBusinessHours($businessHours);
            if ($result) {
                $message = "Business hours have been updated successfully.";
                log_activity("Admin updated business hours");
            } else {
                $error = "Failed to update business hours.";
            }
            break;
    }
}

// Get dashboard data
$today = date('Y-m-d');
$tomorrow = date('Y-m-d', strtotime('+1 day'));
$thisWeek = date('Y-m-d', strtotime('monday this week'));
$nextWeek = date('Y-m-d', strtotime('monday next week'));

$stats = [
    'today_reservations' => count($reservationHandler->getReservationsByDate($today)),
    'tomorrow_reservations' => count($reservationHandler->getReservationsByDate($tomorrow)),
    'this_week_reservations' => count($reservationHandler->getReservationsByDateRange($thisWeek, date('Y-m-d', strtotime('sunday this week')))),
    'total_customers' => count($customerHandler->getAllCustomers()),
    'pending_reservations' => count($reservationHandler->getReservationsByStatus('confirmed')),
    'cancelled_today' => count($reservationHandler->getReservationsByDateAndStatus($today, 'cancelled'))
];

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo htmlspecialchars($settings['site_name']); ?></title>
    <link rel="stylesheet" href="assets/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

</head>

<body>
    <div class="admin-container">
        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebar-overlay"></div>

        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-spa"></i> <?php echo htmlspecialchars($settings['site_name']); ?></h2>
                <p>Admin Panel</p>
            </div>

            <ul class="sidebar-menu">
                <li class="<?php echo $action === 'dashboard' ? 'active' : ''; ?>">
                    <a href="?action=dashboard">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="<?php echo $action === 'reservations' ? 'active' : ''; ?>">
                    <a href="?action=reservations">
                        <i class="fas fa-calendar-check"></i> Reservations
                    </a>
                </li>
                <li class="<?php echo $action === 'customers' ? 'active' : ''; ?>">
                    <a href="?action=customers">
                        <i class="fas fa-users"></i> Customers
                    </a>
                </li>
                <li class="<?php echo $action === 'services' ? 'active' : ''; ?>">
                    <a href="?action=services">
                        <i class="fas fa-concierge-bell"></i> Services
                    </a>
                </li>
                <li class="<?php echo $action === 'employees' ? 'active' : ''; ?>">
                    <a href="?action=employees">
                        <i class="fas fa-user-tie"></i> Employees
                    </a>
                </li>
                <li class="<?php echo $action === 'schedule' ? 'active' : ''; ?>">
                    <a href="?action=schedule">
                        <i class="fas fa-clock"></i> Schedule
                    </a>
                </li>
                <li class="<?php echo $action === 'reports' ? 'active' : ''; ?>">
                    <a href="?action=reports">
                        <i class="fas fa-chart-bar"></i> Reports
                    </a>
                </li>
                <li class="<?php echo $action === 'text_management' ? 'active' : ''; ?>">
                    <a href="?action=text_management">
                        <i class="fas fa-edit"></i> Text Management
                    </a>
                </li>
                <li class="<?php echo $action === 'settings' ? 'active' : ''; ?>">
                    <a href="?action=settings">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </li>
            </ul>

            <div class="sidebar-footer">
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <header class="main-header">
                <div class="header-left">
                    <button id="sidebar-toggle" class="mobile-menu-btn">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1>
                        <?php
                        switch ($action) {
                            case 'dashboard':
                                echo 'Dashboard';
                                break;
                            case 'reservations':
                                echo 'Reservations Management';
                                break;
                            case 'customers':
                                echo 'Customer Management';
                                break;
                            case 'services':
                                echo 'Services Management';
                                break;
                            case 'employees':
                                echo 'Employee Management';
                                break;
                            case 'schedule':
                                echo 'Schedule Management';
                                break;
                            case 'reports':
                                echo 'Reports & Analytics';
                                break;
                            case 'settings':
                                echo 'Settings';
                                break;
                            case 'text_management':
                                echo 'Text Management';
                                break;
                            default:
                                echo 'Dashboard';
                        }
                        ?>
                    </h1>
                </div>
                <div class="header-actions">
                    <span class="admin-info">
                        <i class="fas fa-user-shield"></i>
                        Welcome, Admin
                    </span>
                </div>
            </header>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <div class="content-area">
                <?php
                switch ($action) {
                    case 'dashboard':
                        include 'views/dashboard.php';
                        break;
                    case 'reservations':
                        include 'views/reservations.php';
                        break;
                    case 'customers':
                        include 'views/customers.php';
                        break;
                    case 'services':
                        include 'views/services.php';
                        break;
                    case 'employees':
                        include 'views/employees.php';
                        break;
                    case 'schedule':
                        include 'views/schedule.php';
                        break;
                    case 'reports':
                        include 'views/reports.php';
                        break;
                    case 'settings':
                        include 'views/settings.php';
                        break;
                    case 'text_management':
                        include 'views/text_management.php';
                        break;
                    default:
                        include 'views/dashboard.php';
                }
                ?>
            </div>
        </main>
    </div>

    <!-- Modern Modal Templates -->
    <template id="modal-template">
        <div class="modal-overlay" role="dialog" aria-modal="true">
            <div class="modal" role="document">
                <div class="modal-header">
                    <h3 class="modal-title" id="modal-title"></h3>
                    <button class="modal-close" type="button" aria-label="Close modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="modal-loading" style="display: none;">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>Loading...</span>
                        </div>
                    </div>
                    <div class="modal-content"></div>
                </div>
                <div class="modal-footer" style="display: none;">
                    <!-- Dynamic footer content -->
                </div>
            </div>
        </div>
    </template>

    <!-- Confirmation Modal Template -->
    <template id="confirm-modal-template">
        <div class="modal-overlay" role="dialog" aria-modal="true">
            <div class="modal modal-confirm" role="document">
                <div class="modal-header">
                    <h3 class="modal-title"></h3>
                    <button class="modal-close" type="button" aria-label="Close modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="confirm-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="confirm-message"></div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary cancel-btn" type="button">Cancel</button>
                    <button class="btn btn-danger confirm-btn" type="button">Confirm</button>
                </div>
            </div>
        </div>
    </template>

    <!-- Alert Modal Template -->
    <template id="alert-modal-template">
        <div class="modal-overlay" role="dialog" aria-modal="true">
            <div class="modal modal-alert" role="document">
                <div class="modal-header">
                    <h3 class="modal-title"></h3>
                    <button class="modal-close" type="button" aria-label="Close modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="alert-message"></div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary ok-btn" type="button">OK</button>
                </div>
            </div>
        </div>
    </template>

    <script src="assets/admin.js"></script>
    <script>
        // Update current time
        function updateTime() {
            const timeElement = document.getElementById('current-time');
            if (!timeElement) return; // Skip if element doesn't exist

            const now = new Date();
            const timeString = now.toLocaleString('en-US', {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            timeElement.textContent = timeString;
        }

        updateTime();
        setInterval(updateTime, 1000);

        // Auto-hide alerts after 5 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);
    </script>
</body>

</html>