<?php

require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/customer_handler.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$customerId = sanitize_input($_POST['customer_id'] ?? '');
$customerName = sanitize_input($_POST['customer_name'] ?? '');
$customerEmail = sanitize_input($_POST['customer_email'] ?? '');
$customerMobile = sanitize_input($_POST['customer_mobile'] ?? '');
$customerAddress = sanitize_input($_POST['customer_address'] ?? '');
$customerDob = sanitize_input($_POST['customer_dob'] ?? '');
$customerNotes = sanitize_input($_POST['customer_notes'] ?? '');
$preferredContact = sanitize_input($_POST['preferred_contact'] ?? 'email');
$preferredLanguage = sanitize_input($_POST['preferred_language'] ?? 'el');

if (empty($customerName) || empty($customerEmail)) {
    echo json_encode(['success' => false, 'message' => 'Name and email are required']);
    exit;
}

$customerHandler = new CustomerHandler();

if ($customerId) {
    // Update existing customer
    if (!method_exists($customerHandler, 'updateCustomer')) {
        echo json_encode(['success' => false, 'message' => 'Update function not implemented']);
        exit;
    }
    $result = $customerHandler->updateCustomer(
        $customerId,
        $customerName,
        $customerEmail,
        $customerMobile,
        $customerAddress,
        $customerDob,
        $customerNotes,
        $preferredContact,
        $preferredLanguage
    );
    $successMsg = 'Customer updated successfully';
} else {
    // Add new customer
    if (!method_exists($customerHandler, 'addCustomer')) {
        echo json_encode(['success' => false, 'message' => 'Add function not implemented']);
        exit;
    }
    $result = $customerHandler->addCustomer(
        $customerName,
        $customerEmail,
        $customerMobile,
        $customerAddress,
        $customerDob,
        $customerNotes,
        $preferredContact,
        $preferredLanguage
    );
    $successMsg = 'Customer added successfully';
}

// Add debugging
log_activity("Admin attempting to save customer: $customerName, $customerEmail, $customerMobile");

if (isset($result['success']) && $result['success']) {
    log_activity("Customer save success: " . json_encode($result));
    echo json_encode(['success' => true, 'message' => $successMsg, 'refresh' => true]);
} else {
    $errorMsg = isset($result['message']) ? $result['message'] : 'An error occurred';
    log_activity("Customer save error: " . json_encode($result));
    echo json_encode(['success' => false, 'message' => $errorMsg]);
}
