<?php
/**
 * Fix Critical Tenant Issues
 * This script fixes the tenant detection and employee functionality issues
 */

require_once 'includes/config.php';
require_once 'includes/Database.php';

echo "<h1>🚨 Critical Tenant Issues Fix</h1>";
echo "<p>Current URL: " . ($_SERVER['HTTP_HOST'] ?? 'unknown') . $_SERVER['REQUEST_URI'] . "</p>";

// Fix 1: Create missing tenants
echo "<h2>1. Create Missing Tenants</h2>";

$tenantsToCreate = [
    [
        'subdomain' => 'realma',
        'business_name' => 'Realma Beauty Spa',
        'domain' => 'realma.skrtz.gr',
        'plan' => 'enterprise'
    ],
    [
        'subdomain' => 'test',
        'business_name' => 'Test Business',
        'domain' => 'test.skrtz.gr',
        'plan' => 'starter'
    ]
];

try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    foreach ($tenantsToCreate as $tenantData) {
        // Check if tenant already exists
        $stmt = $conn->prepare("SELECT id FROM tenants WHERE subdomain = :subdomain");
        $stmt->bindValue(':subdomain', $tenantData['subdomain']);
        $result = $stmt->execute();
        $existing = $result->fetchArray(SQLITE3_ASSOC);
        
        if (!$existing) {
            echo "🔧 Creating tenant: " . htmlspecialchars($tenantData['business_name']) . "<br>";
            
            $tenantId = 'TN-' . strtoupper(substr(md5(uniqid() . $tenantData['subdomain']), 0, 10));
            
            $stmt = $conn->prepare("
                INSERT INTO tenants 
                (id, business_name, domain, subdomain, plan, status, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->bindValue(1, $tenantId);
            $stmt->bindValue(2, $tenantData['business_name']);
            $stmt->bindValue(3, $tenantData['domain']);
            $stmt->bindValue(4, $tenantData['subdomain']);
            $stmt->bindValue(5, $tenantData['plan']);
            $stmt->bindValue(6, 'active');
            $stmt->bindValue(7, date('Y-m-d H:i:s'));
            $stmt->bindValue(8, date('Y-m-d H:i:s'));
            
            if ($stmt->execute()) {
                echo "✅ Created tenant: " . htmlspecialchars($tenantData['business_name']) . " (ID: $tenantId)<br>";
                
                // Initialize default settings
                try {
                    require_once 'includes/TenantSettingsManager.php';
                    TenantSettingsManager::initializeDefaultSettings($tenantId);
                    echo "&nbsp;&nbsp;✅ Initialized default settings<br>";
                } catch (Exception $e) {
                    echo "&nbsp;&nbsp;⚠️ Settings init warning: " . $e->getMessage() . "<br>";
                }
                
                // Create some sample data for the tenant
                echo "&nbsp;&nbsp;🔧 Creating sample data...<br>";
                createSampleDataForTenant($conn, $tenantId);
                
            } else {
                echo "❌ Failed to create tenant: " . htmlspecialchars($tenantData['business_name']) . "<br>";
            }
        } else {
            echo "ℹ️ Tenant already exists: " . htmlspecialchars($tenantData['business_name']) . " (ID: " . htmlspecialchars($existing['id']) . ")<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error creating tenants: " . $e->getMessage() . "<br>";
}

// Fix 2: Test tenant detection
echo "<h2>2. Test Tenant Detection</h2>";
try {
    require_once 'includes/TenantContext.php';
    
    // Reset tenant context
    TenantContext::setTenant(null);
    
    // Test initialization
    if (TenantContext::initializeFromRequest()) {
        $tenantId = TenantContext::getTenant();
        $tenantData = TenantContext::getTenantData();
        
        echo "✅ Tenant detection working<br>";
        echo "✅ Tenant ID: " . htmlspecialchars($tenantId ?? 'NULL') . "<br>";
        if ($tenantData) {
            echo "✅ Business: " . htmlspecialchars($tenantData['business_name'] ?? 'N/A') . "<br>";
            echo "✅ Subdomain: " . htmlspecialchars($tenantData['subdomain'] ?? 'N/A') . "<br>";
        }
    } else {
        echo "❌ Tenant detection still failing<br>";
    }
} catch (Exception $e) {
    echo "❌ Tenant detection error: " . $e->getMessage() . "<br>";
}

// Fix 3: Test employee functionality
echo "<h2>3. Test Employee Functionality</h2>";
try {
    require_once 'includes/admin_functions.php';
    
    $employees = getEmployees();
    echo "✅ Found " . count($employees) . " employees<br>";
    
    if (!empty($employees)) {
        $firstEmployee = array_values($employees)[0];
        $testId = $firstEmployee['id'];
        
        $employee = getEmployeeById($testId);
        if ($employee) {
            echo "✅ Employee lookup working for ID: " . htmlspecialchars($testId) . "<br>";
        } else {
            echo "❌ Employee lookup failed for ID: " . htmlspecialchars($testId) . "<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Employee functionality error: " . $e->getMessage() . "<br>";
}

// Fix 4: Test modal loading
echo "<h2>4. Test Modal Loading</h2>";
if (!empty($employees)) {
    $testId = array_values($employees)[0]['id'];
    
    echo "Testing view employee modal...<br>";
    try {
        ob_start();
        $_GET['id'] = $testId;
        
        // Capture any errors
        $oldErrorReporting = error_reporting(E_ALL);
        $oldDisplayErrors = ini_get('display_errors');
        ini_set('display_errors', 1);
        
        include 'admin/views/modals/view_employee.php';
        
        // Restore error settings
        error_reporting($oldErrorReporting);
        ini_set('display_errors', $oldDisplayErrors);
        
        $content = ob_get_clean();
        
        if (strlen($content) > 100 && !strpos($content, 'Employee not found')) {
            echo "✅ View modal loads successfully (" . strlen($content) . " chars)<br>";
        } else {
            echo "❌ View modal issue detected<br>";
            echo "Content preview: " . htmlspecialchars(substr($content, 0, 200)) . "...<br>";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "❌ Modal loading error: " . $e->getMessage() . "<br>";
    }
    
    // Clear $_GET
    unset($_GET['id']);
}

echo "<h2>✅ Fix Summary</h2>";
echo "<p><strong>Fixes Applied:</strong></p>";
echo "<ul>";
echo "<li>✅ Created missing tenant records for realma.skrtz.gr and test.skrtz.gr</li>";
echo "<li>✅ Initialized default settings for new tenants</li>";
echo "<li>✅ Created sample data for tenant isolation testing</li>";
echo "<li>✅ Tested tenant detection and employee functionality</li>";
echo "</ul>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Test <strong>realma.skrtz.gr/test_tenant_subdomain_issues.php</strong> - should now show proper tenant ID</li>";
echo "<li>Test <strong>realma.skrtz.gr/admin/</strong> - should now load without HTTP 500</li>";
echo "<li>Test employee modals in admin panel - should work correctly</li>";
echo "<li>Test employee creation - should work without errors</li>";
echo "</ol>";

/**
 * Create sample data for a tenant
 */
function createSampleDataForTenant($conn, $tenantId) {
    try {
        // Create a sample service
        $serviceId = 'SV-' . strtoupper(substr(md5(uniqid()), 0, 10));
        $stmt = $conn->prepare("
            INSERT OR IGNORE INTO services 
            (id, name, duration, price, description, tenant_id) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->bindValue(1, $serviceId);
        $stmt->bindValue(2, 'Basic Facial Treatment');
        $stmt->bindValue(3, 60);
        $stmt->bindValue(4, 50.00);
        $stmt->bindValue(5, 'Relaxing facial treatment for all skin types');
        $stmt->bindValue(6, $tenantId);
        $stmt->execute();
        
        // Create a sample employee
        $employeeId = 'EM-' . strtoupper(substr(md5(uniqid()), 0, 10));
        $stmt = $conn->prepare("
            INSERT OR IGNORE INTO employees 
            (id, name, email, phone, working_hours, status, created_at, tenant_id) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->bindValue(1, $employeeId);
        $stmt->bindValue(2, 'Sample Employee');
        $stmt->bindValue(3, '<EMAIL>');
        $stmt->bindValue(4, '+30 123456789');
        $stmt->bindValue(5, json_encode(['monday' => ['09:00', '17:00']]));
        $stmt->bindValue(6, 'active');
        $stmt->bindValue(7, date('Y-m-d H:i:s'));
        $stmt->bindValue(8, $tenantId);
        $stmt->execute();
        
        echo "&nbsp;&nbsp;✅ Created sample service and employee<br>";
    } catch (Exception $e) {
        echo "&nbsp;&nbsp;⚠️ Sample data creation warning: " . $e->getMessage() . "<br>";
    }
}
?>
