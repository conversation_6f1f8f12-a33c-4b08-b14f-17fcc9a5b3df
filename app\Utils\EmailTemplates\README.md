# 📧 Advanced Multilingual Email Template System

A comprehensive email template system with customer language preferences, admin management interface, and automatic language detection.

## 🎯 Key Features

### **🌐 Smart Language System**

- **Customer Language Preferences** - Stored in database, set during registration
- **Admin Primary Language** - All admin emails in Greek (primary language)
- **Automatic Detection** - Fallback to session/browser language if needed
- **No Complex Session Logic** - Simple, reliable database-driven approach

### **🎨 Enhanced Design**

- **Professional Styling** - Perfect balance between minimal and complex
- **Dashed Borders** - All codes, IDs, and access codes with attractive borders
- **Color-Coded Templates** - Each email type has unique colors and icons
- **Mobile Responsive** - Optimized for all devices
- **Email Client Compatible** - Inline CSS for maximum compatibility

### **⚙️ Admin Management**

- **Grouped Interface** - Email fields organized by template type
- **Side-by-Side Editing** - English and Greek fields next to each other
- **Template Status** - Visual indicators for available templates
- **One-Click Regeneration** - Rebuild all templates instantly

## 📁 File Structure

```
includes/email_templates/
├── email_template_creator.php    # Core template generation class with multilingual support
├── email_template_selector.php   # Automatic language detection and template selection
├── email_sender.php             # Enhanced EmailSender with multilingual integration
├── generate_templates.php        # Script to generate all templates in all languages
├── update_styles.php            # Style management utility
├── README.md                    # This documentation
└── templates/                   # Generated email templates
    ├── confirmation.html         # English templates
    ├── cancellation.html
    ├── reminder.html
    ├── welcome.html
    ├── verification.html
    ├── admin_new_reservation.html
    ├── admin_cancellation.html
    ├── admin_new_customer.html
    ├── admin_customer_deletion.html
    ├── confirmation_el.html      # Greek templates
    ├── cancellation_el.html
    ├── reminder_el.html
    ├── welcome_el.html
    ├── verification_el.html
    ├── admin_new_reservation_el.html
    ├── admin_cancellation_el.html
    ├── admin_new_customer_el.html
    └── admin_customer_deletion_el.html
```

## 🚀 Quick Start

### Generate All Templates

```bash
php includes/email_templates/generate_templates.php
```

### View Current Configuration

```bash
php includes/email_templates/update_styles.php --show
```

### Regenerate After Changes

```bash
php includes/email_templates/update_styles.php --regenerate
```

## 🎯 How to Use

### **For Customers**

1. **Language Selection** - Choose preferred language during registration
2. **Automatic Emails** - All future emails sent in chosen language
3. **Consistent Experience** - Language preference remembered for all communications

### **For Admins**

1. **Access Email Management** - Admin Panel → Email Templates
2. **Edit Content** - Modify email text for both languages side-by-side
3. **Regenerate Templates** - Click "Regenerate All Templates" after changes
4. **Monitor Status** - View which templates exist for each language

## 🔧 Technical Implementation

### **Customer Registration**

```php
// Customer selects language during registration
$preferredLanguage = $_POST['preferred_language']; // 'en' or 'el'

// Language stored in database
$customer = $customerHandler->getOrCreateCustomer($name, $email, $mobile, $preferredLanguage);
```

### **Email Sending**

```php
// System automatically detects customer language
$emailSender = new EmailSender();
$emailSender->sendConfirmationEmail($email, $reservationData, $customerData);

// Customer ID used to lookup preferred language from database
// Falls back to session/browser language if customer not found
```

### **Admin Emails**

```php
// Admin emails always use primary language (Greek)
$emailSender->sendAdminNewReservationEmail($adminEmail, $reservationData, $customerData);
// Uses Greek templates regardless of customer language
```

## 🎨 Color Schemes

Each template type has its own color scheme:

| Template Type           | Color              | Usage                      |
| ----------------------- | ------------------ | -------------------------- |
| Confirmation            | `#28a745` (Green)  | Successful bookings        |
| Cancellation            | `#dc3545` (Red)    | Cancelled reservations     |
| Reminder                | `#ffc107` (Yellow) | Appointment reminders      |
| Welcome                 | `#6f42c1` (Purple) | New customer welcome       |
| Verification            | `#667eea` (Blue)   | Email verification         |
| Admin New Reservation   | `#28a745` (Green)  | New booking notifications  |
| Admin Cancellation      | `#dc3545` (Red)    | Cancellation notifications |
| Admin New Customer      | `#6f42c1` (Purple) | New customer notifications |
| Admin Customer Deletion | `#6c757d` (Gray)   | Deletion notifications     |

## ⚙️ Customization

### Modify Base Styles

1. Edit `email_template_creator.php`
2. Update the `$baseStyles` array:

```php
private $baseStyles = [
    'body' => 'margin:0;padding:20px;font-family:Arial,sans-serif;...',
    'container' => 'max-width:500px;margin:0 auto;background:#ffffff;...',
    // ... other styles
];
```

3. Regenerate templates:

```bash
php generate_templates.php
```

### Change Colors

1. Edit `email_template_creator.php`
2. Update the `$colorSchemes` array:

```php
private $colorSchemes = [
    'confirmation' => '#007bff',  // Changed to blue
    'cancellation' => '#dc3545', // Keep red
    // ... other colors
];
```

3. Regenerate templates

### Add New Template Type

1. Add color to `$colorSchemes` in `email_template_creator.php`
2. Create generation method in `generate_templates.php`
3. Add to `generateAllTemplates()` method
4. Run generator

## 🔧 Advanced Usage

### Custom Template Generation

```php
require_once 'email_template_creator.php';

$creator = new EmailTemplateCreator();

// Generate custom content
$content = $creator->generateGreeting('Hello {{name}},') . '
    <p>Custom message here</p>' .
    $creator->generateDetailSection('Details', [
        'Field 1' => '{{value1}}',
        'Field 2' => '{{value2}}'
    ]);

// Generate complete template
$html = $creator->generateTemplate(
    'confirmation',           // Template type (for color)
    'Email Title',           // Page title
    'Header Text',           // Header subtitle
    $content                 // Main content
);

// Save or use the template
file_put_contents('custom_template.html', $html);
```

### Helper Methods

```php
// Generate greeting
$creator->generateGreeting('Hello {{customer_name}},');

// Generate detail section
$creator->generateDetailSection('Booking Details', [
    'Service' => '{{service_name}}',
    'Date' => '{{date}}',
    'Time' => '{{time}}'
]);

// Generate reservation ID display
$creator->generateReservationId('ID: {{reservation_id}}');

// Generate verification code
$creator->generateVerificationCode('{{code}}');

// Generate access code section
$creator->generateAccessCode(
    'Your Access Code',
    'Use this code to login:',
    '{{user_hash}}',
    'Keep this safe'
);
```

## 📱 Email Client Compatibility

The templates are designed to work across all major email clients:

- ✅ Gmail (Web, Mobile, App)
- ✅ Outlook (2016+, Web, Mobile)
- ✅ Apple Mail (macOS, iOS)
- ✅ Yahoo Mail
- ✅ Thunderbird
- ✅ Mobile clients (iOS, Android)

### Compatibility Features

- **Inline CSS** - All styles are inline for maximum compatibility
- **Simple Layout** - Uses basic HTML structure
- **Fallback Fonts** - Arial with sans-serif fallback
- **Responsive Design** - Mobile-friendly media queries
- **Safe Colors** - Web-safe color palette

## 🛠️ Maintenance

### Regular Tasks

1. **Update styles** when design changes
2. **Regenerate templates** after modifications
3. **Test emails** in different clients
4. **Backup templates** before major changes

### Best Practices

- Always test emails before deploying
- Keep styles simple and email-safe
- Use web-safe fonts and colors
- Avoid complex CSS features
- Test on mobile devices

## 🔍 Troubleshooting

### Templates not updating?

```bash
# Regenerate all templates
php generate_templates.php
```

### Styles not applying?

- Check that CSS is inline (not external)
- Verify email client compatibility
- Test with simple styles first

### Colors not showing?

- Use hex colors (#ffffff)
- Avoid CSS variables in emails
- Test in different email clients

## 📞 Support

For issues or questions about the email template system:

1. Check this README first
2. Review the generated templates
3. Test with simple modifications
4. Verify email client compatibility

---

**Note**: This system generates email templates with inline CSS for maximum email client compatibility. External CSS files cannot be used in email templates due to security restrictions in email clients.
