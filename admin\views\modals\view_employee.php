<?php
require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
require_once '../../../includes/admin_functions.php';
require_once '../../../includes/reservation_handler.php';

$employeeId = $_GET['id'] ?? '';

// Debug logging
error_log("View Employee Modal - Employee ID: " . $employeeId);

// Try to get employee with detailed error logging
$employee = getEmployeeById($employeeId);

if (!$employee) {
    error_log("Employee not found for ID: " . $employeeId);

    // Try to debug what employees exist
    $allEmployees = getEmployees();
    error_log("Available employees: " . json_encode(array_keys($allEmployees)));

    echo '<div class="alert alert-error">Employee not found. ID: ' . htmlspecialchars($employeeId) . '</div>';
    echo '<div class="alert alert-info">Available employees: ' . implode(', ', array_keys($allEmployees)) . '</div>';
    exit;
}

$employeeServices = getEmployeeServices($employeeId);
$reservationHandler = new ReservationHandler();
$allReservations = $reservationHandler->getAllReservations();

// Calculate employee statistics
$employeeStats = [
    'total_bookings' => 0,
    'total_revenue' => 0,
    'completed_bookings' => 0,
    'cancelled_bookings' => 0,
    'upcoming_bookings' => 0,
    'last_booking_date' => null
];

foreach ($allReservations as $reservation) {
    if ($reservation['employee_id'] === $employeeId) {
        $employeeStats['total_bookings']++;

        if ($reservation['status'] === 'completed') {
            $employeeStats['completed_bookings']++;
            $employeeStats['total_revenue'] += $reservation['price'];
        } elseif ($reservation['status'] === 'cancelled') {
            $employeeStats['cancelled_bookings']++;
        } elseif ($reservation['status'] === 'confirmed' && $reservation['date'] >= date('Y-m-d')) {
            $employeeStats['upcoming_bookings']++;
        }

        if (!$employeeStats['last_booking_date'] || $reservation['date'] > $employeeStats['last_booking_date']) {
            $employeeStats['last_booking_date'] = $reservation['date'];
        }
    }
}
?>

<div class="employee-details">
    <!-- Employee Information -->
    <div class="employee-info-section">
        <h3><?= htmlspecialchars($employee['name']) ?></h3>
        <div class="employee-basic-info">
            <div class="info-row">
                <strong>ID:</strong> <?= htmlspecialchars($employee['id']) ?>
            </div>
            <?php if (!empty($employee['email'])): ?>
                <div class="info-row">
                    <strong>Email:</strong> <?= htmlspecialchars($employee['email']) ?>
                </div>
            <?php endif; ?>
            <?php if (!empty($employee['phone'])): ?>
                <div class="info-row">
                    <strong>Phone:</strong> <?= htmlspecialchars($employee['phone']) ?>
                </div>
            <?php endif; ?>
            <div class="info-row">
                <strong>Status:</strong>
                <span class="status-badge <?= $employee['status'] ?>">
                    <?= ucfirst($employee['status']) ?>
                </span>
            </div>
            <div class="info-row">
                <strong>Created:</strong> <?= date('M j, Y', strtotime($employee['created_at'])) ?>
            </div>
        </div>
    </div>

    <!-- Services -->
    <div class="employee-services-section">
        <h4>Assigned Services</h4>
        <?php if (!empty($employeeServices)): ?>
            <div class="services-list">
                <?php foreach ($employeeServices as $service): ?>
                    <div class="service-item">
                        <strong><?= htmlspecialchars($service['name']) ?></strong>
                        <span class="service-details">
                            <?= $service['duration'] ?>min - €<?= number_format($service['price'], 2) ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <p class="text-muted">No services assigned</p>
        <?php endif; ?>
    </div>

    <!-- Working Hours -->
    <div class="employee-hours-section">
        <h4>Working Hours</h4>
        <?php if (!empty($employee['working_hours'])): ?>
            <div class="working-hours-display">
                <?php foreach ($employee['working_hours'] as $day => $periods): ?>
                    <div class="day-schedule">
                        <strong><?= $day ?>:</strong>
                        <?php foreach ($periods as $period): ?>
                            <span class="time-period">
                                <?= $period['start'] ?> - <?= $period['end'] ?>
                            </span>
                        <?php endforeach; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <p class="text-muted">No working hours configured</p>
        <?php endif; ?>
    </div>

    <!-- Statistics -->
    <div class="employee-stats-section">
        <h4>Statistics</h4>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number"><?= $employeeStats['total_bookings'] ?></div>
                <div class="stat-label">Total Bookings</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $employeeStats['completed_bookings'] ?></div>
                <div class="stat-label">Completed</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $employeeStats['upcoming_bookings'] ?></div>
                <div class="stat-label">Upcoming</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">€<?= number_format($employeeStats['total_revenue'], 2) ?></div>
                <div class="stat-label">Total Revenue</div>
            </div>
        </div>
        <?php if ($employeeStats['last_booking_date']): ?>
            <div class="info-row">
                <strong>Last Booking:</strong> <?= date('M j, Y', strtotime($employeeStats['last_booking_date'])) ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Quick Actions -->
    <div class="employee-quick-actions">
        <button class="btn btn-primary" onclick="editEmployee('<?= htmlspecialchars($employee['id']) ?>')">
            <i class="fas fa-edit"></i> Edit Employee
        </button>
        <button class="btn btn-success" onclick="openModal('Add Reservation', 'views/modals/add_reservation.php?employee_id=<?= htmlspecialchars($employee['id']) ?>')">
            <i class="fas fa-plus"></i> New Booking
        </button>
        <button class="btn btn-info" onclick="window.print()">
            <i class="fas fa-print"></i> Print Report
        </button>
        <?php if ($employeeStats['total_bookings'] === 0): ?>
            <button class="btn btn-danger" onclick="deleteEmployee('<?= htmlspecialchars($employee['id']) ?>')">
                <i class="fas fa-trash"></i> Delete Employee
            </button>
        <?php endif; ?>
    </div>
</div>

<style>
    .employee-details {
        padding: 20px;
    }

    .employee-info-section,
    .employee-services-section,
    .employee-hours-section,
    .employee-stats-section {
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .employee-basic-info .info-row {
        margin-bottom: 8px;
    }

    .services-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .service-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 4px;
    }

    .service-details {
        color: #666;
        font-size: 0.9em;
    }

    .working-hours-display {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .day-schedule {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .time-period {
        background: #e9ecef;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.9em;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-bottom: 15px;
    }

    .stat-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .stat-number {
        font-size: 1.5em;
        font-weight: bold;
        color: #007bff;
    }

    .stat-label {
        font-size: 0.9em;
        color: #666;
        margin-top: 5px;
    }

    .employee-quick-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        font-weight: bold;
    }

    .status-badge.active {
        background: #d4edda;
        color: #155724;
    }

    .status-badge.inactive {
        background: #f8d7da;
        color: #721c24;
    }
</style>