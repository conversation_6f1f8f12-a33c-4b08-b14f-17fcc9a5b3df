<?php

require_once '../../../includes/tenant_init.php';
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
require_once '../../../includes/customer_handler.php';

$customerId = $_GET['id'] ?? '';
$customerHandler = new CustomerHandler();
$customer = $customerHandler->getCustomerById($customerId);

if (!$customer) {
    echo '<div class="alert alert-error">Customer not found.</div>';
    exit;
}
?>

<form method="post" action="actions/save_customer.php" data-validate>
    <input type="hidden" name="customer_id" value="<?= htmlspecialchars($customerId) ?>">

    <div class="form-row">
        <div class="form-group">
            <label>Name *</label>
            <input type="text" name="customer_name" class="form-control" value="<?= htmlspecialchars($customer['name'] ?? '') ?>" required>
        </div>
        <div class="form-group">
            <label>Email *</label>
            <input type="email" name="customer_email" class="form-control" value="<?= htmlspecialchars($customer['email'] ?? '') ?>" required>
        </div>
    </div>

    <div class="form-row">
        <div class="form-group">
            <label>Mobile</label>
            <input type="text" name="customer_mobile" class="form-control" value="<?= htmlspecialchars($customer['mobile'] ?? '') ?>">
        </div>
        <div class="form-group">
            <label>Date of Birth</label>
            <input type="date" name="customer_dob" class="form-control" value="<?= htmlspecialchars($customer['date_of_birth'] ?? '') ?>">
        </div>
    </div>

    <div class="form-group">
        <label>Address</label>
        <textarea name="customer_address" class="form-control" rows="2"><?= htmlspecialchars($customer['address'] ?? '') ?></textarea>
    </div>

    <div class="form-row">
        <div class="form-group">
            <label>Preferred Contact Method</label>
            <select name="preferred_contact" class="form-control">
                <option value="email" <?= ($customer['preferred_contact'] ?? 'email') === 'email' ? 'selected' : '' ?>>Email</option>
                <option value="mobile" <?= ($customer['preferred_contact'] ?? '') === 'mobile' ? 'selected' : '' ?>>Mobile</option>
            </select>
        </div>
        <div class="form-group">
            <label>Preferred Language</label>
            <select name="preferred_language" class="form-control">
                <option value="el" <?= ($customer['preferred_language'] ?? 'el') === 'el' ? 'selected' : '' ?>>🇬🇷 Ελληνικά (Greek)</option>
                <option value="en" <?= ($customer['preferred_language'] ?? 'el') === 'en' ? 'selected' : '' ?>>🇬🇧 English</option>
            </select>
            <small class="form-text text-muted">Language for email communications</small>
        </div>
    </div>

    <div class="form-group">
        <label>Notes</label>
        <textarea name="customer_notes" class="form-control" rows="3" placeholder="Any special notes about this customer..."><?= htmlspecialchars($customer['notes'] ?? '') ?></textarea>
    </div>

    <button type="submit" class="btn btn-primary" data-original-text="Save">
        <i class="fas fa-save"></i> Save Customer
    </button>
</form>