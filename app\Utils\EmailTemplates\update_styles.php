<?php

/**
 * ========================================
 * EMAIL TEMPLATE STYLE UPDATER
 * ========================================
 * 
 * Quick script to update base styles and regenerate
 * all email templates with new styling.
 * 
 * Usage Examples:
 * 
 * 1. Change font family:
 *    php update_styles.php --font "Georgia, serif"
 * 
 * 2. Change container width:
 *    php update_styles.php --width "600px"
 * 
 * 3. Change header color for specific template:
 *    php update_styles.php --template confirmation --color "#007bff"
 * 
 * 4. Regenerate all templates:
 *    php update_styles.php --regenerate
 * 
 * <AUTHOR> System
 * @version 1.0
 */

require_once __DIR__ . '/email_template_creator.php';
require_once __DIR__ . '/generate_templates.php';

class EmailStyleUpdater
{
    private $creator;
    private $generator;
    
    public function __construct()
    {
        $this->creator = new EmailTemplateCreator();
        $this->generator = new EmailTemplateGenerator();
    }
    
    /**
     * Update base font family
     */
    public function updateFont($fontFamily)
    {
        echo "🎨 Updating font family to: $fontFamily\n";
        
        // This would require modifying the EmailTemplateCreator class
        // For now, we'll show how to do it manually
        echo "📝 To update font family:\n";
        echo "   1. Edit includes/email_templates/email_template_creator.php\n";
        echo "   2. Find 'font-family:Arial,sans-serif' in baseStyles['body']\n";
        echo "   3. Replace with 'font-family:$fontFamily'\n";
        echo "   4. Run: php generate_templates.php\n\n";
    }
    
    /**
     * Update container width
     */
    public function updateWidth($width)
    {
        echo "📐 Updating container width to: $width\n";
        echo "📝 To update container width:\n";
        echo "   1. Edit includes/email_templates/email_template_creator.php\n";
        echo "   2. Find 'max-width:500px' in baseStyles['container']\n";
        echo "   3. Replace with 'max-width:$width'\n";
        echo "   4. Run: php generate_templates.php\n\n";
    }
    
    /**
     * Update template color
     */
    public function updateTemplateColor($template, $color)
    {
        echo "🎨 Updating $template template color to: $color\n";
        echo "📝 To update template color:\n";
        echo "   1. Edit includes/email_templates/email_template_creator.php\n";
        echo "   2. Find '$template' in colorSchemes array\n";
        echo "   3. Replace with '$template' => '$color'\n";
        echo "   4. Run: php generate_templates.php\n\n";
    }
    
    /**
     * Regenerate all templates
     */
    public function regenerateAll()
    {
        echo "🔄 Regenerating all email templates...\n\n";
        $this->generator->generateAllTemplates();
    }
    
    /**
     * Show current style configuration
     */
    public function showCurrentStyles()
    {
        echo "📋 CURRENT EMAIL TEMPLATE CONFIGURATION\n";
        echo "=====================================\n\n";
        
        echo "🎨 COLOR SCHEMES:\n";
        echo "   • Confirmation: #28a745 (Green)\n";
        echo "   • Cancellation: #dc3545 (Red)\n";
        echo "   • Reminder: #ffc107 (Yellow)\n";
        echo "   • Welcome: #6f42c1 (Purple)\n";
        echo "   • Verification: #667eea (Blue)\n";
        echo "   • Admin New Reservation: #28a745 (Green)\n";
        echo "   • Admin Cancellation: #dc3545 (Red)\n";
        echo "   • Admin New Customer: #6f42c1 (Purple)\n";
        echo "   • Admin Customer Deletion: #6c757d (Gray)\n\n";
        
        echo "📐 LAYOUT:\n";
        echo "   • Container Width: 500px\n";
        echo "   • Font Family: Arial, sans-serif\n";
        echo "   • Background: #f5f5f5\n";
        echo "   • Border Radius: 8px\n\n";
        
        echo "📝 TO MODIFY STYLES:\n";
        echo "   1. Edit: includes/email_templates/email_template_creator.php\n";
        echo "   2. Update baseStyles or colorSchemes arrays\n";
        echo "   3. Run: php generate_templates.php\n\n";
    }
    
    /**
     * Show help information
     */
    public function showHelp()
    {
        echo "📧 EMAIL TEMPLATE STYLE UPDATER\n";
        echo "==============================\n\n";
        
        echo "USAGE:\n";
        echo "   php update_styles.php [options]\n\n";
        
        echo "OPTIONS:\n";
        echo "   --help              Show this help message\n";
        echo "   --show              Show current style configuration\n";
        echo "   --regenerate        Regenerate all templates\n";
        echo "   --font <family>     Update font family (shows instructions)\n";
        echo "   --width <size>      Update container width (shows instructions)\n";
        echo "   --template <name>   Template to update (use with --color)\n";
        echo "   --color <hex>       New color for template (use with --template)\n\n";
        
        echo "EXAMPLES:\n";
        echo "   php update_styles.php --show\n";
        echo "   php update_styles.php --regenerate\n";
        echo "   php update_styles.php --font \"Georgia, serif\"\n";
        echo "   php update_styles.php --width \"600px\"\n";
        echo "   php update_styles.php --template confirmation --color \"#007bff\"\n\n";
    }
}

// Command line interface
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $updater = new EmailStyleUpdater();
    $args = $argv ?? [];
    
    if (count($args) < 2) {
        $updater->showHelp();
        exit;
    }
    
    switch ($args[1]) {
        case '--help':
            $updater->showHelp();
            break;
            
        case '--show':
            $updater->showCurrentStyles();
            break;
            
        case '--regenerate':
            $updater->regenerateAll();
            break;
            
        case '--font':
            if (isset($args[2])) {
                $updater->updateFont($args[2]);
            } else {
                echo "❌ Error: Font family required\n";
                echo "Usage: php update_styles.php --font \"Arial, sans-serif\"\n";
            }
            break;
            
        case '--width':
            if (isset($args[2])) {
                $updater->updateWidth($args[2]);
            } else {
                echo "❌ Error: Width value required\n";
                echo "Usage: php update_styles.php --width \"600px\"\n";
            }
            break;
            
        case '--template':
            if (isset($args[2]) && isset($args[3]) && $args[3] === '--color' && isset($args[4])) {
                $updater->updateTemplateColor($args[2], $args[4]);
            } else {
                echo "❌ Error: Template name and color required\n";
                echo "Usage: php update_styles.php --template confirmation --color \"#007bff\"\n";
            }
            break;
            
        default:
            echo "❌ Unknown option: {$args[1]}\n";
            echo "Use --help for usage information\n";
    }
}
