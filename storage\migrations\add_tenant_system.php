<?php

/**
 * Migration: Add Tenant System for Multi-Tenancy
 * 
 * This migration adds tenant_id to all existing tables and creates
 * the tenants table for multi-tenant SaaS functionality.
 */

require_once __DIR__ . '/../Database.php';

function migrate_add_tenant_system($autoConfirm = false)
{
    echo "🚀 Starting tenant system migration...\n";

    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Start transaction
        $conn->exec('BEGIN TRANSACTION');

        // Step 1: Create tenants table
        echo "📋 Creating tenants table...\n";
        $conn->exec("CREATE TABLE IF NOT EXISTS tenants (
            id TEXT PRIMARY KEY,
            business_name TEXT NOT NULL,
            domain TEXT UNIQUE,
            subdomain TEXT UNIQUE,
            plan TEXT NOT NULL DEFAULT 'starter',
            status TEXT NOT NULL DEFAULT 'trial',
            trial_ends_at TEXT,
            billing_email TEXT,
            settings TEXT DEFAULT '{}',
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
        )");

        // Step 2: Check if tenant_id columns already exist
        $tables = ['customers', 'employees', 'reservations', 'services', 'employee_services'];
        $columnsToAdd = [];

        foreach ($tables as $table) {
            echo "🔍 Checking table: $table\n";

            // Check if table exists
            $result = $conn->query("SELECT name FROM sqlite_master WHERE type='table' AND name='$table'");
            if (!$result->fetchArray()) {
                echo "⚠️  Table $table does not exist, skipping...\n";
                continue;
            }

            // Check if tenant_id column exists
            $result = $conn->query("PRAGMA table_info($table)");
            $hasColumn = false;

            while ($column = $result->fetchArray(SQLITE3_ASSOC)) {
                if ($column['name'] === 'tenant_id') {
                    $hasColumn = true;
                    break;
                }
            }

            if (!$hasColumn) {
                $columnsToAdd[] = $table;
            } else {
                echo "✅ Table $table already has tenant_id column\n";
            }
        }

        // Step 3: Add tenant_id columns to tables that need it
        foreach ($columnsToAdd as $table) {
            echo "➕ Adding tenant_id to table: $table\n";
            $conn->exec("ALTER TABLE $table ADD COLUMN tenant_id TEXT NOT NULL DEFAULT 'default'");
        }

        // Step 4: Create default tenant for existing data
        echo "🏢 Creating default tenant...\n";
        $defaultTenantId = 'TN-' . strtoupper(substr(md5(uniqid()), 0, 10));

        $stmt = $conn->prepare("INSERT OR IGNORE INTO tenants 
            (id, business_name, domain, subdomain, plan, status, created_at, updated_at) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)");

        $now = date('Y-m-d H:i:s');
        $stmt->bindValue(1, $defaultTenantId);
        $stmt->bindValue(2, 'Default Business');
        $stmt->bindValue(3, 'localhost');
        $stmt->bindValue(4, 'default');
        $stmt->bindValue(5, 'enterprise');
        $stmt->bindValue(6, 'active');
        $stmt->bindValue(7, $now);
        $stmt->bindValue(8, $now);
        $stmt->execute();

        // Step 5: Update existing records to use default tenant
        foreach ($columnsToAdd as $table) {
            echo "🔄 Updating existing records in $table...\n";
            $conn->exec("UPDATE $table SET tenant_id = '$defaultTenantId' WHERE tenant_id = 'default'");
        }

        // Step 6: Create indexes for performance
        echo "📊 Creating indexes for tenant_id...\n";
        $indexes = [
            "CREATE INDEX IF NOT EXISTS idx_customers_tenant ON customers(tenant_id)",
            "CREATE INDEX IF NOT EXISTS idx_employees_tenant ON employees(tenant_id)",
            "CREATE INDEX IF NOT EXISTS idx_reservations_tenant ON reservations(tenant_id)",
            "CREATE INDEX IF NOT EXISTS idx_services_tenant ON services(tenant_id)",
            "CREATE INDEX IF NOT EXISTS idx_employee_services_tenant ON employee_services(employee_id, service_id)",
            "CREATE INDEX IF NOT EXISTS idx_tenants_domain ON tenants(domain)",
            "CREATE INDEX IF NOT EXISTS idx_tenants_subdomain ON tenants(subdomain)"
        ];

        foreach ($indexes as $index) {
            $conn->exec($index);
        }

        // Step 7: Create tenant configuration table
        echo "⚙️ Creating tenant configuration table...\n";
        $conn->exec("CREATE TABLE IF NOT EXISTS tenant_settings (
            tenant_id TEXT NOT NULL,
            setting_key TEXT NOT NULL,
            setting_value TEXT,
            PRIMARY KEY (tenant_id, setting_key),
            FOREIGN KEY (tenant_id) REFERENCES tenants(id)
        )");

        // Commit transaction
        $conn->exec('COMMIT');

        echo "✅ Tenant system migration completed successfully!\n";
        echo "📋 Default tenant ID: $defaultTenantId\n";
        echo "🎯 Next steps:\n";
        echo "   1. Update application code to use tenant context\n";
        echo "   2. Add subdomain routing\n";
        echo "   3. Create tenant management interface\n";

        return true;
    } catch (Exception $e) {
        $conn->exec('ROLLBACK');
        echo "❌ Migration failed: " . $e->getMessage() . "\n";
        return false;
    }
}

/**
 * Rollback function to remove tenant system
 */
function rollback_tenant_system()
{
    echo "🔄 Rolling back tenant system migration...\n";

    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Start transaction
        $conn->exec('BEGIN TRANSACTION');

        // Remove tenant_id columns (SQLite doesn't support DROP COLUMN directly)
        // We'll need to recreate tables without tenant_id

        $tables = ['customers', 'employees', 'reservations', 'services', 'employee_services'];

        foreach ($tables as $table) {
            echo "🔄 Processing table: $table\n";

            // Get table schema without tenant_id
            $result = $conn->query("PRAGMA table_info($table)");
            $columns = [];

            while ($column = $result->fetchArray(SQLITE3_ASSOC)) {
                if ($column['name'] !== 'tenant_id') {
                    $columns[] = $column['name'];
                }
            }

            if (count($columns) > 0) {
                $columnList = implode(', ', $columns);

                // Create temporary table
                $conn->exec("CREATE TABLE {$table}_temp AS SELECT $columnList FROM $table");

                // Drop original table
                $conn->exec("DROP TABLE $table");

                // Rename temp table
                $conn->exec("ALTER TABLE {$table}_temp RENAME TO $table");
            }
        }

        // Drop tenant-related tables
        $conn->exec("DROP TABLE IF EXISTS tenant_settings");
        $conn->exec("DROP TABLE IF EXISTS tenants");

        // Commit transaction
        $conn->exec('COMMIT');

        echo "✅ Tenant system rollback completed!\n";
        return true;
    } catch (Exception $e) {
        $conn->exec('ROLLBACK');
        echo "❌ Rollback failed: " . $e->getMessage() . "\n";
        return false;
    }
}

// Run migration if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    migrate_add_tenant_system();
}
