<?php

require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/admin_functions.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$serviceId = sanitize_input($data['service_id'] ?? '');

if (!$serviceId) {
    echo json_encode(['success' => false, 'message' => 'Service ID is required']);
    exit;
}

$result = deleteService($serviceId);
echo json_encode($result);
