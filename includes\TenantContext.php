<?php

/**
 * Tenant Context Manager
 *
 * Manages the current tenant context for multi-tenant operations.
 * Ensures all database operations are scoped to the correct tenant.
 */

require_once __DIR__ . '/Database.php';

class TenantContext
{
    private static $currentTenant = null;
    private static $tenantData = null;

    /**
     * Set the current tenant context
     */
    public static function setTenant(string $tenantId): void
    {
        self::$currentTenant = $tenantId;
        self::$tenantData = null; // Reset cached data
    }

    /**
     * Get the current tenant ID
     */
    public static function getTenant(): ?string
    {
        return self::$currentTenant;
    }

    /**
     * Get the current tenant ID (alias for getTenant)
     */
    public static function getCurrentTenant(): ?string
    {
        return self::$currentTenant;
    }

    /**
     * Get tenant data
     */
    public static function getTenantData(): ?array
    {
        if (self::$tenantData === null && self::$currentTenant !== null) {
            self::loadTenantData();
        }
        return self::$tenantData;
    }

    /**
     * Initialize tenant context from request
     */
    public static function initializeFromRequest(): bool
    {
        // Method 1: Subdomain detection
        $tenantId = self::getTenantFromSubdomain();

        // Method 2: Domain detection (fallback)
        if (!$tenantId) {
            $tenantId = self::getTenantFromDomain();
        }

        // Method 3: Session/cookie (for admin panel)
        if (!$tenantId) {
            $tenantId = self::getTenantFromSession();
        }

        // Method 4: Default tenant (development)
        if (!$tenantId) {
            $tenantId = self::getDefaultTenant();
        }

        if ($tenantId) {
            self::setTenant($tenantId);
            return true;
        }

        return false;
    }

    /**
     * Get tenant from subdomain (customer1.bookingspa.gr)
     */
    private static function getTenantFromSubdomain(): ?string
    {
        $host = $_SERVER['HTTP_HOST'] ?? '';

        // Match subdomain pattern
        if (preg_match('/^([^.]+)\.skrtz\.gr$/', $host, $matches)) {
            $subdomain = $matches[1];

            // Skip www and admin subdomains
            if (in_array($subdomain, ['www', 'admin', 'api'])) {
                return null;
            }

            return self::getTenantBySubdomain($subdomain);
        }

        return null;
    }

    /**
     * Get tenant from custom domain
     */
    private static function getTenantFromDomain(): ?string
    {
        $host = $_SERVER['HTTP_HOST'] ?? '';

        // Remove www prefix
        $domain = preg_replace('/^www\./', '', $host);

        return self::getTenantByDomain($domain);
    }

    /**
     * Get tenant from session (for admin panel)
     */
    private static function getTenantFromSession(): ?string
    {
        session_start();
        return $_SESSION['tenant_id'] ?? null;
    }

    /**
     * Get default tenant (for development/localhost)
     */
    private static function getDefaultTenant(): ?string
    {
        $host = $_SERVER['HTTP_HOST'] ?? '';

        // Use default tenant for main domain and localhost
        if (in_array($host, ['localhost', '127.0.0.1', 'localhost:8000', 'skrtz.gr'])) {
            $db = Database::getInstance();
            $conn = $db->getConnection();

            $result = $conn->query("SELECT id FROM tenants WHERE subdomain = 'default' LIMIT 1");
            $row = $result->fetchArray(SQLITE3_ASSOC);

            return $row ? $row['id'] : null;
        }

        return null;
    }

    /**
     * Load tenant data from database
     */
    private static function loadTenantData(): void
    {
        if (!self::$currentTenant) {
            return;
        }

        try {
            $db = Database::getInstance();
            $conn = $db->getConnection();

            $stmt = $conn->prepare("SELECT * FROM tenants WHERE id = :tenant_id LIMIT 1");
            $stmt->bindValue(':tenant_id', self::$currentTenant);
            $result = $stmt->execute();

            $row = $result->fetchArray(SQLITE3_ASSOC);
            if ($row) {
                self::$tenantData = [
                    'id' => $row['id'],
                    'business_name' => $row['business_name'],
                    'domain' => $row['domain'],
                    'subdomain' => $row['subdomain'],
                    'plan' => $row['plan'],
                    'status' => $row['status'],
                    'settings' => json_decode($row['settings'] ?? '{}', true)
                ];
            }
        } catch (Exception $e) {
            error_log('Failed to load tenant data: ' . $e->getMessage());
        }
    }

    /**
     * Get tenant by subdomain
     */
    private static function getTenantBySubdomain(string $subdomain): ?string
    {
        try {
            $db = Database::getInstance();
            $conn = $db->getConnection();

            $stmt = $conn->prepare("SELECT id FROM tenants WHERE subdomain = :subdomain AND status = 'active' LIMIT 1");
            $stmt->bindValue(':subdomain', $subdomain);
            $result = $stmt->execute();

            $row = $result->fetchArray(SQLITE3_ASSOC);
            return $row ? $row['id'] : null;
        } catch (Exception $e) {
            error_log('Failed to get tenant by subdomain: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get tenant by domain
     */
    private static function getTenantByDomain(string $domain): ?string
    {
        try {
            $db = Database::getInstance();
            $conn = $db->getConnection();

            $stmt = $conn->prepare("SELECT id FROM tenants WHERE domain = :domain AND status = 'active' LIMIT 1");
            $stmt->bindValue(':domain', $domain);
            $result = $stmt->execute();

            $row = $result->fetchArray(SQLITE3_ASSOC);
            return $row ? $row['id'] : null;
        } catch (Exception $e) {
            error_log('Failed to get tenant by domain: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if current tenant is valid and active
     */
    public static function isValidTenant(): bool
    {
        $data = self::getTenantData();
        return $data && $data['status'] === 'active';
    }

    /**
     * Get tenant setting
     */
    public static function getSetting(string $key, $default = null)
    {
        $data = self::getTenantData();
        return $data['settings'][$key] ?? $default;
    }

    /**
     * Require tenant context (throw exception if not set)
     */
    public static function requireTenant(): void
    {
        if (!self::$currentTenant) {
            throw new Exception('Tenant context is required but not set');
        }

        if (!self::isValidTenant()) {
            throw new Exception('Invalid or inactive tenant');
        }
    }

    /**
     * Clear tenant context
     */
    public static function clear(): void
    {
        self::$currentTenant = null;
        self::$tenantData = null;
    }

    /**
     * Get tenant-scoped database query helper
     */
    public static function addTenantFilter(string $sql, string $tableAlias = ''): string
    {
        self::requireTenant();

        $tenantColumn = $tableAlias ? "$tableAlias.tenant_id" : "tenant_id";

        // Add WHERE clause if not present
        if (stripos($sql, 'WHERE') === false) {
            $sql .= " WHERE $tenantColumn = :tenant_id";
        } else {
            $sql .= " AND $tenantColumn = :tenant_id";
        }

        return $sql;
    }

    /**
     * Bind tenant parameter to prepared statement
     */
    public static function bindTenantParam($stmt): void
    {
        self::requireTenant();
        $stmt->bindValue(':tenant_id', self::$currentTenant);
    }
}
