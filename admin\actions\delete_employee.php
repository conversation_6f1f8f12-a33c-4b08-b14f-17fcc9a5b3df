<?php

require_once '../../includes/tenant_init.php';
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/admin_functions.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$employeeId = sanitize_input($data['employee_id'] ?? '');

if (!$employeeId) {
    echo json_encode(['success' => false, 'message' => 'Employee ID is required']);
    exit;
}

$result = deleteEmployee($employeeId);
echo json_encode($result);
