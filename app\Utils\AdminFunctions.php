<?php
$settings = require __DIR__ . '/settings.php'; // Use correct path if needed
require_once __DIR__ . '/Database.php';
/**
 * Admin helper functions
 */

// ============================================================================
// EMPLOYEE MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Get all employees from database (tenant-aware)
 */
function getEmployees(): array
{
    $employees = [];
    $db = Database::getInstance();
    $conn = $db->getConnection();

    // Get current tenant ID
    require_once __DIR__ . '/TenantContext.php';
    try {
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("SELECT * FROM employees WHERE tenant_id = :tenant_id ORDER BY name");
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
    } catch (Exception $e) {
        // Fallback for non-tenant context
        $result = $conn->query("SELECT * FROM employees ORDER BY name");
    }

    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $employees[$row['id']] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'email' => $row['email'] ?? '',
            'phone' => $row['phone'] ?? '',
            'working_hours' => json_decode($row['working_hours'] ?? '{}', true),
            'status' => $row['status'] ?? 'active',
            'created_at' => $row['created_at']
        ];
    }

    return $employees;
}

/**
 * Get employee by ID (tenant-aware)
 */
function getEmployeeById(string $employeeId): ?array
{
    $db = Database::getInstance();
    $conn = $db->getConnection();

    // Get current tenant ID
    require_once __DIR__ . '/TenantContext.php';

    error_log("getEmployeeById called with ID: $employeeId");

    try {
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        error_log("getEmployeeById - Using tenant context: $tenantId");

        $stmt = $conn->prepare("SELECT * FROM employees WHERE id = :id AND tenant_id = :tenant_id LIMIT 1");
        $stmt->bindValue(':id', $employeeId);
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();

        $row = $result->fetchArray(SQLITE3_ASSOC);

        if (!$row) {
            error_log("getEmployeeById - No employee found with tenant filter. Trying without tenant filter...");
            // Try without tenant filter as fallback
            $stmt = $conn->prepare("SELECT * FROM employees WHERE id = :id LIMIT 1");
            $stmt->bindValue(':id', $employeeId);
            $result = $stmt->execute();
            $row = $result->fetchArray(SQLITE3_ASSOC);
        }
    } catch (Exception $e) {
        error_log("getEmployeeById - Tenant context error: " . $e->getMessage());
        // Fallback for non-tenant context
        $stmt = $conn->prepare("SELECT * FROM employees WHERE id = :id LIMIT 1");
        $stmt->bindValue(':id', $employeeId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_ASSOC);
    }

    if (!$row) {
        error_log("getEmployeeById - Employee not found: $employeeId");
        return null;
    }

    error_log("getEmployeeById - Found employee: " . $row['name']);

    return [
        'id' => $row['id'],
        'name' => $row['name'],
        'email' => $row['email'] ?? '',
        'phone' => $row['phone'] ?? '',
        'working_hours' => json_decode($row['working_hours'] ?? '{}', true),
        'status' => $row['status'] ?? 'active',
        'created_at' => $row['created_at']
    ];
}

/**
 * Get services assigned to an employee (tenant-aware)
 */
function getEmployeeServices(string $employeeId): array
{
    $db = Database::getInstance();
    $conn = $db->getConnection();

    // Get current tenant ID
    require_once __DIR__ . '/TenantContext.php';
    try {
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("
            SELECT s.* FROM services s
            INNER JOIN employee_services es ON s.id = es.service_id
            WHERE es.employee_id = :employee_id AND s.tenant_id = :tenant_id
            ORDER BY s.name
        ");
        $stmt->bindValue(':employee_id', $employeeId);
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
    } catch (Exception $e) {
        // Fallback for non-tenant context
        $stmt = $conn->prepare("
            SELECT s.* FROM services s
            INNER JOIN employee_services es ON s.id = es.service_id
            WHERE es.employee_id = :employee_id
            ORDER BY s.name
        ");
        $stmt->bindValue(':employee_id', $employeeId);
        $result = $stmt->execute();
    }

    $services = [];
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $services[$row['id']] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'duration' => (int)$row['duration'],
            'price' => (float)$row['price'],
            'description' => $row['description'] ?? ''
        ];
    }

    return $services;
}

/**
 * Get employees who can perform a specific service (tenant-aware)
 */
function getEmployeesForService(string $serviceId): array
{
    $db = Database::getInstance();
    $conn = $db->getConnection();

    // Get current tenant ID
    require_once __DIR__ . '/TenantContext.php';
    try {
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        $stmt = $conn->prepare("
            SELECT e.* FROM employees e
            INNER JOIN employee_services es ON e.id = es.employee_id
            WHERE es.service_id = :service_id AND e.status = 'active' AND e.tenant_id = :tenant_id
            ORDER BY e.name
        ");
        $stmt->bindValue(':service_id', $serviceId);
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();
    } catch (Exception $e) {
        // Fallback for non-tenant context
        $stmt = $conn->prepare("
            SELECT e.* FROM employees e
            INNER JOIN employee_services es ON e.id = es.employee_id
            WHERE es.service_id = :service_id AND e.status = 'active'
            ORDER BY e.name
        ");
        $stmt->bindValue(':service_id', $serviceId);
        $result = $stmt->execute();
    }

    $employees = [];
    while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
        $employees[$row['id']] = [
            'id' => $row['id'],
            'name' => $row['name'],
            'email' => $row['email'] ?? '',
            'phone' => $row['phone'] ?? '',
            'working_hours' => json_decode($row['working_hours'] ?? '{}', true),
            'status' => $row['status'] ?? 'active',
            'created_at' => $row['created_at']
        ];
    }

    return $employees;
}

/**
 * Add a new employee
 */
function addEmployee(string $employeeId, string $employeeName, string $email = '', string $phone = '', array $workingHours = [], array $serviceIds = []): bool
{
    $db = Database::getInstance();
    $conn = $db->getConnection();

    // Get tenant ID first for checking
    require_once __DIR__ . '/TenantContext.php';
    $tenantId = null;
    $useTenant = false;

    try {
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();
        $useTenant = true;

        // Check if employee ID already exists in this tenant
        $stmt = $conn->prepare("SELECT id FROM employees WHERE id = :id AND tenant_id = :tenant_id LIMIT 1");
        $stmt->bindValue(':id', $employeeId);
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();

        if ($result->fetchArray()) {
            return false; // Employee ID already exists in this tenant
        }
    } catch (Exception $e) {
        error_log('Tenant context error in addEmployee ID check: ' . $e->getMessage());
        // Continue without tenant - check for global ID conflicts
        $stmt = $conn->prepare("SELECT id FROM employees WHERE id = :id LIMIT 1");
        $stmt->bindValue(':id', $employeeId);
        $result = $stmt->execute();

        if ($result->fetchArray()) {
            return false; // Employee ID already exists
        }
    }

    try {
        // Begin transaction
        $conn->exec('BEGIN');

        // Insert new employee
        if ($useTenant && $tenantId) {
            $stmt = $conn->prepare("
                INSERT INTO employees (id, name, email, phone, working_hours, status, created_at, tenant_id)
                VALUES (:id, :name, :email, :phone, :working_hours, :status, :created_at, :tenant_id)
            ");
            $stmt->bindValue(':id', $employeeId);
            $stmt->bindValue(':name', $employeeName);
            $stmt->bindValue(':email', $email);
            $stmt->bindValue(':phone', $phone);
            $stmt->bindValue(':working_hours', json_encode($workingHours));
            $stmt->bindValue(':status', 'active');
            $stmt->bindValue(':created_at', date('Y-m-d H:i:s'));
            $stmt->bindValue(':tenant_id', $tenantId);
        } else {
            $stmt = $conn->prepare("
                INSERT INTO employees (id, name, email, phone, working_hours, status, created_at)
                VALUES (:id, :name, :email, :phone, :working_hours, :status, :created_at)
            ");
            $stmt->bindValue(':id', $employeeId);
            $stmt->bindValue(':name', $employeeName);
            $stmt->bindValue(':email', $email);
            $stmt->bindValue(':phone', $phone);
            $stmt->bindValue(':working_hours', json_encode($workingHours));
            $stmt->bindValue(':status', 'active');
            $stmt->bindValue(':created_at', date('Y-m-d H:i:s'));
        }

        if (!$stmt->execute()) {
            $conn->exec('ROLLBACK');
            return false;
        }

        // Assign services to employee
        foreach ($serviceIds as $serviceId) {
            if ($useTenant && $tenantId) {
                $stmt = $conn->prepare("
                    INSERT INTO employee_services (employee_id, service_id, tenant_id)
                    VALUES (:employee_id, :service_id, :tenant_id)
                ");
                $stmt->bindValue(':employee_id', $employeeId);
                $stmt->bindValue(':service_id', $serviceId);
                $stmt->bindValue(':tenant_id', $tenantId);
            } else {
                $stmt = $conn->prepare("
                    INSERT INTO employee_services (employee_id, service_id)
                    VALUES (:employee_id, :service_id)
                ");
                $stmt->bindValue(':employee_id', $employeeId);
                $stmt->bindValue(':service_id', $serviceId);
            }

            if (!$stmt->execute()) {
                $conn->exec('ROLLBACK');
                return false;
            }
        }

        // Commit transaction
        $conn->exec('COMMIT');
        return true;
    } catch (Exception $e) {
        $conn->exec('ROLLBACK');
        error_log('Add employee error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Update an existing employee
 */
function updateEmployee(string $employeeId, string $employeeName, string $email = '', string $phone = '', array $workingHours = [], array $serviceIds = []): bool
{
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Check if employee exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM employees WHERE id = :id");
        $stmt->bindValue(':id', $employeeId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_NUM);

        if (!$row || $row[0] == 0) {
            return false;
        }

        // Begin transaction
        $conn->exec('BEGIN');

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            // Update the employee (with tenant check)
            $stmt = $conn->prepare("UPDATE employees SET name = :name, email = :email, phone = :phone, working_hours = :working_hours WHERE id = :id AND tenant_id = :tenant_id");
            $stmt->bindValue(':name', $employeeName);
            $stmt->bindValue(':email', $email);
            $stmt->bindValue(':phone', $phone);
            $stmt->bindValue(':working_hours', json_encode($workingHours));
            $stmt->bindValue(':id', $employeeId);
            $stmt->bindValue(':tenant_id', $tenantId);
        } catch (Exception $e) {
            // Fallback for non-tenant context
            $stmt = $conn->prepare("UPDATE employees SET name = :name, email = :email, phone = :phone, working_hours = :working_hours WHERE id = :id");
            $stmt->bindValue(':name', $employeeName);
            $stmt->bindValue(':email', $email);
            $stmt->bindValue(':phone', $phone);
            $stmt->bindValue(':working_hours', json_encode($workingHours));
            $stmt->bindValue(':id', $employeeId);
        }

        if (!$stmt->execute()) {
            $conn->exec('ROLLBACK');
            return false;
        }

        // Remove existing service assignments (tenant-aware)
        $stmt = $conn->prepare("DELETE FROM employee_services WHERE employee_id = :employee_id AND tenant_id = :tenant_id");
        $stmt->bindValue(':employee_id', $employeeId);
        $stmt->bindValue(':tenant_id', $tenantId);

        if (!$stmt->execute()) {
            $conn->exec('ROLLBACK');
            return false;
        }

        // Add new service assignments (with tenant_id)
        foreach ($serviceIds as $serviceId) {
            $stmt = $conn->prepare("
                INSERT INTO employee_services (employee_id, service_id, tenant_id)
                VALUES (:employee_id, :service_id, :tenant_id)
            ");
            $stmt->bindValue(':employee_id', $employeeId);
            $stmt->bindValue(':service_id', $serviceId);
            $stmt->bindValue(':tenant_id', $tenantId);

            if (!$stmt->execute()) {
                $conn->exec('ROLLBACK');
                return false;
            }
        }

        // Commit transaction
        $conn->exec('COMMIT');
        return true;
    } catch (Exception $e) {
        $conn->exec('ROLLBACK');
        error_log('Update employee error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Delete an employee (tenant-aware)
 */
function deleteEmployee(string $employeeId): array
{
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            // Check if employee exists and belongs to current tenant
            $stmt = $conn->prepare("SELECT COUNT(*) FROM employees WHERE id = :id AND tenant_id = :tenant_id");
            $stmt->bindValue(':id', $employeeId);
            $stmt->bindValue(':tenant_id', $tenantId);
            $result = $stmt->execute();
        } catch (Exception $e) {
            // Fallback for non-tenant context
            $stmt = $conn->prepare("SELECT COUNT(*) FROM employees WHERE id = :id");
            $stmt->bindValue(':id', $employeeId);
            $result = $stmt->execute();
        }

        $row = $result->fetchArray(SQLITE3_NUM);

        if (!$row || $row[0] == 0) {
            return ['success' => false, 'message' => 'Employee not found.'];
        }

        // Check if employee is assigned to any reservations
        $stmt = $conn->prepare("SELECT COUNT(*) FROM reservations WHERE employee_id = :employee_id");
        $stmt->bindValue(':employee_id', $employeeId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_NUM);

        if ($row && $row[0] > 0) {
            return ['success' => false, 'message' => 'Cannot delete employee. They are assigned to existing reservations.'];
        }

        // Begin transaction
        $conn->exec('BEGIN');

        // Delete employee service assignments (tenant-aware)
        $stmt = $conn->prepare("DELETE FROM employee_services WHERE employee_id = :employee_id AND tenant_id = :tenant_id");
        $stmt->bindValue(':employee_id', $employeeId);
        $stmt->bindValue(':tenant_id', $tenantId);

        if (!$stmt->execute()) {
            $conn->exec('ROLLBACK');
            return ['success' => false, 'message' => 'Failed to delete employee service assignments.'];
        }

        // Delete the employee (tenant-aware)
        $stmt = $conn->prepare("DELETE FROM employees WHERE id = :id AND tenant_id = :tenant_id");
        $stmt->bindValue(':id', $employeeId);
        $stmt->bindValue(':tenant_id', $tenantId);
        $result = $stmt->execute();

        if ($result) {
            $conn->exec('COMMIT');
            return ['success' => true, 'message' => 'Employee deleted successfully.'];
        } else {
            $conn->exec('ROLLBACK');
            return ['success' => false, 'message' => 'Failed to delete employee.'];
        }
    } catch (Exception $e) {
        if (isset($conn)) {
            $conn->exec('ROLLBACK');
        }
        error_log('Delete employee error: ' . $e->getMessage());
        return ['success' => false, 'message' => 'Database error occurred.'];
    }
}

// ============================================================================
// SERVICE MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Add a new service
 */
function addService(string $serviceId, string $serviceName, int $duration, float $price, string $description = '', int $allowEmployeeSelection = 0): bool
{
    $db = Database::getInstance();
    $conn = $db->getConnection();

    // Check if service ID already exists
    $stmt = $conn->prepare("SELECT id FROM services WHERE id = :id LIMIT 1");
    $stmt->bindValue(':id', $serviceId);
    $result = $stmt->execute();

    if ($result->fetchArray()) {
        return false; // Service ID already exists
    }

    // Get current tenant ID
    require_once __DIR__ . '/TenantContext.php';
    try {
        TenantContext::requireTenant();
        $tenantId = TenantContext::getTenant();

        // Insert new service
        $stmt = $conn->prepare("
            INSERT INTO services (id, name, duration, price, description, allow_employee_selection, tenant_id)
            VALUES (:id, :name, :duration, :price, :description, :allow_employee_selection, :tenant_id)
        ");

        $stmt->bindValue(':id', $serviceId);
        $stmt->bindValue(':name', $serviceName);
        $stmt->bindValue(':duration', $duration);
        $stmt->bindValue(':price', $price);
        $stmt->bindValue(':description', $description);
        $stmt->bindValue(':allow_employee_selection', $allowEmployeeSelection);
        $stmt->bindValue(':tenant_id', $tenantId);
    } catch (Exception $e) {
        // Fallback for non-tenant context
        $stmt = $conn->prepare("
            INSERT INTO services (id, name, duration, price, description, allow_employee_selection)
            VALUES (:id, :name, :duration, :price, :description, :allow_employee_selection)
        ");

        $stmt->bindValue(':id', $serviceId);
        $stmt->bindValue(':name', $serviceName);
        $stmt->bindValue(':duration', $duration);
        $stmt->bindValue(':price', $price);
        $stmt->bindValue(':description', $description);
        $stmt->bindValue(':allow_employee_selection', $allowEmployeeSelection);
    }

    return $stmt->execute() !== false;
}

/**
 * Update business hours
 */
function updateBusinessHours(array $businessHours): bool
{
    $settingsFile = __DIR__ . '/settings.php';
    $settings = require $settingsFile;

    $validDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    $newBusinessHours = [];

    foreach ($validDays as $day) {
        $start = sanitize_input($businessHours[$day]['start'] ?? 'closed');
        $end = sanitize_input($businessHours[$day]['end'] ?? 'closed');

        // Validate time format
        if ($start !== 'closed' && !preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $start)) {
            return false;
        }
        if ($end !== 'closed' && !preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $end)) {
            return false;
        }

        $newBusinessHours[$day] = [
            'start' => $start,
            'end' => $end
        ];
    }

    $settings['business_hours'] = $newBusinessHours;

    // Save back to settings.php
    $content = "<?php\nreturn " . var_export($settings, true) . ";\n";
    return file_put_contents($settingsFile, $content) !== false;
}

/**
 * Get recent activity (tenant-aware)
 */
function getRecentActivity(int $limit = 20): array
{
    try {
        // Get current tenant
        $tenantId = TenantContext::getTenant();
        if (!$tenantId) {
            // Fallback to file-based logging if no tenant context
            return getRecentActivityFromFile($limit);
        }

        $db = Database::getInstance();
        $conn = $db->getConnection();

        $stmt = $conn->prepare("SELECT message, created_at FROM activity_logs WHERE tenant_id = :tenant_id ORDER BY created_at DESC LIMIT :limit");
        $stmt->bindValue(':tenant_id', $tenantId);
        $stmt->bindValue(':limit', $limit, SQLITE3_INTEGER);
        $result = $stmt->execute();

        $activities = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $activities[] = [
                'timestamp' => $row['created_at'],
                'message' => $row['message'],
                'type' => determineActivityType($row['message'])
            ];
        }

        return $activities;
    } catch (Exception $e) {
        // Fallback to file-based logging on error
        return getRecentActivityFromFile($limit);
    }
}

/**
 * Get recent activity from log file (fallback)
 */
function getRecentActivityFromFile(int $limit = 20): array
{
    $logFile = DATA_DIR . 'activity.log';
    $activities = [];

    if (!file_exists($logFile)) {
        return [];
    }

    $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if (!$lines) {
        return [];
    }

    // Get last N lines
    $lines = array_slice($lines, -$limit);
    $lines = array_reverse($lines);

    foreach ($lines as $line) {
        if (preg_match('/^\[(.*?)\] (.*)$/', $line, $matches)) {
            $activities[] = [
                'timestamp' => $matches[1],
                'message' => $matches[2],
                'type' => determineActivityType($matches[2])
            ];
        }
    }

    return $activities;
}

/**
 * Determine activity type from message
 */
function determineActivityType(string $message): string
{
    if (strpos($message, 'reservation created') !== false) return 'reservation';
    if (strpos($message, 'reservation cancelled') !== false) return 'cancellation';
    if (strpos($message, 'reservation updated') !== false) return 'update';
    if (strpos($message, 'login') !== false) return 'login';
    if (strpos($message, 'logout') !== false) return 'logout';
    if (strpos($message, 'service') !== false) return 'service';
    if (strpos($message, 'customer') !== false) return 'customer';
    return 'system';
}

/**
 * Get activity icon based on type
 */
function getActivityIcon(string $type): string
{
    switch ($type) {
        case 'reservation':
            return 'calendar-plus';
        case 'cancellation':
            return 'calendar-times';
        case 'update':
            return 'edit';
        case 'login':
            return 'sign-in-alt';
        case 'logout':
            return 'sign-out-alt';
        case 'service':
            return 'concierge-bell';
        case 'customer':
            return 'user';
        case 'system':
            return 'cog';
        default:
            return 'info-circle';
    }
}

/**
 * Perform system health checks
 */
function performSystemChecks(): array
{
    $checks = [];

    // Check data directory permissions
    $checks[] = [
        'name' => 'Data Directory',
        'status' => is_writable(DATA_DIR) ? 'ok' : 'error',
        'message' => is_writable(DATA_DIR) ? 'Writable' : 'Not writable - check permissions'
    ];

    // Check database connection and file
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        if ($conn) {
            $dbExists = file_exists(DB_FILE);
            $dbSize = $dbExists ? filesize(DB_FILE) : 0;
            $checks[] = [
                'name' => 'Database',
                'status' => 'ok',
                'message' => $dbExists ? 'Connected (' . formatBytes($dbSize) . ')' : 'Connected (new database)'
            ];
        } else {
            $checks[] = [
                'name' => 'Database',
                'status' => 'error',
                'message' => 'Connection failed'
            ];
        }
    } catch (Exception $e) {
        $checks[] = [
            'name' => 'Database',
            'status' => 'error',
            'message' => 'Error: ' . $e->getMessage()
        ];
    }

    // Check settings file
    $settingsFile = __DIR__ . '/settings.php';
    $checks[] = [
        'name' => 'Settings File',
        'status' => file_exists($settingsFile) ? 'ok' : 'error',
        'message' => file_exists($settingsFile) ? 'Exists' : 'Missing - check configuration'
    ];

    // Check email templates directory
    $templatesDir = __DIR__ . '/email_templates/templates';
    $checks[] = [
        'name' => 'Email Templates',
        'status' => is_dir($templatesDir) ? 'ok' : 'warning',
        'message' => is_dir($templatesDir) ? 'Available' : 'Directory missing - emails may not work'
    ];

    // Check PHP version
    $phpVersion = PHP_VERSION;
    $checks[] = [
        'name' => 'PHP Version',
        'status' => version_compare($phpVersion, '8.0.0', '>=') ? 'ok' : (version_compare($phpVersion, '7.4.0', '>=') ? 'warning' : 'error'),
        'message' => "PHP $phpVersion" . (version_compare($phpVersion, '8.0.0', '<') ? ' (PHP 8.0+ recommended)' : '')
    ];

    // Check required PHP extensions
    $requiredExtensions = ['sqlite3', 'json', 'mbstring'];
    foreach ($requiredExtensions as $ext) {
        $checks[] = [
            'name' => "PHP Extension: $ext",
            'status' => extension_loaded($ext) ? 'ok' : 'error',
            'message' => extension_loaded($ext) ? 'Loaded' : 'Missing - required for proper operation'
        ];
    }

    // Check mail function
    $checks[] = [
        'name' => 'Email Function',
        'status' => function_exists('mail') ? 'ok' : 'warning',
        'message' => function_exists('mail') ? 'Available' : 'Not available - emails will not be sent'
    ];

    // Check disk space
    $freeBytes = disk_free_space(DATA_DIR);
    $totalBytes = disk_total_space(DATA_DIR);
    if ($freeBytes && $totalBytes) {
        $usedPercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;
        $checks[] = [
            'name' => 'Disk Space',
            'status' => $usedPercent < 90 ? 'ok' : ($usedPercent < 95 ? 'warning' : 'error'),
            'message' => sprintf('%.1f%% used (%.2f MB free)', $usedPercent, $freeBytes / 1024 / 1024)
        ];
    }

    return $checks;
}

/**
 * Format bytes to human readable format
 */
function formatBytes($bytes, $precision = 2): string
{
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * Get reservation statistics for admin dashboard
 */
function getReservationStats(int $days = 30): array
{
    $reservationHandler = new ReservationHandler();
    $startDate = date('Y-m-d', strtotime("-$days days"));
    $endDate = date('Y-m-d');

    $allReservations = [];
    $currentDate = $startDate;

    while ($currentDate <= $endDate) {
        $dayReservations = $reservationHandler->getReservationsByDate($currentDate);
        $allReservations = array_merge($allReservations, $dayReservations);
        $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
    }

    $stats = [
        'total' => count($allReservations),
        'confirmed' => 0,
        'cancelled' => 0,
        'completed' => 0,
        'revenue' => 0,
        'services' => [],
        'daily_average' => 0
    ];

    $services = getServices();
    $today = date('Y-m-d');

    foreach ($allReservations as $reservation) {
        $status = $reservation['status'] ?? 'confirmed';
        $stats[$status]++;

        // Calculate revenue for completed reservations
        if ($status === 'completed' || ($status === 'confirmed' && $reservation['date'] < $today)) {
            $serviceKey = $reservation['service'];
            if (isset($services[$serviceKey])) {
                $stats['revenue'] += (float)$services[$serviceKey]['price'];
            }
        }

        // Track service popularity
        $serviceKey = $reservation['service'];
        if (!isset($stats['services'][$serviceKey])) {
            $stats['services'][$serviceKey] = 0;
        }
        $stats['services'][$serviceKey]++;
    }

    $stats['daily_average'] = round($stats['total'] / $days, 1);

    return $stats;
}

/**
 * Get reservations for admin dashboard
 * 
 * @param string|null $startDate Optional start date filter
 * @param string|null $endDate Optional end date filter
 * @return array
 */
function getAdminReservations(?string $startDate = null, ?string $endDate = null): array
{
    $reservationHandler = new ReservationHandler();

    if ($startDate && $endDate) {
        return $reservationHandler->getReservationsByDateRange($startDate, $endDate);
    } else {
        return $reservationHandler->getAllReservations();
    }
}

/**
 * Get business hours from settings
 */
function getBusinessHours(): array
{
    $settings = require __DIR__ . '/settings.php';
    return $settings['business_hours'] ?? [];
}

/**
 * Get special days from settings
 */
function getSpecialDays(): array
{
    $settings = require __DIR__ . '/settings.php';
    return $settings['special_days'] ?? [];
}

/**
 * Get reservations for a specific date range
 * 
 * @param string|null $startDate Optional start date filter
 * @param string|null $endDate Optional end date filter
 * @return array
 */
function getReservationsForDateRange(?string $startDate = null, ?string $endDate = null): array
{
    $reservationHandler = new ReservationHandler();

    if ($startDate && $endDate) {
        return $reservationHandler->getReservationsByDateRange($startDate, $endDate);
    } else {
        return $reservationHandler->getAllReservations();
    }
}

/**
 * Export data to CSV
 */
function exportToCsv(string $type, string $startDate = null, string $endDate = null): array
{
    $filename = '';
    $data = [];
    $headers = [];

    switch ($type) {
        case 'reservations':
            $filename = 'reservations_' . date('Y-m-d') . '.csv';
            $headers = ['ID', 'Name', 'Mobile', 'Email', 'Service', 'Date', 'Time', 'Duration', 'Price', 'Status', 'Created At'];

            $reservationHandler = new ReservationHandler();
            if ($startDate && $endDate) {
                $reservations = $reservationHandler->getReservationsByDateRange($startDate, $endDate);
            } else {
                $reservations = $reservationHandler->getAllReservations();
            }

            $services = getServices();
            foreach ($reservations as $reservation) {
                $serviceName = isset($services[$reservation['service']]) ?
                    $services[$reservation['service']]['name'] :
                    $reservation['service'];

                $data[] = [
                    $reservation['id'],
                    $reservation['name'],
                    $reservation['mobile'],
                    $reservation['email'],
                    $serviceName,
                    $reservation['date'],
                    $reservation['time'],
                    $reservation['duration'] ?? '',
                    $reservation['price'] ?? '',
                    $reservation['status'] ?? 'confirmed',
                    $reservation['created_at'] ?? ''
                ];
            }
            break;

        case 'services':
            $filename = 'services_' . date('Y-m-d') . '.csv';
            $headers = ['ID', 'Name', 'Duration (minutes)', 'Price'];

            $services = getServices();
            foreach ($services as $service) {
                $data[] = [
                    $service['id'],
                    $service['name'],
                    $service['duration'],
                    $service['price']
                ];
            }
            break;

        case 'business_hours':
            $filename = 'business_hours_' . date('Y-m-d') . '.csv';
            $headers = ['Day', 'Interval', 'Start Time', 'End Time'];

            $businessHours = getBusinessHours();
            foreach ($businessHours as $day => $intervals) {
                if (empty($intervals)) {
                    $data[] = [$day, 1, 'closed', 'closed'];
                } else {
                    foreach ($intervals as $idx => $interval) {
                        $data[] = [
                            $day,
                            $idx + 1,
                            $interval['start'] ?? '',
                            $interval['end'] ?? ''
                        ];
                    }
                }
            }
            break;
        case 'special_days':
            $filename = 'special_days_' . date('Y-m-d') . '.csv';
            $headers = ['Date', 'Interval', 'Start Time', 'End Time'];

            $specialDays = getSpecialDays();
            foreach ($specialDays as $date => $intervals) {
                if (empty($intervals)) {
                    $data[] = [$date, 1, 'closed', 'closed'];
                } else {
                    foreach ($intervals as $idx => $interval) {
                        $data[] = [
                            $date,
                            $idx + 1,
                            $interval['start'] ?? '',
                            $interval['end'] ?? ''
                        ];
                    }
                }
            }
            break;
        default:
            return ['success' => false, 'message' => 'Invalid export type'];
    }

    return [
        'success' => true,
        'filename' => $filename,
        'headers' => $headers,
        'data' => $data
    ];
}

/**
 * Clean old log files
 */
function cleanOldLogs(int $daysToKeep = 30): int
{
    $logFile = DATA_DIR . 'activity.log';
    if (!file_exists($logFile)) {
        return 0;
    }

    $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    if (!$lines) {
        return 0;
    }

    $cutoffDate = date('Y-m-d', strtotime("-$daysToKeep days"));
    $keptLines = [];
    $removedCount = 0;

    foreach ($lines as $line) {
        if (preg_match('/^\[(.*?)\]/', $line, $matches)) {
            $logDate = date('Y-m-d', strtotime($matches[1]));
            if ($logDate >= $cutoffDate) {
                $keptLines[] = $line;
            } else {
                $removedCount++;
            }
        }
    }

    if ($removedCount > 0) {
        file_put_contents($logFile, implode("\n", $keptLines) . "\n");
    }

    return $removedCount;
}

/**
 * Get system information
 */
function getSystemInfo(): array
{
    return [
        'php_version' => PHP_VERSION,
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? '',
        'data_directory' => DATA_DIR,
        'timezone' => date_default_timezone_get(),
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'disk_free_space' => disk_free_space(DATA_DIR),
        'disk_total_space' => disk_total_space(DATA_DIR)
    ];
}

function deleteService(string $serviceId): array
{
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Check if service exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM services WHERE id = :id");
        $stmt->bindValue(':id', $serviceId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_NUM);

        if (!$row || $row[0] == 0) {
            return ['success' => false, 'message' => 'Service not found.'];
        }

        // Check if service is used in any reservations
        $stmt = $conn->prepare("SELECT COUNT(*) FROM reservations WHERE service = :service_id");
        $stmt->bindValue(':service_id', $serviceId);
        $result = $stmt->execute();
        $row = $result->fetchArray(SQLITE3_NUM);

        if ($row && $row[0] > 0) {
            return ['success' => false, 'message' => 'Cannot delete service. It is used in existing reservations.'];
        }

        // Delete the service
        $stmt = $conn->prepare("DELETE FROM services WHERE id = :id");
        $stmt->bindValue(':id', $serviceId);
        $result = $stmt->execute();

        if ($result) {
            return ['success' => true, 'message' => 'Service deleted successfully.'];
        } else {
            return ['success' => false, 'message' => 'Failed to delete service.'];
        }
    } catch (Exception $e) {
        error_log('Delete service error: ' . $e->getMessage());
        return ['success' => false, 'message' => 'Database error occurred.'];
    }
}

// Helper function to save services (if not already present)
function saveServices(array $services): bool
{
    $file = SERVICES_FILE;
    if (($fp = fopen($file, 'w')) === false) return false;
    // Write header if needed
    fputcsv($fp, ['id', 'name', 'duration', 'price']);
    foreach ($services as $id => $svc) {
        fputcsv($fp, [
            $id,
            $svc['name'],
            $svc['duration'],
            $svc['price']
        ]);
    }
    fclose($fp);
    return true;
}

function updateService(string $serviceId, string $serviceName, int $duration, float $price, string $description = '', int $allowEmployeeSelection = 0): bool
{
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // Get current tenant ID
        require_once __DIR__ . '/TenantContext.php';
        try {
            TenantContext::requireTenant();
            $tenantId = TenantContext::getTenant();

            // Check if service exists and belongs to current tenant
            $stmt = $conn->prepare("SELECT COUNT(*) FROM services WHERE id = :id AND tenant_id = :tenant_id");
            $stmt->bindValue(':id', $serviceId);
            $stmt->bindValue(':tenant_id', $tenantId);
            $result = $stmt->execute();
            $row = $result->fetchArray(SQLITE3_NUM);

            if (!$row || $row[0] == 0) {
                return false;
            }

            // Update the service (with tenant check)
            $stmt = $conn->prepare("UPDATE services SET name = :name, duration = :duration, price = :price, description = :description, allow_employee_selection = :allow_employee_selection WHERE id = :id AND tenant_id = :tenant_id");
            $stmt->bindValue(':name', $serviceName);
            $stmt->bindValue(':duration', $duration);
            $stmt->bindValue(':price', $price);
            $stmt->bindValue(':description', $description);
            $stmt->bindValue(':allow_employee_selection', $allowEmployeeSelection);
            $stmt->bindValue(':id', $serviceId);
            $stmt->bindValue(':tenant_id', $tenantId);
        } catch (Exception $e) {
            // Fallback for non-tenant context
            $stmt = $conn->prepare("SELECT COUNT(*) FROM services WHERE id = :id");
            $stmt->bindValue(':id', $serviceId);
            $result = $stmt->execute();
            $row = $result->fetchArray(SQLITE3_NUM);

            if (!$row || $row[0] == 0) {
                return false;
            }

            // Update the service (without tenant check)
            $stmt = $conn->prepare("UPDATE services SET name = :name, duration = :duration, price = :price, description = :description, allow_employee_selection = :allow_employee_selection WHERE id = :id");
            $stmt->bindValue(':name', $serviceName);
            $stmt->bindValue(':duration', $duration);
            $stmt->bindValue(':price', $price);
            $stmt->bindValue(':description', $description);
            $stmt->bindValue(':allow_employee_selection', $allowEmployeeSelection);
            $stmt->bindValue(':id', $serviceId);
        }

        return $stmt->execute() !== false;
    } catch (Exception $e) {
        error_log('Update service error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Delete a customer and their reservations
 */
function deleteCustomer(string $customerId, bool $forceDelete = false): array
{
    try {
        require_once __DIR__ . '/functions.php';
        require_once __DIR__ . '/customer_handler.php';
        require_once __DIR__ . '/reservation_handler.php';

        $customerHandler = new CustomerHandler();
        $reservationHandler = new ReservationHandler();

        // Get customer details before deletion
        $customer = $customerHandler->getCustomerById($customerId);
        if (!$customer) {
            return ['success' => false, 'message' => 'Customer not found'];
        }

        // Check if customer has future reservations
        $customerReservations = $reservationHandler->getReservationsForCustomer($customerId);
        $hasFutureReservations = false;
        $futureReservationCount = 0;
        $cancelledReservations = [];
        $today = date('Y-m-d');

        foreach ($customerReservations as $reservation) {
            if ($reservation['date'] >= $today && $reservation['status'] === 'confirmed') {
                $hasFutureReservations = true;
                $futureReservationCount++;
                $cancelledReservations[] = $reservation; // Store for admin notification
            }
        }

        if ($hasFutureReservations && !$forceDelete) {
            return [
                'success' => false,
                'message' => "Customer has $futureReservationCount upcoming reservation(s). Use force delete to remove customer and cancel all reservations.",
                'has_future_reservations' => true
            ];
        }

        // If force delete, cancel all future reservations first
        if ($forceDelete && $hasFutureReservations) {
            foreach ($customerReservations as $reservation) {
                if ($reservation['date'] >= $today && $reservation['status'] === 'confirmed') {
                    $reservationHandler->cancelReservation($reservation['id'], $customerId);
                }
            }
        }

        // Delete the customer
        $result = $customerHandler->deleteCustomer($customerId);

        if ($result['success']) {
            // Send admin notification about customer deletion
            try {
                $adminNotificationSent = notify_admin_customer_deletion($customer, $cancelledReservations, $forceDelete);
                if ($adminNotificationSent) {
                    log_activity("Admin notification sent successfully for customer deletion: {$customer['name']} ({$customer['email']})");
                } else {
                    log_activity("Failed to send admin notification for customer deletion: {$customer['name']} ({$customer['email']})");
                }
            } catch (Exception $e) {
                log_activity("Admin notification error for customer deletion {$customer['email']}: " . $e->getMessage());
            }

            if ($forceDelete && $hasFutureReservations) {
                $result['message'] .= " ($futureReservationCount upcoming reservations were cancelled)";
            }
        }

        return $result;
    } catch (Exception $e) {
        log_activity("Delete customer error: " . $e->getMessage());
        return ['success' => false, 'message' => 'An error occurred while deleting the customer'];
    }
}
