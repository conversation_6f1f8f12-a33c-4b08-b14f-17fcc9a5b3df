<?php
require_once '../includes/tenant_init.php';
require_once '../includes/text_manager.php';
require_once '../includes/unified_text_manager.php';

// Get data for both customer page texts and email templates using unified manager
$categories = TextManager::getCustomerTextCategories();
$allTexts = UnifiedTextManager::getCustomerTexts();
$emailTexts = UnifiedTextManager::getEmailTexts();

// Determine active tab and filter
$activeTab = $_GET['tab'] ?? 'customer';
$activeFilter = $_GET['filter'] ?? 'general';

// Use the actual 10 categories from TextManager for customer filters
$customerFilters = [];
foreach ($categories as $key => $category) {
    $customerFilters[$key] = [
        'title' => $category['title'],
        'icon' => getCategoryIcon($key)
    ];
}

// Define email filters based on simplified email structure
$emailFilters = [
    'confirmation' => ['title' => 'Confirmation Emails', 'icon' => 'fas fa-check-circle'],
    'cancellation' => ['title' => 'Cancellation Emails', 'icon' => 'fas fa-times-circle'],
    'reminder' => ['title' => 'Reminder Emails', 'icon' => 'fas fa-bell'],
    'welcome' => ['title' => 'Welcome Emails', 'icon' => 'fas fa-hand-wave'],
    'verification' => ['title' => 'Verification Emails', 'icon' => 'fas fa-shield-check'],
    'admin' => ['title' => 'Admin Notifications', 'icon' => 'fas fa-user-shield']
];

// Helper function to get category icons
function getCategoryIcon($key)
{
    $icons = [
        'general' => 'fas fa-home',
        'email_verification' => 'fas fa-envelope-check',
        'customer_flow' => 'fas fa-user-flow',
        'registration' => 'fas fa-user-plus',
        'booking' => 'fas fa-calendar-plus',
        'reservations' => 'fas fa-calendar-check',
        'cancellation' => 'fas fa-times-circle',
        'messages' => 'fas fa-comments',
        'buttons' => 'fas fa-mouse-pointer',
        'javascript' => 'fas fa-code'
    ];
    return $icons[$key] ?? 'fas fa-cog';
}
?>

<div class="text-management-container">
    <div class="page-header">
        <h1><i class="fas fa-edit"></i> Text Management</h1>
        <p>Manage all customer page texts and email templates</p>

        <div class="header-actions">
            <button type="button" class="btn btn-warning btn-sm" onclick="resetCurrentFilter()">
                <i class="fas fa-undo"></i> Reset Current Filter
            </button>
            <button type="button" class="btn btn-success" onclick="saveCurrentFilter()">
                <i class="fas fa-save"></i> Save Current Filter
            </button>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation">
        <button class="tab-btn <?= $activeTab === 'customer' ? 'active' : '' ?>"
            onclick="switchTab('customer')">
            <i class="fas fa-globe"></i> Customer Page
        </button>
        <button class="tab-btn <?= $activeTab === 'email' ? 'active' : '' ?>"
            onclick="switchTab('email')">
            <i class="fas fa-envelope"></i> Customer Emails
        </button>
    </div>

    <!-- Filter Navigation -->
    <div class="filter-navigation">
        <?php if ($activeTab === 'customer'): ?>
            <?php foreach ($customerFilters as $filterKey => $filter): ?>
                <button class="filter-btn <?= $activeFilter === $filterKey ? 'active' : '' ?>"
                    onclick="switchFilter('<?= $filterKey ?>')">
                    <i class="<?= $filter['icon'] ?>"></i> <?= $filter['title'] ?>
                </button>
            <?php endforeach; ?>
        <?php else: ?>
            <?php foreach ($emailFilters as $filterKey => $filter): ?>
                <button class="filter-btn <?= $activeFilter === $filterKey ? 'active' : '' ?>"
                    onclick="switchFilter('<?= $filterKey ?>')">
                    <i class="<?= $filter['icon'] ?>"></i> <?= $filter['title'] ?>
                </button>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Main Content Area -->
    <div class="text-content-area">
        <form id="text-form" class="text-form">
            <input type="hidden" name="tab" value="<?= htmlspecialchars($activeTab) ?>">
            <input type="hidden" name="filter" value="<?= htmlspecialchars($activeFilter) ?>">

            <!-- Div Table Structure -->
            <div class="text-table">
                <!-- Table Header -->
                <div class="text-table-header">
                    <div class="text-col-name">Name</div>
                    <div class="text-col-greek">Greek</div>
                    <div class="text-col-english">English</div>
                    <div class="text-col-reset">Reset</div>
                </div>


                <!-- Table Body -->
                <div class="text-table-body">
                    <?php if ($activeTab === 'customer'): ?>
                        <?php
                        // Get texts for current filter from the actual categories
                        $currentTexts = [];
                        if (isset($categories[$activeFilter])) {
                            foreach ($categories[$activeFilter]['keys'] as $key) {
                                $textData = $allTexts[$key] ?? ['primary' => '', 'secondary' => ''];

                                // Handle old format (string) and new format (array)
                                if (is_string($textData)) {
                                    $textData = ['primary' => $textData, 'secondary' => ''];
                                }

                                $currentTexts[$key] = [
                                    'greek' => $textData['primary'] ?? '',
                                    'english' => $textData['secondary'] ?? ''
                                ];
                            }
                        }
                        ?>
                        <?php foreach ($currentTexts as $key => $text): ?>
                            <div class="text-table-row" data-key="<?= htmlspecialchars($key) ?>">
                                <div class="text-col-name">
                                    <span class="text-key"><?= htmlspecialchars($key) ?></span>
                                </div>
                                <div class="text-col-greek">
                                    <textarea name="texts[<?= htmlspecialchars($key) ?>][primary]"
                                        class="text-input"
                                        placeholder="Greek text..."><?= htmlspecialchars($text['greek']) ?></textarea>
                                </div>
                                <div class="text-col-english">
                                    <textarea name="texts[<?= htmlspecialchars($key) ?>][secondary]"
                                        class="text-input"
                                        placeholder="English text..."><?= htmlspecialchars($text['english']) ?></textarea>
                                </div>
                                <div class="text-col-reset">
                                    <button type="button" class="btn-reset" onclick="resetText('<?= htmlspecialchars($key) ?>')">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <?php
                        // Get email texts for current filter
                        $currentEmailTexts = [];

                        // Get both English and Greek email texts
                        $englishTexts = $emailTexts['en'] ?? [];
                        $greekTexts = $emailTexts['el'] ?? [];

                        // Filter based on active filter
                        foreach ($englishTexts as $key => $englishText) {
                            $includeText = false;

                            switch ($activeFilter) {
                                case 'confirmation':
                                    $includeText = $key === 'confirmation_html';
                                    break;
                                case 'cancellation':
                                    $includeText = $key === 'cancellation_html';
                                    break;
                                case 'reminder':
                                    $includeText = $key === 'reminder_html';
                                    break;
                                case 'welcome':
                                    $includeText = $key === 'welcome_html';
                                    break;
                                case 'verification':
                                    $includeText = $key === 'verification_html';
                                    break;
                                case 'admin':
                                    $includeText = strpos($key, 'admin_') === 0;
                                    break;
                                default:
                                    $includeText = true; // Show all if no specific filter
                            }

                            if ($includeText) {
                                $currentEmailTexts[$key] = [
                                    'greek' => $greekTexts[$key] ?? '',
                                    'english' => $englishText
                                ];
                            }
                        }
                        ?>
                        <?php foreach ($currentEmailTexts as $key => $text): ?>
                            <?php if (strpos($key, '_html') !== false): ?>
                                <!-- HTML Email Editor - Side by Side -->
                                <div class="html-email-editor" data-key="<?= htmlspecialchars($key) ?>">
                                    <h3><?= htmlspecialchars(ucwords(str_replace(['_html', '_'], [' Email', ' '], $key))) ?></h3>

                                    <div class="html-editor-dual">
                                        <!-- Greek Side -->
                                        <div class="html-editor-side">
                                            <h4>🇬🇷 Greek Version</h4>
                                            <textarea name="email_texts[<?= htmlspecialchars($key) ?>][el]"
                                                id="html-<?= htmlspecialchars($key) ?>-el"
                                                class="html-textarea"
                                                rows="15"
                                                onkeyup="updatePreview('<?= htmlspecialchars($key) ?>', 'el')"
                                                placeholder="Greek HTML content..."><?= htmlspecialchars($text['greek']) ?></textarea>
                                            <div class="preview-container">
                                                <h5>Preview:</h5>
                                                <div id="preview-<?= htmlspecialchars($key) ?>-el" class="email-preview"></div>
                                            </div>
                                        </div>

                                        <!-- English Side -->
                                        <div class="html-editor-side">
                                            <h4>🇺🇸 English Version</h4>
                                            <textarea name="email_texts[<?= htmlspecialchars($key) ?>][en]"
                                                id="html-<?= htmlspecialchars($key) ?>-en"
                                                class="html-textarea"
                                                rows="15"
                                                onkeyup="updatePreview('<?= htmlspecialchars($key) ?>', 'en')"
                                                placeholder="English HTML content..."><?= htmlspecialchars($text['english']) ?></textarea>
                                            <div class="preview-container">
                                                <h5>Preview:</h5>
                                                <div id="preview-<?= htmlspecialchars($key) ?>-en" class="email-preview"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="html-editor-actions">
                                        <button type="button" class="btn btn-warning btn-sm" onclick="resetSpecificEmailType('<?= htmlspecialchars(str_replace('_html', '', $key)) ?>')">
                                            <i class="fas fa-undo"></i> Reset This Email Type to Default
                                        </button>
                                        <button type="button" class="btn btn-success btn-sm" onclick="saveCurrentEmailType('<?= htmlspecialchars($key) ?>')" style="margin-left: 10px;">
                                            <i class="fas fa-save"></i> Save This Email Type
                                        </button>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- Regular Text Editor -->
                                <div class="text-table-row" data-key="<?= htmlspecialchars($key) ?>">
                                    <div class="text-col-name">
                                        <span class="text-key"><?= htmlspecialchars($key) ?></span>
                                    </div>
                                    <div class="text-col-greek">
                                        <textarea name="email_texts[<?= htmlspecialchars($key) ?>][el]"
                                            class="text-input"
                                            placeholder="Greek email text..."><?= htmlspecialchars($text['greek']) ?></textarea>
                                    </div>
                                    <div class="text-col-english">
                                        <textarea name="email_texts[<?= htmlspecialchars($key) ?>][en]"
                                            class="text-input"
                                            placeholder="English email text..."><?= htmlspecialchars($text['english']) ?></textarea>
                                    </div>
                                    <div class="text-col-reset">
                                        <button type="button" class="btn-reset" onclick="resetEmailText('<?= htmlspecialchars($key) ?>')">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    // Text management JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Form submission
        document.getElementById('text-form').addEventListener('submit', function(e) {
            e.preventDefault();
            saveCurrentFilter();
        });



        // Auto-resize all textareas
        document.querySelectorAll('.text-input').forEach(textarea => {
            autoResizeTextarea(textarea);

            // Add event listener for future changes
            textarea.addEventListener('input', function() {
                autoResizeTextarea(this);
            });
        });

        // Add keyboard shortcut for save (Ctrl+S)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                saveCurrentFilter();
            }
        });
    });

    // Tab switching
    function switchTab(tab) {
        window.location.href = `?action=text_management&tab=${tab}&filter=general`;
    }

    // Filter switching
    function switchFilter(filter) {
        const currentTab = new URLSearchParams(window.location.search).get('tab') || 'customer';
        window.location.href = `?action=text_management&tab=${currentTab}&filter=${filter}`;
    }



    // Save current filter texts
    function saveCurrentFilter() {
        const formData = new FormData(document.getElementById('text-form'));

        // Show loading state
        const saveBtn = document.querySelector('.btn-success');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        saveBtn.disabled = true;

        fetch('actions/save_texts.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Current filter texts saved successfully!', 'success');
                } else {
                    showNotification('Error saving texts: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('Network error: ' + error.message, 'error');
            })
            .finally(() => {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            });
    }

    // Reset current filter texts (without saving)
    function resetCurrentFilter() {
        if (confirm('Reset all texts in current filter to default values? You will need to save manually.')) {
            const currentTab = new URLSearchParams(window.location.search).get('tab') || 'customer';
            const currentFilter = new URLSearchParams(window.location.search).get('filter') || 'general';

            // Get all visible text rows
            const rows = document.querySelectorAll('.text-table-row');
            let resetCount = 0;

            rows.forEach(row => {
                const key = row.getAttribute('data-key');
                if (key) {
                    // Reset this field
                    if (currentTab === 'customer') {
                        resetTextFieldOnly(key, 'customer');
                    } else {
                        resetTextFieldOnly(key, 'email');
                    }
                    resetCount++;
                }
            });

            if (resetCount > 0) {
                showNotification(`${resetCount} texts reset to defaults. Click "Save Current Filter" to save changes.`, 'success');
            } else {
                showNotification('No texts found to reset.', 'error');
            }
        }
    }

    // Reset individual text
    function resetText(key) {
        if (confirm(`Reset "${key}" to default value?`)) {
            const row = document.querySelector(`[data-key="${key}"]`);

            // Try different selector patterns for customer texts
            let greekTextarea = row.querySelector('[name*="[primary]"]') ||
                row.querySelector('[name*="[greek]"]');
            let englishTextarea = row.querySelector('[name*="[secondary]"]') ||
                row.querySelector('[name*="[english]"]');

            if (greekTextarea && englishTextarea) {
                // Fetch default values from server
                fetch('actions/get_default_texts.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            key: key,
                            type: 'customer'
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Set default values
                            greekTextarea.value = data.texts.primary || '';
                            englishTextarea.value = data.texts.secondary || '';

                            // Auto-resize if needed
                            autoResizeTextarea(greekTextarea);
                            autoResizeTextarea(englishTextarea);

                            showNotification(`"${key}" reset to default values. Click "Save Current Filter" to save.`, 'success');
                        } else {
                            showNotification(`Error: ${data.message}`, 'error');
                        }
                    })
                    .catch(error => {
                        showNotification(`Network error: ${error.message}`, 'error');
                    });
            } else {
                showNotification(`Error: Could not find text fields for "${key}"`, 'error');
            }
        }
    }

    // Reset text field only (helper function for bulk reset)
    function resetTextFieldOnly(key, type) {
        const row = document.querySelector(`[data-key="${key}"]`);
        if (!row) return;

        // Fetch default values from server
        fetch('actions/get_default_texts.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    key: key,
                    type: type
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (type === 'customer') {
                        let greekTextarea = row.querySelector('[name*="[primary]"]') ||
                            row.querySelector('[name*="[greek]"]');
                        let englishTextarea = row.querySelector('[name*="[secondary]"]') ||
                            row.querySelector('[name*="[english]"]');

                        if (greekTextarea && englishTextarea) {
                            greekTextarea.value = data.texts.primary || '';
                            englishTextarea.value = data.texts.secondary || '';
                            autoResizeTextarea(greekTextarea);
                            autoResizeTextarea(englishTextarea);
                        }
                    } else {
                        let greekTextarea = row.querySelector('[name*="[el]"]') ||
                            row.querySelector('[name*="[greek]"]');
                        let englishTextarea = row.querySelector('[name*="[en]"]') ||
                            row.querySelector('[name*="[english]"]');

                        if (greekTextarea && englishTextarea) {
                            greekTextarea.value = data.texts.greek || '';
                            englishTextarea.value = data.texts.english || '';
                            autoResizeTextarea(greekTextarea);
                            autoResizeTextarea(englishTextarea);
                        }
                    }
                }
            })
            .catch(error => {
                console.error(`Error resetting ${key}:`, error);
            });
    }

    // Reset individual email text
    function resetEmailText(key) {
        if (confirm(`Reset email "${key}" to default value?`)) {
            const row = document.querySelector(`[data-key="${key}"]`);

            // Try different selector patterns for email texts
            let greekTextarea = row.querySelector('[name*="[el]"]') ||
                row.querySelector('[name*="[greek]"]');
            let englishTextarea = row.querySelector('[name*="[en]"]') ||
                row.querySelector('[name*="[english]"]');

            if (greekTextarea && englishTextarea) {
                // Fetch default values from server
                fetch('actions/get_default_texts.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            key: key,
                            type: 'email'
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Set default values
                            greekTextarea.value = data.texts.greek || '';
                            englishTextarea.value = data.texts.english || '';

                            // Auto-resize if needed
                            autoResizeTextarea(greekTextarea);
                            autoResizeTextarea(englishTextarea);

                            showNotification(`Email "${key}" reset to default values. Click "Save Current Filter" to save.`, 'success');
                        } else {
                            showNotification(`Error: ${data.message}`, 'error');
                        }
                    })
                    .catch(error => {
                        showNotification(`Network error: ${error.message}`, 'error');
                    });
            } else {
                showNotification(`Error: Could not find email text fields for "${key}"`, 'error');
            }
        }
    }

    // Helper function to auto-resize textarea
    function autoResizeTextarea(textarea) {
        if (textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.max(60, textarea.scrollHeight) + 'px';
        }
    }

    // Show notification
    function showNotification(message, type) {
        // Remove existing notifications
        const existing = document.querySelectorAll('.notification');
        existing.forEach(n => n.remove());

        // Create new notification
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        ${message}
    `;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Auto-resize textareas
    document.addEventListener('input', function(e) {
        if (e.target.classList.contains('text-input')) {
            e.target.style.height = 'auto';
            e.target.style.height = Math.max(60, e.target.scrollHeight) + 'px';
        }
    });

    // Get email type colors for preview
    function getEmailTypeColors(emailType) {
        const colorSchemes = {
            'confirmation': {
                primary: '#28a745',
                secondary: '#1e7e34'
            },
            'cancellation': {
                primary: '#dc3545',
                secondary: '#c82333'
            },
            'reminder': {
                primary: '#ffc107',
                secondary: '#e0a800'
            },
            'welcome': {
                primary: '#007bff',
                secondary: '#0056b3'
            },
            'verification': {
                primary: '#6f42c1',
                secondary: '#5a32a3'
            }
        };
        return colorSchemes[emailType] || colorSchemes['welcome'];
    }

    // HTML Editor Functions - Load actual email template
    function updatePreview(key, language) {
        const textarea = document.getElementById(`html-${key}-${language}`);
        const preview = document.getElementById(`preview-${key}-${language}`);

        if (textarea && preview) {
            // Extract email type from key (remove _html suffix)
            const emailType = key.replace('_html', '');

            // Load the actual generated email template
            loadEmailTemplate(emailType, language, textarea.value, preview);
        }
    }

    // Load actual email template with its styling
    function loadEmailTemplate(emailType, language, content, previewElement) {
        // Determine template filename
        const templateFile = language === 'el' ? `${emailType}_el.html` : `${emailType}.html`;

        // Replace placeholders with sample data for preview
        content = content.replace(/\{\{site_name\}\}/g, 'Realma.gr');
        content = content.replace(/\{\{customer_name\}\}/g, 'John Doe');
        content = content.replace(/\{\{reservation_id\}\}/g, 'RS-SAMPLE001');
        content = content.replace(/\{\{service_name\}\}/g, 'Sample Service');
        content = content.replace(/\{\{date\}\}/g, 'Monday, June 30');
        content = content.replace(/\{\{time\}\}/g, '14:30 - 15:15');
        content = content.replace(/\{\{service_price\}\}/g, '50');
        content = content.replace(/\{\{verification_code\}\}/g, '123456');
        content = content.replace(/\{\{access_code\}\}/g, 'ABC123XYZ');
        content = content.replace(/\{\{customer_name\}\}/g, 'John Doe');
        content = content.replace(/\{\{year\}\}/g, new Date().getFullYear());
        // Legacy support
        content = content.replace(/\{\{code\}\}/g, '123456');
        content = content.replace(/\{\{user_hash\}\}/g, 'ABC123XYZ');
        content = content.replace(/\{\{access_hash\}\}/g, 'ABC123XYZ');
        content = content.replace(/\{\{year\}\}/g, new Date().getFullYear());

        // Load the template file (correct path from admin directory)
        fetch(`../includes/email_templates/templates/${templateFile}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Template not found');
                }
                return response.text();
            })
            .then(templateHtml => {
                // Replace the body content with the current textarea content
                let previewHtml = templateHtml.replace(
                    /<body[^>]*>[\s\S]*<\/body>/i,
                    `<body>${content}</body>`
                );

                // Replace placeholders with sample data for preview
                previewHtml = previewHtml.replace(/\{\{site_name\}\}/g, 'Realma.gr');
                previewHtml = previewHtml.replace(/\{\{customer_name\}\}/g, 'John Doe');
                previewHtml = previewHtml.replace(/\{\{reservation_id\}\}/g, 'RS-SAMPLE001');
                previewHtml = previewHtml.replace(/\{\{service_name\}\}/g, 'Sample Service');
                previewHtml = previewHtml.replace(/\{\{date\}\}/g, 'Monday, June 30');
                previewHtml = previewHtml.replace(/\{\{time\}\}/g, '14:30 - 15:15');
                previewHtml = previewHtml.replace(/\{\{service_price\}\}/g, '50');
                previewHtml = previewHtml.replace(/\{\{verification_code\}\}/g, '123456');
                previewHtml = previewHtml.replace(/\{\{access_code\}\}/g, 'ABC123XYZ');
                previewHtml = previewHtml.replace(/\{\{year\}\}/g, new Date().getFullYear());
                // Legacy support
                previewHtml = previewHtml.replace(/\{\{code\}\}/g, '123456');
                previewHtml = previewHtml.replace(/\{\{user_hash\}\}/g, 'ABC123XYZ');
                previewHtml = previewHtml.replace(/\{\{access_hash\}\}/g, 'ABC123XYZ');

                // Scale down the preview for better fit in admin panel
                previewHtml = previewHtml.replace(
                    '<style>',
                    '<style>.email-container { max-width: 100% !important; transform: scale(0.8); transform-origin: top left; }'
                );

                previewElement.innerHTML = previewHtml;
            })
            .catch(error => {
                console.error('Error loading template:', error);
                // Fallback to simple content display
                previewElement.innerHTML = `<div style="padding: 20px; border: 1px solid #ddd; border-radius: 4px; background: #f8f9fa;">
                    <p><strong>Preview not available</strong></p>
                    <p>Template file: ${templateFile}</p>
                    <div style="margin-top: 15px; padding: 10px; background: white; border-radius: 4px;">${content}</div>
                </div>`;
            });
    }

    // Reset specific email type to default
    function resetSpecificEmailType(emailType) {
        if (confirm(`Reset ${emailType} email to default content? This will overwrite any changes.`)) {
            fetch('actions/reset_email_type.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email_type: emailType
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification(`${emailType} email reset to default successfully!`, 'success');
                        // Reload the page to show updated content
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotification('Error resetting email: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showNotification('Network error: ' + error.message, 'error');
                });
        }
    }

    // Save specific email type
    function saveCurrentEmailType(emailKey) {
        const emailType = emailKey.replace('_html', '');

        if (confirm(`Save ${emailType} email changes?`)) {
            // Get the form data for this specific email type
            const formData = new FormData();

            // Add Greek content
            const greekTextarea = document.getElementById(`html-${emailKey}-el`);
            if (greekTextarea) {
                formData.append(`email_texts[${emailKey}][el]`, greekTextarea.value);
            }

            // Add English content
            const englishTextarea = document.getElementById(`html-${emailKey}-en`);
            if (englishTextarea) {
                formData.append(`email_texts[${emailKey}][en]`, englishTextarea.value);
            }

            fetch('actions/save_texts.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification(`${emailType} email saved successfully!`, 'success');
                    } else {
                        showNotification('Error saving email: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    showNotification('Network error: ' + error.message, 'error');
                });
        }
    }

    // Initialize previews on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Update all HTML previews
        document.querySelectorAll('.html-textarea').forEach(textarea => {
            const matches = textarea.id.match(/html-(.+)-(el|en)/);
            if (matches) {
                updatePreview(matches[1], matches[2]);
            }
        });
    });
</script>

<style>
    /* HTML Email Editor Styles - Side by Side */
    .html-email-editor {
        margin-bottom: 40px;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        background: #f9f9f9;
    }

    .html-email-editor h3 {
        margin: 0 0 20px 0;
        color: #333;
        font-size: 20px;
        text-align: center;
    }

    .html-editor-dual {
        display: flex;
        gap: 20px;
        min-height: 600px;
    }

    .html-editor-side {
        flex: 1;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        background: white;
    }

    .html-editor-side h4 {
        margin: 0 0 15px 0;
        color: #333;
        font-size: 16px;
        text-align: center;
        padding: 10px;
        background: #e9ecef;
        border-radius: 4px;
    }

    .html-editor-side h5 {
        margin: 15px 0 10px 0;
        color: #666;
        font-size: 14px;
    }

    .html-textarea {
        width: 100%;
        font-family: 'Courier New', monospace;
        font-size: 11px;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        resize: vertical;
        box-sizing: border-box;
    }

    .preview-container {
        margin-top: 15px;
    }

    .email-preview {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        max-height: 300px;
        overflow-y: auto;
        background: #fff;
        font-size: 12px;
    }

    .html-editor-actions {
        margin-top: 20px;
        text-align: center;
    }

    /* Responsive */
    @media (max-width: 1200px) {
        .html-editor-dual {
            flex-direction: column;
        }

        .html-editor-side {
            margin-bottom: 20px;
        }
    }
</style>