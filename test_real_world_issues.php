<?php

/**
 * Real World Issues Test
 * This script tests all the reported issues to identify root causes
 */

require_once 'includes/config.php';
require_once 'includes/tenant_init.php';

echo "<h1>Real World Issues Test</h1>";

// Test 1: Employee Modal Issues
echo "<h2>1. Employee Modal Issues</h2>";
try {
    require_once 'includes/admin_functions.php';

    // Get all employees
    $employees = getEmployees();
    echo "✅ Total employees found: " . count($employees) . "<br>";

    if (!empty($employees)) {
        $firstEmployeeId = array_keys($employees)[0];
        echo "✅ First employee ID: " . $firstEmployeeId . "<br>";

        // Test getEmployeeById
        $employee = getEmployeeById($firstEmployeeId);
        if ($employee) {
            echo "✅ getEmployeeById works for ID: " . $firstEmployeeId . "<br>";
            echo "✅ Employee name: " . $employee['name'] . "<br>";
        } else {
            echo "❌ getEmployeeById failed for ID: " . $firstEmployeeId . "<br>";
        }

        // Test employee services
        $services = getEmployeeServices($firstEmployeeId);
        echo "✅ Employee services count: " . count($services) . "<br>";
    } else {
        echo "❌ No employees found<br>";
    }
} catch (Exception $e) {
    echo "❌ Employee test error: " . $e->getMessage() . "<br>";
}

// Test 2: Settings Issues
echo "<h2>2. Settings Issues</h2>";
try {
    echo "🔍 Loading TenantSettingsManager...<br>";
    require_once 'includes/TenantSettingsManager.php';
    echo "✅ TenantSettingsManager loaded<br>";

    echo "🔍 Getting settings...<br>";
    // Test getting settings - need to include functions.php first
    require_once 'includes/functions.php';
    $settings = getSettings();
    echo "✅ Settings loaded: " . json_encode($settings) . "<br>";

    echo "🔍 Testing setting save...<br>";
    // Test setting a value
    $testKey = 'test_setting_' . time();
    $testValue = 'test_value_' . time();
    $saveResult = TenantSettingsManager::setSetting($testKey, $testValue);
    echo "✅ Test setting save result: " . ($saveResult ? 'SUCCESS' : 'FAILED') . "<br>";

    echo "🔍 Testing setting read...<br>";
    // Test reading the value back
    $readValue = TenantSettingsManager::getSetting($testKey, 'default');
    echo "✅ Test setting read back: " . $readValue . "<br>";
    echo "✅ Values match: " . ($readValue === $testValue ? 'YES' : 'NO') . "<br>";

    echo "🔍 Testing site name...<br>";
    // Test site name specifically
    $siteName = TenantSettingsManager::getSetting('site_name', 'Default');
    echo "✅ Current site name: " . $siteName . "<br>";
} catch (Exception $e) {
    echo "❌ Settings test error: " . $e->getMessage() . "<br>";
    echo "❌ Error file: " . $e->getFile() . " line " . $e->getLine() . "<br>";
    echo "❌ Stack trace: <pre>" . $e->getTraceAsString() . "</pre><br>";
} catch (Error $e) {
    echo "❌ Fatal error in settings: " . $e->getMessage() . "<br>";
    echo "❌ Error file: " . $e->getFile() . " line " . $e->getLine() . "<br>";
}

// Test 3: Tenant Context Issues
echo "<h2>3. Tenant Context Issues</h2>";
try {
    require_once 'includes/TenantContext.php';

    $tenantId = TenantContext::getTenant();
    echo "✅ Current tenant ID: " . $tenantId . "<br>";

    $tenantData = TenantContext::getTenantData();
    echo "✅ Tenant data: " . json_encode($tenantData) . "<br>";

    $isValid = TenantContext::isValidTenant();
    echo "✅ Is valid tenant: " . ($isValid ? 'YES' : 'NO') . "<br>";
} catch (Exception $e) {
    echo "❌ Tenant context error: " . $e->getMessage() . "<br>";
}

// Test 4: Database Issues
echo "<h2>4. Database Issues</h2>";
try {
    require_once 'includes/Database.php';

    $db = Database::getInstance();
    $conn = $db->getConnection();

    // Test basic query
    $result = $conn->query("SELECT COUNT(*) as count FROM employees");
    $row = $result->fetchArray(SQLITE3_ASSOC);
    echo "✅ Total employees in DB: " . $row['count'] . "<br>";

    // Test tenant_settings table
    $result = $conn->query("SELECT COUNT(*) as count FROM tenant_settings");
    $row = $result->fetchArray(SQLITE3_ASSOC);
    echo "✅ Total tenant settings in DB: " . $row['count'] . "<br>";

    // Test specific tenant settings
    $tenantId = TenantContext::getTenant();
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tenant_settings WHERE tenant_id = :tenant_id");
    $stmt->bindValue(':tenant_id', $tenantId);
    $result = $stmt->execute();
    $row = $result->fetchArray(SQLITE3_ASSOC);
    echo "✅ Settings for current tenant: " . $row['count'] . "<br>";
} catch (Exception $e) {
    echo "❌ Database test error: " . $e->getMessage() . "<br>";
}

// Test 5: Modal URL Testing
echo "<h2>5. Modal URL Testing</h2>";
if (!empty($employees)) {
    $firstEmployeeId = array_keys($employees)[0];

    $viewUrl = "admin/views/modals/view_employee.php?id=" . $firstEmployeeId;
    $editUrl = "admin/views/modals/edit_employee.php?id=" . $firstEmployeeId;

    echo "✅ View employee URL: <a href='$viewUrl' target='_blank'>$viewUrl</a><br>";
    echo "✅ Edit employee URL: <a href='$editUrl' target='_blank'>$editUrl</a><br>";

    // Test if modal files exist (without query parameters)
    echo "✅ View modal file exists: " . (file_exists('admin/views/modals/view_employee.php') ? 'YES' : 'NO') . "<br>";
    echo "✅ Edit modal file exists: " . (file_exists('admin/views/modals/edit_employee.php') ? 'YES' : 'NO') . "<br>";
}

// Test 6: Settings Modal Testing
echo "<h2>6. Settings Modal Testing</h2>";
$settingsModalUrl = "admin/views/modals/settings.php";
echo "✅ Settings modal URL: <a href='$settingsModalUrl' target='_blank'>$settingsModalUrl</a><br>";
echo "✅ Settings modal exists: " . (file_exists($settingsModalUrl) ? 'YES' : 'NO') . "<br>";

$saveSettingsUrl = "admin/actions/save_settings.php";
echo "✅ Save settings action exists: " . (file_exists($saveSettingsUrl) ? 'YES' : 'NO') . "<br>";

echo "<h2>Test Complete</h2>";
echo "<p>Check the results above to identify specific issues. Click the modal URLs to test them directly.</p>";
